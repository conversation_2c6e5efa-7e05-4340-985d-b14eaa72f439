# Task Completion Checklist

When completing any coding task, follow these steps:

## 1. Code Quality Checks
```bash
# Backend (from apps/backend)
pnpm lint               # Fix linting issues
pnpm typecheck          # Ensure TypeScript compiles
pnpm test               # Run tests if any exist
pnpm tidy               # Format + lint

# Frontend (from apps/frontend)  
pnpm lint:fix           # Fix linting issues
pnpm typecheck          # Ensure TypeScript compiles
pnpm tidy               # Format + lint

# Mobile (from apps/mobile)
pnpm lint               # Fix linting issues
pnpm typecheck          # Ensure TypeScript compiles
```

## 2. Verification Steps
- [ ] Code follows established patterns (DDD for backend, component patterns for frontend)
- [ ] No TypeScript errors (`pnpm typecheck`)
- [ ] ESLint passes (`pnpm lint`)
- [ ] Prettier formatting applied (`pnpm format` or `pnpm tidy`)
- [ ] Descriptive variable/function names used
- [ ] Error handling implemented where needed
- [ ] No console.log statements left in production code

## 3. Testing (if applicable)
- [ ] Unit tests written for new business logic
- [ ] Existing tests still pass
- [ ] Edge cases considered

## 4. Documentation
- [ ] Complex functions have docstrings
- [ ] API endpoints documented with Swagger decorators
- [ ] README updated if adding new features

## 5. Security Checks
- [ ] No hardcoded secrets or API keys
- [ ] Input validation on all user inputs
- [ ] Proper authentication/authorization checks
- [ ] SQL injection prevention (use TypeORM query builder)

## 6. Performance Considerations
- [ ] Database queries optimized (use relations, avoid N+1)
- [ ] Large lists paginated
- [ ] Unnecessary re-renders avoided (React.memo, useMemo)
- [ ] Images/files properly compressed

## Common Issues to Check
- Tour composite key: Always use originalNumber + deliveryDate
- Keycloak roles: Use @Roles decorator, not @UseGuards
- TypeORM: Use repository pattern through services
- React: Avoid direct DOM manipulation
- Mobile: Test offline functionality