# Code Style and Conventions

## General Principles
- **TypeScript**: Strict mode disabled, but use explicit types when possible
- **Formatting**: Prettier with single quotes and trailing commas
- **Linting**: ESLint with auto-fix enabled
- **Comments**: Minimal - only comment what code cannot express
- **Documentation**: Use docstrings for highly reusable functions
- **Variable naming**: Always use descriptive names
- **Error handling**: Explicit error handling with proper logging

## Backend Conventions
### Controller Pattern
```typescript
@ApiTags('manager/resource-name')
@Controller('manager/resource-name')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class ResourceManagerController {
  // Use @Roles from nest-keycloak-connect, NOT @UseGuards
  // Always include ApiTags and Swagger documentation
  // Use ParseUUIDPipe to validate UUIDs
  // Return entities directly (ClassSerializerInterceptor handles serialization)
}
```

### Repository Pattern
- Extend TypeORM Repository directly
- No custom methods in repository - use TypeORM methods in services
- Repository only handles data access, business logic in services
- Inject repositories through infrastructure services

### Service Pattern
- Business logic in application services
- Use TypeORM transactions for data consistency
- Validate DTOs with class-validator
- Map between entities and DTOs

### Naming Conventions
- Database: snake_case (e.g., tour_assignment)
- TypeScript: camelCase for variables/functions, PascalCase for classes
- File names: kebab-case (e.g., tour.service.ts)
- API endpoints: kebab-case (e.g., /manager/tour-assignments)

## Frontend/Mobile Conventions
### React Components
- Functional components with hooks
- Use TypeScript interfaces for props
- Extract reusable logic to custom hooks
- Keep components focused and small

### State Management
- Redux Toolkit for client state
- TanStack Query for server state
- Immer for immutable updates
- Avoid prop drilling

### Styling
- TailwindCSS utility classes
- Component variants with class-variance-authority
- Responsive design with mobile-first approach

## Testing Conventions
- Jest for unit tests
- Test files alongside source (.spec.ts)
- Focus on business logic testing
- Mock external dependencies
- Use descriptive test names

## Import Order
1. External packages
2. Internal modules by layer (domain → application → infrastructure)
3. Relative imports
4. Types/interfaces last