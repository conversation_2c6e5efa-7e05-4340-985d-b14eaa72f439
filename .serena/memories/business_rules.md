# Business Rules and Domain Logic

## Tour Management
### Tour Identification
- **Composite Key**: Tours are uniquely identified by `originalNumber + deliveryDate`
- Same tour number can exist for multiple dates
- Tours imported daily from XML files (Tour_<number>_DateLivr_<YYYYMMDD>.xml)
- Tour types: `normal` (e.g., Tour_4012) and `frozen` (e.g., Tour_4025TK)

### Tour Status Transitions
- `planned` → `in_progress` → `completed`
- Automatic transition when first stop is started
- Complete when all stops are delivered

### Tour Assignment
- Tours assigned to operators (drivers) with date ranges
- Support for multi-tours (one driver, multiple tours)
- 20 fixed tours + 5 temporary licenses

## Stop Completion Rules
### Proof Requirements
1. **Normal Delivery**: Signature optional, but either signature OR photo required
2. **Secure Location**: Photo mandatory, no signature needed
3. **At least one proof**: Must have signature OR photo (unless secure location)

### Incident Handling
- Three incident families: Traffic, Client Refusal, Payment Default
- Completion types when incident reported:
  - `FULL`: Delivered despite incident
  - `PARTIAL`: Partial delivery
  - `NONE`: No delivery possible
- Mandatory questions based on incident type

### Equipment Return (Reprise d'agrès)
- Track returned logistics equipment:
  - Pallets (palettes)
  - Rolls
  - Packages (colis)
- All equipment returns optional
- Receptionist validates with potential discrepancy alerts

## Delivery Flow
1. **Preparation**: Check-list validation (Complete/Partial/Absent)
2. **Loading**: Last delivered = first loaded (reverse order)
3. **Delivery**: Free choice of stop order
4. **Validation**: Signature/photo + equipment return
5. **Completion**: Auto-generate delivery receipt PDF

## File Management
- All files stored in Amazon S3
- Signatures/photos as base64 in API
- Automatic PDF generation for receipts
- Offline support with local storage + sync

## User Roles
- **MANAGER**: Full access, planning, supervision, reports
- **OPERATOR**: Mobile app access, tour execution
- **RECEPTIONIST**: Equipment validation (future)

## Data Import
- Daily XML import from FTP
- Creates tours, stops, clients, delivery notes
- Handles tour reassignments
- Processes shipment lines with operations