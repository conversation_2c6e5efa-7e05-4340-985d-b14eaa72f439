# Project Structure and Navigation

## Root Structure
```
lrg-bl/                     # Monorepo root
├── apps/                   # Application packages
│   ├── backend/           # NestJS API server
│   ├── frontend/          # React web dashboard
│   └── mobile/            # Ionic mobile app
├── services/              # Infrastructure services
│   └── keycloak/         # Auth service config
├── patches/               # Package patches
├── docs/                  # Documentation
├── .github/workflows/     # CI/CD pipelines
└── CLAUDE.md             # AI assistant instructions
```

## Backend Structure (Domain-Driven Design)
```
apps/backend/src/
├── domain/               # Core business logic
│   ├── entity/          # TypeORM entities
│   ├── enum/            # Business enums
│   └── value-object/    # Value objects
├── application/          # Use cases & services
│   ├── dto/             # Data transfer objects
│   ├── service/         # Business services
│   ├── mapper/          # Entity-DTO mappers
│   └── use-case/        # Complex operations
├── infrastructure/       # External interfaces
│   ├── controller/      # REST endpoints
│   ├── repository/      # Data access
│   ├── service/         # External services (S3, Keycloak)
│   └── command/         # CLI commands
└── main.ts              # Application entry
```

## Frontend Structure
```
apps/frontend/src/
├── components/          # Reusable UI components
├── features/           # Feature-based modules
├── hooks/              # Custom React hooks
├── lib/                # Utilities & helpers
├── pages/              # Route components
├── services/           # API client services
├── store/              # Redux store & slices
└── styles/             # Global styles
```

## Mobile Structure
```
apps/mobile/src/
├── components/         # Ionic/React components
├── contexts/          # React contexts
├── hooks/             # Custom hooks
├── pages/             # Route pages
├── services/          # API & device services
├── store/             # Redux store
├── utils/             # Helpers
└── App.tsx           # Root component
```

## Key Files to Know
- `CLAUDE.md`: Development commands and guidelines
- `bdd.dbml`: Database schema definition
- `package.json` (root): Monorepo scripts
- `.env.example` files: Environment variables
- `docker-compose.yml`: Local services setup

## Navigation Tips
- Use `apps/backend` for API development
- Use `apps/frontend` for manager dashboard
- Use `apps/mobile` for driver/operator app
- Check `REAMDE.md` for project status and TODO list
- Run `pnpm dev` from root to start everything