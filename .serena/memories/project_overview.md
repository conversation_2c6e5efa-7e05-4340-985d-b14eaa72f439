# LRG Monorepo Project Overview

## Project Purpose
LRG (Logistique et Routage Grande Distribution) is a delivery tour management system designed to modernize daily delivery operations. The system manages tour assignments, delivery tracking, incident handling, and logistics equipment (agrès) return.

## Tech Stack
- **Monorepo**: pnpm workspaces with 3 apps
- **Backend**: NestJS 11 + TypeORM + PostgreSQL 15 + Amazon S3
- **Frontend Web**: React 19 + Vite + Redux Toolkit + TanStack Query + TailwindCSS 4 + Radix UI
- **Mobile Apps**: Ionic 8 + React 19 + Capacitor (iOS/Android) + Redux Toolkit
- **Authentication**: Keycloak with JWT tokens and role-based access (MANAGER, OPERATOR)
- **Infrastructure**: Docker, GitHub Actions CI/CD

## Architecture
- **Backend**: Domain-Driven Design with 3 layers:
  - `domain/`: Entities, enums, core business logic
  - `application/`: DTOs, services, use cases, mappers  
  - `infrastructure/`: Controllers, repositories, external services
- **Frontend/Mobile**: Component-based React with hooks, Redux for state management
- **Database**: PostgreSQL with TypeORM, snake_case naming in DB, camelCase in code

## Key Business Entities
- **Users**: Keycloak-managed with roles (MANAGER, OPERATOR)
- **Tours**: Daily delivery routes with composite key (originalNumber + deliveryDate)
- **Stops**: Delivery points within tours
- **Clients**: Recipients with addresses and delivery codes
- **Deliveries**: Delivery notes (BL) with shipment lines
- **Files**: S3-stored signatures, photos, PDFs

## Current Status (67% MVP Complete)
- ✅ Backend API complete with authentication, tours, stops, clients, files
- ✅ Mobile app with offline-first architecture and stop completion
- ✅ XML tour import and assignment system
- ❌ Web dashboard manager views not implemented
- ❌ WebSocket real-time updates missing
- ❌ Limited test coverage