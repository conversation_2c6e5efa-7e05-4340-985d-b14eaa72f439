# Suggested Development Commands

## Quick Start
```bash
pnpm dev                # Start all apps concurrently (backend:3000, frontend:8000, mobile:8001)
pnpm backend:dev        # Backend only with watch mode
pnpm frontend:dev       # Frontend only
pnpm mobile:dev         # Mobile only
```

## Backend Commands
```bash
cd apps/backend
pnpm start:dev          # Development with watch mode
pnpm test               # Run Jest unit tests
pnpm test:watch         # Test watch mode for TDD
pnpm test <path>        # Run specific tests
pnpm lint               # ESLint with auto-fix
pnpm typecheck          # TypeScript validation
pnpm format             # Prettier formatting
pnpm tidy               # Format + lint fix
pnpm build              # Production build

# CLI Commands (from backend directory)
pnpm command-entrypoint-dev seed-roles                    # Seed Keycloak roles
pnpm command-entrypoint-dev import:xml-tours-from-local-files  # Import XML tours
pnpm command-entrypoint-dev assign-tour                    # Assign tour to user
```

## Frontend Commands
```bash
cd apps/frontend
pnpm dev                # Vite dev server
pnpm build              # Production build
pnpm lint               # ESLint
pnpm lint:fix           # ESLint with auto-fix
pnpm typecheck          # TypeScript validation
pnpm tidy               # Format + lint fix
```

## Mobile Commands
```bash
cd apps/mobile
pnpm dev                # Vite dev server
pnpm build              # Production build
pnpm lint               # ESLint with auto-fix
pnpm typecheck          # TypeScript validation
pnpm sync               # Sync web assets to native platforms
pnpm open:ios           # Open in Xcode
```

## Monorepo Commands (from root)
```bash
pnpm tidy               # Format + lint all apps
pnpm lint               # Lint all apps
pnpm backend:build      # Build backend
pnpm frontend:build     # Build frontend
```

## Git Commands (macOS)
```bash
git status              # Check current status
git diff                # View changes
git add .               # Stage all changes
git commit -m "message" # Commit changes
git push                # Push to remote
git pull                # Pull from remote
git log --oneline -10   # View recent commits
```

## macOS Utilities
```bash
ls -la                  # List files (with colors on macOS)
find . -name "*.ts"     # Find files by pattern
grep -r "pattern" .     # Search in files (with colors)
sed -i '' 's/old/new/g' # Replace text (macOS sed syntax)
open .                  # Open folder in Finder
```