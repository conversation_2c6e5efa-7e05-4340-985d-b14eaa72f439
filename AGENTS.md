# AGENTS.md

Monorepo managed with pnpm. Use workspace-scoped scripts. Always run lint/typecheck/build before commits.

Build/Lint/Test
- Root: pnpm dev | pnpm lint | pnpm tidy
- Backend (apps/backend): pnpm lint | pnpm typecheck | pnpm build | pnpm test | pnpm test <path> | pnpm test --testNamePattern "name" | pnpm test:watch
- Frontend (apps/frontend): pnpm lint | pnpm lint:fix | pnpm typecheck | pnpm build
- Mobile (apps/mobile): pnpm lint | pnpm typecheck | pnpm build

Single Test Examples
- Backend file: cd apps/backend && pnpm test src/.../file.spec.ts
- Backend test name: cd apps/backend && pnpm test --testNamePattern "should ..."

Code Style
- Formatting: Prettier; run pnpm tidy (backend/fe/mobile) before commit
- Imports: Backend absolute from src/*; Frontend/Mobile relative imports
- Types: Explicit types, avoid any; prefer interfaces/DTOs; strict patterns in Cursor rules
- Naming: camelCase code, PascalCase classes/components, snake_case DB, kebab-case files
- Errors: Throw NestJS exceptions; add context when catching; no silent fails

Cursor/Copilot Rules
- .cursor/rules: backend-architecture, backend-solid, backend-pagination, react-dev, frontend-component-patterns, mobile-architecture, mobile-hooks-patterns, mobile-ionic-patterns, mobile-typescript-patterns
- No .github/copilot-instructions.md found

Backend Practices
- Repositories extend TypeORM Repository without business logic; controllers use @Roles and return entities
- Use DTO validation (class-validator), pagination DTOs/adapters per Cursor rules

Frontend/Mobile Practices
- React 19; use react-router (not react-router-dom in this project note); Redux Toolkit + TanStack Query; follow component/dialog separation patterns

Before PR/Commit
- For each changed app: pnpm lint && pnpm typecheck && pnpm build; run tests where present