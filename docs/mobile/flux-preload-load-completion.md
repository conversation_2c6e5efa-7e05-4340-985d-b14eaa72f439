# Flux Preload, Load et Completion des Arrêts - Documentation Mobile

## Vue d'ensemble

Cette documentation décrit le flux complet de gestion des arrêts (stops) dans l'application mobile, depuis le préchargement jusqu'à la livraison finale. Le système utilise une architecture optimiste avec un système de queue d'événements pour gérer les opérations hors ligne.

## Architecture du Système

### 1. Système de Queue d'Événements (`eventQueueSlice`)

Le cœur du système est une queue d'événements qui permet :
- **Persistance locale** : Les événements sont stockés dans IndexedDB via `indexedDBPreferencesStorage`
- **Synchronisation asynchrone** : Les événements sont traités quand la connexion est disponible
- **Gestion d'état optimiste** : L'UI est mise à jour immédiatement, même hors ligne

#### Types d'événements supportés :
```typescript
type StopPreloadEvent = {
  eventType: 'stop-preload';
  payload: IPreloadStopDto & { stopId: string };
};

type StopLoadEvent = {
  eventType: 'stop-load';
  payload: ILoadStopDto & { stopId: string };
};

type StopCompletionEvent = {
  eventType: 'stop-completion';
  payload: ICompleteStopDeliveryDto & { stopId: string };
};
```

#### États des événements :
- `pending` : En attente de traitement
- `syncing` : En cours de synchronisation avec le backend
- `completed` : Synchronisé avec succès
- `failed` : Échec de synchronisation (avec message d'erreur)

### 2. Projection des Tours (`tourSlice`)

Le `tourSlice` utilise un pattern d'Event Sourcing pour projeter l'état des tours :

```typescript
export const getToursWithQueueProjection = createSelector(
  // Combine les tours du backend avec les événements locaux
  (state) => state.tour.tours,
  (state) => state.eventQueue.queue,
  (state) => state.tour.dataResolvedAt,
  (tours, queue, dataResolvedAt) => {
    // Applique les événements de la queue sur les tours
    // pour obtenir l'état le plus récent
  }
);
```

Cette projection permet :
- D'afficher instantanément les modifications locales
- De conserver la cohérence entre les données serveur et locales
- De gérer les conflits temporels (événements créés après la dernière sync)

## Flux Métier Détaillé

### 1. Préchargement (Preload)

**Objectif** : Préparer l'équipement logistique en avance (veille ou matin)

**Flux technique** :
1. L'utilisateur sélectionne l'équipement via l'UI
2. Création d'un événement `stop-preload` dans la queue
3. Mise à jour optimiste de l'état local via la projection
4. Synchronisation asynchrone vers `/api/deliver/stops/{stopId}/preload`

**Impact métier** :
- Le statut de la tournée reste "Planifiée"
- Permet la préparation flexible du matériel
- Les données sont visibles immédiatement dans l'UI

### 2. Chargement (Load)

**Objectif** : Confirmer le chargement effectif et démarrer la tournée

**Flux technique** :
1. L'utilisateur confirme le chargement définitif
2. Création d'un événement `stop-load` dans la queue
3. Mise à jour optimiste incluant le changement de statut
4. Synchronisation vers `/api/deliver/stops/{stopId}/load`

**Impact métier** :
- La tournée passe en statut "En cours"
- Déclenche le début opérationnel de la tournée
- Verrouille certaines modifications

### 3. Completion (Livraison)

**Objectif** : Finaliser la livraison avec preuves et détails

**Flux technique** :
1. L'utilisateur complète le formulaire de livraison
2. Création d'un événement `stop-completion` avec toutes les données
3. Mise à jour du statut de livraison dans la projection
4. Synchronisation vers `/api/deliver/stops/{stopId}/complete`

**Impact métier** :
- Enregistre les preuves (signature, photo)
- Gère les incidents éventuels
- Permet le retour d'équipement

## Implémentation pour les Développeurs

### Ajouter un événement à la queue

```typescript
// Dans un composant ou hook
import { useAppDispatch } from '../stores/hooks';
import { addEventToQueue } from '../stores/eventQueueSlice';

const dispatch = useAppDispatch();

// Préchargement
dispatch(addEventToQueue({
  eventType: 'stop-preload',
  payload: {
    stopId: 'uuid-du-stop',
    equipmentCount: { pallets: 5, rolls: 2 },
    equipmentDetails: [...]
  }
}));
```

### Récupérer l'état d'un stop avec projection

```typescript
import { useAppSelector } from '../stores/hooks';
import { makeGetStopByIdSelector } from '../stores/tourSlice';

// Créer le sélecteur (à faire une fois)
const getStopById = makeGetStopByIdSelector();

// Dans le composant
const stop = useAppSelector(state => getStopById(state, stopId));
// stop contient l'état le plus récent incluant les événements locaux
```

### Gérer la synchronisation

```typescript
import { processEvent } from '../stores/eventQueueSlice';

// Le système tente automatiquement la synchronisation
// Pour forcer un retry sur un événement failed :
dispatch(retryFailedEvent(eventId));

// Pour nettoyer les événements complétés :
dispatch(clearCompletedEvents());
```

### Structure des DTOs

```typescript
interface ILoadStopDto {
  equipmentCount?: IEquipmentCountDto;
  equipmentDetails?: ILogisticsEquipmentDetailDto[];
}

interface IEquipmentCountDto {
  pallets?: number;
  rolls?: number;
  packages?: number;
}

interface ILogisticsEquipmentDetailDto {
  logisticsEquipmentTypeId: string;
  quantity: number;
}
```

## Points d'Attention

### Gestion Hors Ligne
- Les événements sont persistés localement même sans connexion
- La synchronisation reprend automatiquement au retour de la connexion
- L'UI affiche toujours l'état le plus récent grâce à la projection

### Gestion des Conflits
- Les événements sont horodatés (`createdAt`)
- La projection ignore les événements antérieurs à `dataResolvedAt`
- En cas d'échec, l'événement reste dans la queue pour retry

### Performance
- Utilisation de `createSelector` pour mémoriser les projections
- Les événements complétés peuvent être nettoyés périodiquement
- La queue est chargée une seule fois au démarrage de l'app

## Endpoints Backend Correspondants

- `POST /api/deliver/stops/{stopId}/preload` - Préchargement
- `POST /api/deliver/stops/{stopId}/load` - Chargement définitif
- `POST /api/deliver/stops/{stopId}/complete` - Completion de livraison

Chaque endpoint attend le DTO correspondant et retourne l'entité `StopEntity` mise à jour.

## Exemple Complet d'Utilisation

```typescript
// Hook personnalisé pour gérer un stop
export function useStopOperations(stopId: string) {
  const dispatch = useAppDispatch();
  const getStopById = useMemo(() => makeGetStopByIdSelector(), []);
  const stop = useAppSelector(state => getStopById(state, stopId));
  
  const preloadStop = useCallback((data: IPreloadStopDto) => {
    dispatch(addEventToQueue({
      eventType: 'stop-preload',
      payload: { ...data, stopId }
    }));
  }, [dispatch, stopId]);

  const loadStop = useCallback((data: ILoadStopDto) => {
    dispatch(addEventToQueue({
      eventType: 'stop-load',
      payload: { ...data, stopId }
    }));
  }, [dispatch, stopId]);

  const completeStop = useCallback((data: ICompleteStopDeliveryDto) => {
    dispatch(addEventToQueue({
      eventType: 'stop-completion',
      payload: { ...data, stopId }
    }));
  }, [dispatch, stopId]);

  return {
    stop,
    preloadStop,
    loadStop,
    completeStop
  };
}
```

## Prochaines Étapes

1. **Implémenter les composants UI** pour chaque opération
2. **Ajouter des indicateurs visuels** pour les états de synchronisation
3. **Gérer les cas d'erreur** avec des messages utilisateur clairs
4. **Tester le mode hors ligne** extensivement
5. **Monitorer la taille de la queue** et implémenter un nettoyage automatique