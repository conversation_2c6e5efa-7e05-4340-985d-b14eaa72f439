# Guide Développeur - Architecture de Gestion Hors Ligne des Tournées

## Vue d'ensemble

Ce guide détaille l'architecture technique de la gestion hors ligne des tournées dans l'application mobile. Le système permet le téléchargement, le stockage et la consultation des tournées et documents de livraison sans connexion Internet.

## Table des matières

1. [Architecture globale](#architecture-globale)
2. [Composants Redux](#composants-redux)
3. [Services et hooks de synchronisation](#services-et-hooks-de-synchronisation)
4. [Flux de données et persistance](#flux-de-données-et-persistance)
5. [Intégration avec les bons de livraison](#intégration-avec-les-bons-de-livraison)
6. [Gestion des erreurs](#gestion-des-erreurs)
7. [Tests et débogage](#tests-et-débogage)
8. [Améliorations futures](#améliorations-futures)

## Architecture globale

### Diagramme d'architecture

```mermaid
graph TB
    subgraph "Couche UI"
        A[TourList] --> B[ApplicationStateStatus]
        A --> C[DeliveryNoteButton]
    end
    
    subgraph "Hooks de Synchronisation"
        D[useTourSynchronization]
        E[useDeliveryNoteSynchronization]
    end
    
    subgraph "Redux Store"
        F[tourSlice]
        G[deliveryNotesSlice]
        H[applicationStateSlice]
    end
    
    subgraph "Services"
        I[TourOperatorService]
        J[OfflineStorageService]
        K[DeliveryNoteCleanupService]
    end
    
    subgraph "Stockage Persistant"
        L[Capacitor Preferences]
        M[Filesystem Cache]
    end
    
    A --> D
    D --> F
    D --> I
    E --> G
    C --> G
    B --> F
    B --> G
    B --> H
    F --> L
    G --> L
    G --> J
    J --> M
    K --> M
    K --> L
```

### Composants principaux

1. **Redux Slices** : Gestion de l'état global
2. **Hooks de synchronisation** : Orchestration de la synchronisation
3. **Services de stockage** : Gestion des fichiers locaux
4. **Composants UI** : Interface utilisateur et indicateurs

## Composants Redux

### tourSlice

Gère l'état des tournées :

```typescript
interface TourState {
  tours: ITourEntity[];
  loading: boolean;
  error: string | null;
  isDataResolved: boolean;
}
```

**Actions principales** :
- `getToursForToday` : Thunk asynchrone pour récupérer les tournées
- `setTours` : Mise à jour de la liste des tournées
- `setLoading` : Gestion de l'état de chargement
- `setError` : Gestion des erreurs

**Logique de récupération** :
1. Si connecté : Récupère depuis l'API et met en cache
2. Si hors ligne : Charge depuis le cache local
3. Déclenche automatiquement la mise en file des bons de livraison

### deliveryNotesSlice

Gère le téléchargement et le stockage des bons de livraison :

```typescript
interface DeliveryNotesState {
  downloadQueue: DeliveryNoteDownloadItem[];
  isProcessing: boolean;
  offlineDeliveryNotes: Record<string, string>;
  error: string | null;
  isInitialized: boolean;
}

interface DeliveryNoteDownloadItem {
  deliveryNoteId: string;
  fileId: string;
  filename: string;
  status: 'pending' | 'downloading' | 'completed' | 'failed';
  localPath?: string;
  error?: string;
  retryCount: number;
  tourId: string;
  stopId: string;
}
```

**Thunks asynchrones** :
- `loadPersistedState` : Charge l'état persisté au démarrage
- `queueDeliveryNotesForDownload` : Ajoute des documents à la file
- `processNextDeliveryNote` : Traite le prochain document
- `processDeliveryNoteDownload` : Télécharge un document spécifique
- `persistOfflineNotes` : Sauvegarde l'état

**Gestion de la file de téléchargement** :
- Maximum 3 téléchargements simultanés
- Retry automatique (max 3 tentatives)
- Gestion des erreurs 404 (fichier non importé)
- Nettoyage automatique des éléments complétés

### applicationStateSlice

Gère l'état global de l'application (connexion, etc.) :

```typescript
interface ApplicationState {
  hasInternet: boolean;
  isInitialized: boolean;
}
```

## Services et hooks de synchronisation

### useTourSynchronization

Hook responsable de la synchronisation des tournées :

```typescript
export function useTourSynchronization() {
  const dispatch = useAppDispatch();
  const hasInternet = useAppSelector((state) => state.applicationState.hasInternet);
  const isDataResolved = useAppSelector((state) => state.tour.isDataResolved);
  // ...

  useEffect(() => {
    if (shouldSync) {
      dispatch(getToursForToday(hasInternet));
    }
  }, [dependencies]);
}
```

**Conditions de déclenchement** :
- Application initialisée
- Utilisateur authentifié
- Données non encore résolues
- État des bons de livraison initialisé

### useDeliveryNoteSynchronization

Hook gérant le téléchargement des bons de livraison :

```typescript
export function useDeliveryNoteSynchronization() {
  const isProcessing = useAppSelector((state) => state.deliveryNotes.isProcessing);
  const hasInternet = useAppSelector((state) => state.applicationState.hasInternet);
  const hasActiveDownloads = useAppSelector(hasActiveDownloadsSelector);
  
  // Gestion de l'état de traitement
  useEffect(() => {
    if (hasActiveDownloads && hasInternet && !isProcessing) {
      dispatch(setIsProcessing(true));
    }
  }, [conditions]);
  
  // Boucle de traitement
  useEffect(() => {
    if (isProcessing) {
      const interval = setInterval(() => {
        dispatch(processNextDeliveryNote());
      }, 200);
      return () => clearInterval(interval);
    }
  }, [isProcessing]);
}
```

**Stratégie de téléchargement** :
- Intervalle de 200ms entre chaque vérification
- Arrêt automatique si perte de connexion
- Reprise automatique à la reconnexion

## Flux de données et persistance

### Flux de synchronisation complet

```mermaid
sequenceDiagram
    participant User
    participant App
    participant Redux
    participant API
    participant Storage
    
    User->>App: Ouvre l'application
    App->>Redux: loadPersistedState()
    Redux->>Storage: Charge état sauvegardé
    
    alt Connecté à Internet
        App->>API: getTodayTours()
        API-->>Redux: Tours du jour
        Redux->>Storage: Sauvegarde tours
        Redux->>Redux: queueDeliveryNotesForDownload()
        
        loop Pour chaque document
            Redux->>API: downloadDeliveryNote()
            API-->>Storage: PDF (base64)
            Storage-->>Redux: Chemin local
            Redux->>Storage: Persist état
        end
    else Hors ligne
        Redux->>Storage: Charge tours en cache
    end
    
    User->>App: Consulte bon de livraison
    App->>Storage: Récupère PDF local
    Storage-->>User: Affiche PDF
```

### Stockage persistant

#### Capacitor Preferences
Utilisé pour stocker :
- État des tournées (`tours`)
- File de téléchargement (`deliveryNotes_downloadQueue`)
- Références des fichiers hors ligne (`deliveryNotes_offline`)
- Date du dernier nettoyage (`deliveryNotes_lastCleanup`)

#### Filesystem (Cache Directory)
Structure des dossiers :
```
Cache/
└── deliveryNotes/
    └── YYYY-MM-DD/
        ├── BL_4012_20250502_0004876078.pdf
        ├── BL_4012_20250502_0004880540.pdf
        └── ...
```

### OfflineStorageService

Service gérant l'accès au système de fichiers :

```typescript
export class OfflineStorageService {
  directory = Directory.Cache;
  
  async writeFile(fileName: string, data: string) {
    await this.ensureDirectoryExists();
    const filePath = `${this.basePath}/${fileName}`;
    await Filesystem.writeFile({
      path: filePath,
      directory: this.directory,
      data,
    });
  }
  
  async doesFileExist(fileName: string) {
    // Vérification d'existence
  }
  
  async getFileContent(fileName: string): Promise<string> {
    // Récupération du contenu
  }
}
```

## Intégration avec les bons de livraison

### DeliveryNoteButton

Composant affichant l'état et permettant l'ouverture des PDF :

```typescript
const DeliveryNoteButton: React.FC<Props> = ({ deliveryNote, tourId }) => {
  const downloadItem = useAppSelector(state => 
    state.deliveryNotes.downloadQueue.find(item => 
      item.deliveryNoteId === deliveryNote.id
    )
  );
  
  const isOfflineAvailable = useAppSelector(state => 
    !!state.deliveryNotes.offlineDeliveryNotes[deliveryNote.id]
  );
  
  // Logique d'affichage selon l'état
  if (isOfflineAvailable) {
    // Bouton vert - Ouvrir le PDF
  } else if (downloadItem?.status === 'downloading') {
    // Spinner - Téléchargement en cours
  } else if (downloadItem?.status === 'failed') {
    // Bouton rouge - Erreur
  }
};
```

### Ouverture des PDF

Utilisation du plugin FileOpener de Capacitor :

```typescript
const openDeliveryNote = async () => {
  if (isOfflineAvailable) {
    const base64Data = await deliveryNotesStorageService.getFileContent(
      deliveryNote.filename
    );
    
    // Création d'un blob et ouverture
    const blob = base64ToBlob(base64Data, 'application/pdf');
    const url = URL.createObjectURL(blob);
    await Browser.open({ url });
  }
};
```

## Gestion des erreurs

### Stratégies de retry

1. **Erreurs réseau** : 3 tentatives avec incrémentation du compteur
2. **Erreurs 404** : Pas de retry (fichier non encore importé)
3. **Timeout** : 30 secondes par téléchargement

### Gestion des états bloqués

```typescript
// Reset des téléchargements bloqués
resetStuckDownloads: (state) => {
  state.downloadQueue = state.downloadQueue.map(item => {
    if (item.status === 'downloading') {
      return { ...item, status: 'pending' };
    }
    return item;
  });
}
```

### Nettoyage et maintenance

#### DeliveryNoteCleanupService

Service de nettoyage automatique :

```typescript
export class DeliveryNoteCleanupService {
  async cleanupOldDeliveryNotes() {
    const todayFolder = DateTime.now().toISODate();
    const folders = await this.listFolders();
    
    for (const folder of folders) {
      if (folder !== todayFolder) {
        await this.deleteFolder(folder);
      }
    }
    
    await this.cleanupReduxState();
  }
  
  async shouldRunCleanup(): Promise<boolean> {
    // Vérifie si le nettoyage doit être exécuté (1 fois/jour)
  }
}
```

## Tests et débogage

### Points de débogage

1. **Console logs stratégiques** :
```typescript
console.log('[ProcessDownload] Starting download for:', filename);
console.log('[DeliveryNoteSync] Queue status:', queueStats);
```

2. **Redux DevTools** :
- Inspection de l'état
- Suivi des actions
- Time-travel debugging

3. **Vérification du stockage** :
```typescript
// Dans la console du navigateur
const stats = await deliveryNoteCleanupService.getStorageStats();
console.log('Storage stats:', stats);
```

### Tests unitaires recommandés

```typescript
describe('DeliveryNotesSlice', () => {
  it('should queue delivery notes for download', async () => {
    const notes = [/* mock data */];
    const result = await store.dispatch(
      queueDeliveryNotesForDownload(notes)
    );
    expect(store.getState().deliveryNotes.downloadQueue).toHaveLength(notes.length);
  });
  
  it('should handle download failures with retry', () => {
    // Test de la logique de retry
  });
  
  it('should clean completed downloads from queue', () => {
    // Test du nettoyage automatique
  });
});
```

### Simulation d'environnements

1. **Mode hors ligne** :
```typescript
// Forcer le mode hors ligne
dispatch(setHasInternet(false));
```

2. **Erreurs de téléchargement** :
```typescript
// Mock du service pour simuler des erreurs
jest.mock('../services/TourOperatorService', () => ({
  downloadDeliveryNote: jest.fn().mockRejectedValue(new Error('Network error'))
}));
```

## Améliorations futures

### Optimisations de performance

1. **Téléchargement différentiel** :
   - Vérifier les checksums avant téléchargement
   - Ne télécharger que les fichiers modifiés

2. **Compression** :
   - Compresser les PDF côté serveur
   - Décompression à la volée côté client

3. **Priorisation intelligente** :
   - Télécharger en priorité les prochains arrêts
   - Apprentissage des patterns d'utilisation

### Fonctionnalités avancées

1. **Synchronisation bidirectionnelle** :
```typescript
interface OfflineAction {
  type: 'COMPLETE_STOP' | 'UPDATE_STATUS';
  payload: any;
  timestamp: number;
  synced: boolean;
}
```

2. **Gestion de quotas** :
```typescript
interface StorageQuota {
  maxSizeMB: number;
  warningThresholdPercent: number;
  autoCleanupEnabled: boolean;
}
```

3. **Métriques et analytiques** :
```typescript
interface SyncMetrics {
  totalDownloads: number;
  successRate: number;
  averageDownloadTime: number;
  offlineUsageTime: number;
}
```

### Intégration avec d'autres systèmes

1. **Service Worker** pour le téléchargement en arrière-plan
2. **Push notifications** pour les mises à jour importantes
3. **Synchronisation avec calendrier** pour préchargement

### Améliorations de l'expérience utilisateur

1. **Mode économie de données** :
   - Téléchargement WiFi uniquement
   - Compression adaptative

2. **Prévisualisation** :
   - Miniatures des documents
   - Métadonnées sans téléchargement complet

3. **Gestion avancée du cache** :
   - LRU (Least Recently Used) pour l'éviction
   - Préchargement basé sur l'historique

### Considérations de sécurité

1. **Chiffrement des données locales**
2. **Validation de l'intégrité des fichiers**
3. **Expiration automatique des données sensibles**