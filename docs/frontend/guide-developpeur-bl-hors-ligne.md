# Guide Développeur - Architecture des Bons de Livraison Hors Ligne

## Vue d'ensemble de l'architecture

La fonctionnalité de bons de livraison hors ligne est construite autour de plusieurs composants clés qui travaillent ensemble pour offrir une expérience fluide de téléchargement, stockage et consultation des PDF de livraison.

## Composants principaux

### 1. DeliveryNoteButton (`components/DeliveryNoteButton.tsx`)

Le composant principal qui gère l'interface utilisateur pour les bons de livraison.

**Fonctionnalités principales :**
- Affichage de l'état du document (en ligne, hors ligne, en téléchargement)
- Gestion des clics pour ouvrir ou télécharger
- Intégration avec Redux pour l'état global
- Support des modes compact et étendu

**États gérés :**
```typescript
- isOffline: boolean // Document disponible localement
- isDownloading: boolean // Téléchargement en cours
- isPending: boolean // En attente dans la queue
- isFailed: boolean // Échec du téléchargement
- isNotAvailable: boolean // Fichier non importé sur le serveur
```

### 2. Delivery Note Loader (`utils/delivery-note-loader.ts`)

Utilitaire intelligent pour charger les bons de livraison avec stratégie de cache.

**Fonctions principales :**

```typescript
// Charge un bon de livraison (cache ou téléchargement)
loadDeliveryNote(
  tourId: string,
  deliveryNote: IDeliveryNoteEntity,
  options: {
    openAfterDownload?: boolean,
    forceRefresh?: boolean
  }
): Promise<DeliveryNoteLoaderResult>

// Vérifie si un document est disponible localement
isDeliveryNoteAvailableLocally(
  deliveryNote: IDeliveryNoteEntity
): Promise<boolean>

// Récupère le contenu base64 d'un document
getDeliveryNoteContent(
  tourId: string,
  deliveryNote: IDeliveryNoteEntity
): Promise<string>
```

**Stratégie de stockage :**
- Les fichiers sont stockés dans `Directory.Cache`
- Structure : `deliveryNotes/YYYY-MM-DD/filename.pdf`
- Conversion blob → base64 pour le stockage Capacitor

### 3. Redux Slice (`stores/deliveryNotesSlice.ts`)

Gestion de l'état global des téléchargements et du stockage hors ligne.

**État Redux :**
```typescript
interface DeliveryNotesState {
  downloadQueue: DeliveryNoteDownloadItem[] // Queue de téléchargement
  isProcessing: boolean // Traitement en cours
  offlineDeliveryNotes: Record<string, string> // ID → chemin local
  error: string | null
  isInitialized: boolean
}
```

**Actions principales :**
- `queueDeliveryNotesForDownload`: Ajoute des documents à la queue
- `processNextDeliveryNote`: Traite le prochain téléchargement
- `updateDownloadItemStatus`: Met à jour le statut d'un téléchargement
- `resetStuckDownloads`: Réinitialise les téléchargements bloqués

### 4. Service de stockage hors ligne (`services/OfflineStorageService.ts`)

Service générique pour la gestion du stockage local.

**Méthodes :**
```typescript
class OfflineStorageService {
  writeFile(fileName: string, data: string): Promise<void>
  doesFileExist(fileName: string): Promise<boolean>
  getFileContent(fileName: string): Promise<string>
}
```

### 5. Service de nettoyage (`services/DeliveryNoteCleanupService.ts`)

Gère le nettoyage automatique et manuel des anciens fichiers.

**Fonctionnalités :**
- Suppression des dossiers de dates antérieures
- Statistiques de stockage
- Nettoyage de l'état Redux
- Vérification de la dernière exécution

### 6. Hook de synchronisation (`plugin/applicationState/useDeliveryNoteSynchronization.ts`)

Gère la synchronisation automatique en arrière-plan.

**Logique :**
- Démarre/arrête le traitement selon la connexion internet
- Intervalle de 200ms pour traiter la queue
- Limite de 3 téléchargements simultanés

## Flux de données

### 1. Téléchargement d'un document

```mermaid
sequenceDiagram
    participant User
    participant DeliveryNoteButton
    participant Loader
    participant Redux
    participant API
    participant Storage

    User->>DeliveryNoteButton: Click
    DeliveryNoteButton->>Loader: loadDeliveryNote()
    Loader->>Storage: Check local file
    alt File exists
        Loader->>FileOpener: Open PDF
    else File missing
        Loader->>API: Download PDF
        API-->>Loader: Blob data
        Loader->>Storage: Save base64
        Loader->>FileOpener: Open PDF
        Loader->>Redux: Update offline state
    end
```

### 2. Téléchargement en arrière-plan

```mermaid
sequenceDiagram
    participant Sync Hook
    participant Redux
    participant API
    participant Storage

    Sync Hook->>Redux: Check queue
    loop Every 200ms
        Redux->>Redux: Get next pending
        Redux->>API: Download file
        API-->>Redux: Blob data
        Redux->>Storage: Save file
        Redux->>Redux: Update status
    end
```

## Persistance des données

### 1. Capacitor Preferences

Utilisé pour stocker l'état Redux entre les sessions :
- `deliveryNotes_downloadQueue`: Queue de téléchargement
- `deliveryNotes_offline`: Mapping des fichiers hors ligne
- `deliveryNotes_lastCleanup`: Dernière exécution du nettoyage

### 2. Filesystem Capacitor

Structure de stockage :
```
Cache/
└── deliveryNotes/
    └── 2025-01-31/
        ├── BL_4012_20250131_0001234567.pdf
        ├── BL_4012_20250131_0001234568.pdf
        └── ...
```

## Gestion des erreurs

### Types d'erreurs gérés :

1. **404 / Not Found** : Fichier non encore importé
   - Marqué comme "File not yet imported"
   - Pas de retry automatique

2. **Timeout** : Délai de 30 secondes dépassé
   - Retry jusqu'à 3 fois
   - Incrémentation du compteur de retry

3. **Erreur réseau** : Pas de connexion
   - Mise en pause du traitement
   - Reprise automatique au retour de la connexion

## Optimisations

### 1. Performances

- **Téléchargements parallèles** : Maximum 3 simultanés
- **Cache intelligent** : Vérification locale avant téléchargement
- **Conversion asynchrone** : Blob → Base64 sans bloquer l'UI

### 2. Espace de stockage

- **Nettoyage automatique** : Au démarrage de l'app
- **Structure par date** : Facilite la suppression des anciens fichiers
- **Compression** : Les PDF sont déjà optimisés côté serveur

### 3. Expérience utilisateur

- **Feedback visuel** : Icônes et spinners pour chaque état
- **Mode dégradé** : Téléchargement via navigateur si FileOpener échoue
- **Retry intelligent** : Distinction entre erreurs temporaires et permanentes

## Configuration et personnalisation

### 1. Limites configurables

```typescript
// Dans processNextDeliveryNote
const MAX_CONCURRENT_DOWNLOADS = 3;

// Dans processDeliveryNoteDownload
const DOWNLOAD_TIMEOUT = 30000; // 30 secondes

// Dans useDeliveryNoteSynchronization
const PROCESSING_INTERVAL = 200; // 200ms
```

### 2. Stratégies de stockage

Le chemin de base peut être modifié dans `OfflineStorageService` :
```typescript
this.basePath = `${this.basePath}/${DateTime.now().toISODate()}`;
```

## Tests et débogage

### 1. Logs utiles

L'application utilise des préfixes pour faciliter le filtrage :
- `[DeliveryNoteLoader]` : Opérations de chargement
- `[ProcessDownload]` : Téléchargements individuels
- `[ProcessNext]` : Gestion de la queue
- `[DeliveryNoteSync]` : Synchronisation
- `[DeliveryNoteCleanup]` : Nettoyage

### 2. États Redux DevTools

Surveillez ces états dans Redux DevTools :
- `deliveryNotes.downloadQueue` : Queue active
- `deliveryNotes.offlineDeliveryNotes` : Fichiers disponibles
- `deliveryNotes.isProcessing` : État du processeur

### 3. Inspection du stockage

Sur Android :
```bash
adb shell
run-as com.yourapp
cd cache/deliveryNotes
ls -la
```

Sur iOS : Utilisez Xcode Device Manager

## Améliorations futures

1. **Force refresh** : Permettre le re-téléchargement manuel
2. **Compression** : Zipper plusieurs PDF ensemble
3. **Préchargement** : Télécharger les tournées du lendemain
4. **Métriques** : Statistiques d'utilisation hors ligne
5. **Synchronisation différentielle** : Ne télécharger que les changements

## Dépannage courant

### Problème : Les téléchargements ne démarrent pas

**Solutions :**
1. Vérifier `hasInternet` dans l'état Redux
2. Vérifier que `isProcessing` est `true`
3. Inspecter la `downloadQueue` pour les items `pending`

### Problème : Fichiers non trouvés après téléchargement

**Solutions :**
1. Vérifier le format de date dans le chemin
2. S'assurer que `Directory.Cache` est utilisé partout
3. Vérifier les permissions de l'application

### Problème : Mémoire saturée

**Solutions :**
1. Forcer le nettoyage manuel
2. Réduire la durée de rétention
3. Implémenter une limite de taille totale