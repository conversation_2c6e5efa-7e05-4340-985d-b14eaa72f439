# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Common Development Workflow

```bash
pnpm dev                # Start all apps concurrently
pnpm backend:dev        # Backend only (port 3000)
pnpm frontend:dev       # Frontend only (port 8000)  
pnpm mobile:dev         # Mobile only (port 8001)
```

### Backend (@lrg/backend)

```bash
pnpm start              # Start server
pnpm start:dev          # Development with watch mode
pnpm start:debug        # Debug mode with watch
pnpm start:prod         # Production mode (requires build)
pnpm test               # Run Jest unit tests
pnpm test:watch         # Test watch mode
pnpm test:cov           # Coverage reports
pnpm test <path>        # Run tests for specific file/directory
pnpm format             # Prettier code formatting
pnpm tidy               # Format + lint fix
pnpm test:debug         # Debug tests
pnpm test:e2e           # E2E tests
pnpm lint               # ESLint with auto-fix
pnpm typecheck          # TypeScript validation
pnpm build              # Production build
pnpm tidy               # Format + lint fix

# CLI Commands
pnpm command-entrypoint-dev <command>    # Development CLI
pnpm command-entrypoint-prod <command>   # Production CLI

# Available commands:
# - import-xml-tours --dir <directory>     # Import tours from XML files
# - assign-tour                            # Assign tours to operators
# - seed-keycloak-roles                    # Seed Keycloak roles
# - simulate-app (not yet implemented)

# Available CLI Commands:
# - seed-roles: Seed Keycloak roles (MANAGER, OPERATOR)
# - assign-tour: Interactive tour assignment to users
# - import:xml-tours-from-local-files: Import XML tours from data directory
```

### Frontend (@lrg/frontend)

```bash
pnpm dev                # Vite dev server
pnpm build              # Production build
pnpm lint               # ESLint
pnpm lint:fix           # ESLint with auto-fix
pnpm typecheck          # TypeScript validation
pnpm tidy               # Format + lint fix
```

### Mobile (@lrg/mobile)

```bash
pnpm dev                # Vite dev server
pnpm build              # Production build
pnpm lint               # ESLint with auto-fix
pnpm typecheck          # TypeScript validation
pnpm sync               # Sync web assets to native platforms
pnpm open:ios           # Open in Xcode
```

## Architecture Overview

### Monorepo Structure

- **Backend**: NestJS API with TypeORM and PostgreSQL
- **Frontend**: React 19 with Vite, TailwindCSS, Redux Toolkit, TanStack Query
- **Mobile**: Ionic React with Capacitor for iOS/Android

### Backend Architecture (Domain-Driven Design)

```
src/
├── domain/           # Entities, enums, core business logic
├── application/      # DTOs, services, use cases, mappers
└── infrastructure/   # Controllers, repositories, external services
```

Key patterns:

- Repository pattern with TypeORM
- Service layer for business logic
- DTO validation with class-validator
- CLI commands using nest-commander
- Keycloak authentication with JWT

### Frontend Architecture

- Component-based React with custom hooks
- State management: Redux Toolkit (client state) + TanStack Query (server state)
- UI components: Radix UI primitives + custom components
- API client with axios and Keycloak integration

### Core Domain Entities

- **Users**: Keycloak-managed authentication
- **Tours**: Tour assignments to drivers with stops
- **Deliveries**: Delivery notes and shipment lines  
- **Clients**: Client management with addresses
- **Files**: S3-stored files with metadata

### Tour Entity Business Rules

**IMPORTANT**: Tours have a **composite unique key**:
- A tour is uniquely identified by: `tourIdentifier.originalNumber + deliveryDate`
- The same `tourIdentifier.originalNumber` can appear multiple times in the database, **one for each delivery date**
- Example: Tour "4029" can exist for 2024-12-01, 2024-12-02, 2024-12-03, etc.

This means:
- When searching/filtering tours, always include both `originalNumber` AND `deliveryDate`
- Tour assignments reference this composite key
- Planning displays must group by both identifiers
- Import processes create one tour per date, even with the same original number

## Development Guidelines

### Code Quality

- Run `pnpm lint` and `pnpm typecheck` before committing
- Backend uses strict TypeScript with explicit return types
- Database entities use snake_case, TypeScript uses camelCase
- ESLint enforces consistent code style across all apps

### Testing

- Backend: Jest unit tests in `src/` alongside source files
- Use `pnpm test:watch` for TDD workflow
- Repository tests use in-memory database

### Database

- PostgreSQL with TypeORM
- Auto-sync enabled in development
- Entities in `src/domain/entity/`
- Repositories in `src/infrastructure/repository/`

### File Operations

- All file uploads go through S3Service
- File entities track metadata in database
- PDF processing for delivery documents

## Backend-Specific Details

### CLI Commands Usage Examples

```bash
# From backend directory:
cd apps/backend

# Seed Keycloak roles (required for first-time setup)
pnpm command-entrypoint-dev seed-roles

# Import XML tour files from data directory
pnpm command-entrypoint-dev import:xml-tours-from-local-files

# Assign tour to user (interactive prompt)
pnpm command-entrypoint-dev assign-tour
```

### XML Tour Import

- Place XML files in `data/YYYYMMDD/` folders
- Files are imported by date folder (e.g., `data/20241201/`)
- Import creates Tour entities with stops and delivery notes
- Tour types: Normal tours and Frozen tours (suffix 'TK')

### Running Specific Tests

```bash
# Run tests for a specific file
pnpm test src/application/service/tour.service.spec.ts

# Run tests matching a pattern
pnpm test --testNamePattern="should create tour"

# Run tests in a specific directory
pnpm test src/application/service/
```

## Development Notes

- Attention: Tours are imported multiple times in the app daily. Tour identifiers can be found by making a distinct request on tourIdentifier.originalNumber
- XML tour imports process delivery notes and create tour assignments
- TypeScript strict mode is disabled; explicit types are still encouraged
- E2E tests use a separate configuration in test/jest-e2e.json
- The backend README.md mentions a simulation command, but it's not currently implemented in the command directory

## Business Rules - Delivery Completion Flow

### Stop Completion Rules

1. **Normal Delivery**: Signature is optional
2. **Secure Location Delivery**: Photo proof is mandatory (no signature needed)
3. **At least one proof required**: Either signature OR photo must be provided (unless secure location)

### Incident Handling

- If an incident is reported, must specify delivery completion type:
  - `FULL`: Delivery completed despite incident
  - `PARTIAL`: Partial delivery due to incident
  - `NONE`: No delivery possible due to incident
- Incident types are managed in the `incident_types` table

### Equipment Return (Reprise d'agrès) : LogisticsEquipment

- Numeric fields for:
  - Pallet count (palettes)
  - Roll count (rolls)
  - Package count (colis)
- All equipment returns are optional

### File Storage

- Signatures and photos are stored in S3
- Files are uploaded as base64 in the API request
- File metadata is tracked in the `file` table

### API Endpoints

- `POST /operator/stops/:id/complete` - Complete a stop delivery
- `GET /operator/incident-types` - Get available incident types

## Development Principles

- When possible, parallel process tasks

## Code Development Best Practices

- Quand tu développe assure toi de creer peu de dette technique

## Git Commit Guidelines

- Quand tu commit ne specifie pas que toi (claude) est le co author

## GitHub Interaction

- Pour interagir avec GitHub comme les issues PR etc.. utilise le gh cli

## Routing Specifics

- react-router-dom n'existe pas sur ce projet c'est juste react-router mais fonctionne exactement pareil  c'est juste l'import a modifier