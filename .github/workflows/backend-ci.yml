name: Backend CI

on:
  push:
    branches: [main]
    paths:
      - "apps/backend/**"
  pull_request:
    branches: [main]
    paths:
      - "apps/backend/**"

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: "10.0.0"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "pnpm"
          cache-dependency-path: "pnpm-lock.yaml"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Lint
        working-directory: apps/backend
        run: pnpm run lint

      - name: Type check
        working-directory: apps/backend
        run: pnpm run typecheck

      - name: Build
        working-directory: apps/backend
        run: pnpm run build

