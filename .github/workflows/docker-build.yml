name: Docker Build and Push

on:
  push:
    branches:
      - main
      - staging
      - production

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Extract branch name
        shell: bash
        run: echo "branch_name=${GITHUB_REF#refs/heads/}" >> $GITHUB_ENV
      
      - name: Log in to Scaleway Container Registry
        uses: docker/login-action@v3
        with:
          registry: rg.fr-par.scw.cloud/lrg-hosting
          username: nologin
          password: ${{ secrets.SCW_PASSWORD }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: rg.fr-par.scw.cloud/lrg-hosting/lrg:${{ env.branch_name }}