
## **Objectifs globaux**

- Moderniser la gestion quotidienne des tournées de livraison
- Faciliter le travail terrain pour les chauffeurs et réceptionnistes
- Offrir au management un suivi temps réel et des outils d'action
- Sécuriser la traçabilité (agrès, paiements, signatures, BL, incidents)

## **Fonctionnalités par profil utilisateur**

### **Manager (Interface web)**

**Paramétrage :**

- Gestion des types d'agrès (couleur, forme) avec impact immédiat sur mobile
- Création/gestion des camions (immatriculation, capacité, statut TK)
- Gestion de la liste des types d'incident

**Planning :**

- Planification annuelle (20 tournées fixes + 5 licences temporaires)
- Drag-and-drop chauffeurs ↔ tournées
- Réaffectations en cas d'imprévu
- Fusion ou scission de tournées (multi-tournées pour un chauffeur)

**Supervision :**

- Dashboard temps réel : % d'avancement, livraisons OK/KO, incidents ouverts, non-paiements, agrès manquants
- Statistiques historiques : nombre de livraisons, temps moyen, agrès manquants
- Consultation de la liste des tournées et livraisons en temps réel

**Gestion des événements :**

- Traitement des non-paiements
- Workflow de correction des écarts d'agrès
- Gestion des incidents véhicule et circulation

### **Chauffeur (Application mobile)**

**Préparation du chargement :**

- Prévisualisation des écrans Surgelés → Frais → Ramasse
- Liste scrollable triée "dernier livré / premier chargé"
- Validation par check-list : Complet / Partiel / Absent

**Exécution de la livraison :**

- Choix libre de l'ordre des arrêts
- Multi-tournées (navigation par onglets)
- Fiche client avec codes LIV/RAM, adresses, email pré-renseigné

**Gestion des incidents :**

- Bouton incident disponible à tout moment
- Trois familles : Circulation, Refus client, Défaut de paiement
- Questions obligatoires selon le type d'incident
- Commentaire libre pour chaque incident

**Contrôle des agrès :**

- Trois écrans dédiés (surgelés, frais, ramasse)
- Saisie des quantités
- Reprise possible si client absent

**Validation de la tournée :**

- Ajout de photos multiples
- Signature client (nom, prénom, GPS, date/heure)
- Génération automatique du Document de réception PDF
- Gestion des paiements (paid/partial/not_paid)

### **Réceptionniste (Application mobile)**

**Contrôle des agrès :**

- Un écran par type d'agré (palette, roll, caisse...)
- Saisie rapide des quantités réelles
- Calcul automatique de l'écart VS déclaration chauffeur

**Validation :**

- Alerte "Écart agrès" envoyée au Manager si mismatch
- Possibilité d'ajouter un commentaire

**Suivi :**

- Liste des tournées terminées avec date/heure
- Filtres et recherche
- Consultation des tournées en temps réel

## **Architecture technique**

- **Frontend web :** React 18
- **Apps mobiles :** Ionic 7 + React 18 + TailwindCSS
- **Backend :** NestJS 10 avec REST & WebSocket
- **Base de données :** PostgreSQL 15
- **Stockage :** Amazon S3 (photos, signatures, PDF)
- **Notifications :** Firebase Cloud Messaging / APNs + SMTP
- **Intégration :** Fichier XML quotidien via FTP pour les données de tournées

Le projet suit une approche MVP avec un déploiement en phases, de juin 2025 à décembre 2025.

## 📊 **ÉTAT D'AVANCEMENT ACTUEL**

### ✅ **FONCTIONNALITÉS IMPLÉMENTÉES ET OPÉRATIONNELLES**

#### **Backend (NestJS + PostgreSQL + S3)**

- ✅ **Architecture complète** : Domain-Driven Design avec entités, services, repositories
- ✅ **Authentification Keycloak** : Intégration complète avec JWT et rôles (MANAGER/OPERATOR)
- ✅ **Gestion des tournées** : CRUD complet, import XML automatique, assignation
- ✅ **Gestion des arrêts** : API complète avec completion et gestion des incidents
- ✅ **Gestion des clients** : CRUD complet avec adresses et informations de livraison
- ✅ **Gestion des bons de livraison** : Upload, stockage S3, métadonnées
- ✅ **API de completion** : Endpoint complet avec validation métier et stockage fichiers
- ✅ **Gestion des incidents** : Types d'incidents, description, completion conditionnelle
- ✅ **CLI outils** : Import XML, assignation, gestion des rôles Keycloak
- ✅ **Statuts de tournée** : Transitions automatiques PLANNED → IN_PROGRESS → COMPLETED
- ❌ **WebSocket temps réel** : Pas d'updates en temps réel implémentées
- ❌ **Tests** : Quelques tests unitaires, il faudrait en ajouter

#### **Frontend Web (React + Redux + TailwindCSS)**

- ✅ **Architecture de base** : Structure complète avec routing et état partagé
- ✅ **Authentification** : Intégration Keycloak avec context et protection des routes
- ✅ **Navigation et layout** : Sidebar complète, breadcrumbs, responsive design
- ✅ **API integration** : Services complets pour tous les endpoints backend
- ✅ **State management** : Redux Toolkit avec TanStack Query
- ✅ **UI Components** : Librairie complète Radix UI + TailwindCSS
- 🔄 **Services API seulement** : Tour, client, file management (pas d'UI)
- ❌ **Dashboard manager** : Aucune vue métier implémentée
- ❌ **Interfaces CRUD** : Pas d'interfaces utilisateur pour les entités
- ❌ **Export de données** : Aucune fonctionnalité d'export

#### **Mobile (Ionic React + Capacitor + Redux)**

- ✅ **Architecture complète** : Ionic 7 avec offline-first et synchronisation
- ✅ **Authentification mobile** : Keycloak avec stockage sécurisé Capacitor
- ✅ **Accès au data hors-ligne** : Stockage local des tournées et synchronisation automatique
- ❌ **Validation des tournées hors-ligne** : Validation des tournées hors-ligne pas totalement implémentée
- ✅ **Gestion des BL** : Téléchargement offline, visualisation, queue de download
- ✅ **Navigation des tournées** : Liste, détails, indicateurs de progression
- ✅ **Completion des arrêts** : Interface complète avec signature, incidents, équipements
- ❌ **Queue offline** : Gestion automatique des completions en attente de sync : partiellement implémentée
- ✅ **Signature capture** : Canvas tactile avec validation métier
- ✅ **Indicateurs visuels** : Statuts temps réel, synchronisation, erreurs
- ✅ **Support multi-tournées** : Gestion simultanée de plusieurs tournées
- ✅ **Photo capture** : Capture photo réelle avec caméra et galerie Capacitor
- ❌ **GPS/Location** : Aucun tracking de géolocalisation
- ❌ **Écrans de préparation** : Pas d'interface de préparation du chargement

#### **Fonctionnalités métier opérationnelles**

- ✅ **Import quotidien XML** : Traitement automatique des tournées
- ✅ **Assignation des tournées** : CLI et interface pour affecter aux chauffeurs
- ✅ **Suivi temps réel** : États des arrêts et synchronisation mobile ↔ backend
- ✅ **Gestion des preuves** : Signature et photo avec validation métier
- ✅ **Gestion des incidents** : Types configurables, completion conditionnelle
- ✅ **Retour d'équipement** : Saisie palettes/rolls/colis avec validation
- ✅ **Lieux sûrs** : Validation spécifique pour livraisons sécurisées

### 🎯 **PROCHAINES ÉTAPES RECOMMANDÉES**

#### **Priorité Immédiate (Semaine 1-2)**

1. **Dashboard manager** : Créer les interfaces web manquantes (KPI, liste tournées)
2. **Photo capture mobile** : Implémenter la vraie caméra Capacitor
3. **Tests critiques** : Tests E2E du flow de completion
4. **WebSocket temps réel** : Updates automatiques pour le dashboard

#### **Priorité Élevée (Semaine 3-4)**

1. **Interfaces CRUD web** : Tour, client, incident management
2. **GPS mobile** : Tracking de géolocalisation pour les signatures
3. **Tests complets** : Couverture backend + mobile
4. **Préparation de chargement** : Écrans mobiles manquants

### 📈 **MÉTRIQUES D'AVANCEMENT MVP**

| Composant | Fonctionnalités Core | Complétées | Restantes | % Avancement |
|-----------|---------------------|------------|-----------|--------------|
| **Backend** | 12 | 10 | 2 | **83%** |
| **Frontend Manager** | 10 | 4 | 6 | **40%** |
| **Mobile Chauffeur** | 15 | 12 | 3 | **80%** |
| **Mobile Réceptionniste** | 8 | 0 | 8 | **0%** |
| **Tests & Qualité** | 10 | 0 | 10 | **0%** |
| **Infrastructure** | 6 | 6 | 0 | **100%** |

**🎯 Avancement Global MVP : ~67%**

#### **🚀 Fonctionnalités critiques complétées**

- ✅ Architecture complète et déploiement
- ✅ Authentification et sécurité (Keycloak)
- ✅ Backend API complet et robuste
- ✅ Interface mobile chauffeur opérationnelle
- ✅ Système offline/synchronisation
- ✅ Completion des livraisons avec validation métier

#### **⚠️ Gaps critiques identifiés**

- ❌ Dashboard manager (interface web vide)
- ❌ Tests (aucun test implémenté)
- ❌ WebSocket temps réel
- ✅ Photo capture mobile (implémentée avec Capacitor Camera)

## TODO LIST MVP - TÂCHES RESTANTES

### 🚨 **PRIORITÉ HAUTE** (Bloquant pour le MVP)

#### 1. ✅ **📱 MOBILE: Implémenter le flow complet de livraison (Stop Completion)** - **TERMINÉ**

- ✅ Créer la page/modal de complétion d'arrêt avec tous les champs requis
- ✅ Intégrer la capture de signature (canvas)
- ✅ Intégrer la capture photo (Capacitor Camera API) - *Implémentation complète*
- ✅ Créer le formulaire de retour d'agrès (palettes, rolls, colis)
- ✅ Créer le sélecteur d'incidents avec questions obligatoires
- ✅ Gérer les types de complétion (FULL/PARTIAL/NONE)
- ✅ Connecter le bouton ARRIVÉE dans TourDetail.tsx
- ✅ Implémenter la validation des règles métier (signature OU photo obligatoire)
- ✅ Validation alignée avec le backend (lieu sûr, incidents, équipements)

#### 2. ✅ **📱 MOBILE: Finaliser le mode offline et la synchronisation** - **TERMINÉ**

- ✅ Compléter useTourSynchronisation.ts avec gestion des conflits
- ✅ Implémenter la queue de synchronisation pour les complétions d'arrêts
- ✅ Gérer le stockage local des photos/signatures avec base64
- ✅ Implémenter la synchronisation automatique au retour réseau
- ✅ Ajouter des indicateurs visuels du statut offline/online
- ✅ Redux middleware pour persistance automatique
- ✅ Gestion des erreurs et retry avec compteurs

#### 3. **🔧 BACKEND: Gestion temps réel et tests** - **CRITIQUE**

- ✅ Transitions automatiques implémentées (PLANNED → IN_PROGRESS → COMPLETED)
- ✅ Endpoints tournée opérationnels (/operator/tours)
- ✅ Logique de statuts au niveau des stops
- ✅ API stop completion connectée aux statuts
- ❌ **Manquant** : WebSocket/SSE pour updates temps réel
- ❌ **Manquant** : Tests unitaires et d'intégration

#### 4. ✅ **📱 MOBILE: Créer les composants UI manquants** - **TERMINÉ**

- ✅ SignatureCapture: Canvas pour signature avec gestion tactile
- ✅ PhotoCapture: Interface de prise de photo avec preview
- ✅ EquipmentReturnForm: Formulaire numérique pour agrès (intégré)
- ✅ IncidentSelector: Sélecteur avec questions conditionnelles
- ❌ PaymentSelector: Choix paid/partial/not_paid - *Non requis pour MVP*
- ✅ StopCompletionModal: Page complète regroupant tous les éléments

### ⚡ **PRIORITÉ MOYENNE** (Important pour le MVP)

#### 5. **💻 FRONTEND: Créer le dashboard manager** - **CRITIQUE**

- ✅ Architecture et authentification en place
- ✅ Services API pour toutes les données nécessaires
- ✅ Composants UI et navigation prêts
- ❌ **Manquant** : Vue principale avec KPIs temps réel
- ❌ **Manquant** : Liste des tournées avec filtres et recherche  
- ❌ **Manquant** : Graphiques de performance
- ❌ **Manquant** : Alertes pour incidents
- ❌ **Manquant** : Export des données en CSV/Excel

#### 6. **💻 FRONTEND: Créer l'interface de gestion des tournées**

- Liste paginée avec filtres (date, statut, chauffeur)
- Vue détaillée d'une tournée avec tous les arrêts
- Drag & drop pour réassignation de tournées
- Interface de fusion/scission de tournées
- Gestion des multi-tournées pour un chauffeur

#### 7. **💻 FRONTEND: Créer l'interface de gestion des clients**

- CRUD complet avec formulaire d'édition
- Gestion des adresses multiples
- Affichage des codes LIV/RAM
- Historique des livraisons par client
- Import/export de clients

#### 8. **📱 MOBILE: Implémenter la préparation du chargement** - **MANQUANT**

- ❌ Créer les 3 écrans (Surgelés, Frais, Ramasse)
- ❌ Liste triée "dernier livré / premier chargé"
- ❌ Check-list de validation (Complet/Partiel/Absent)
- ❌ Sauvegarde locale de l'état de préparation
- ❌ Indicateurs visuels de progression

#### 9. **🔧 BACKEND: Créer la gestion des véhicules**

- Créer entité Vehicle (immatriculation, capacité, statut TK)
- CRUD API pour les véhicules
- Association véhicule-tournée
- Gestion du statut (disponible, en maintenance, etc.)

#### 10. **🔔 Implémenter le système de notifications**

- Intégrer Firebase Cloud Messaging (FCM)
- Backend: Service d'envoi de notifications
- Mobile: Réception et affichage des notifications
- Types: Nouvelle tournée, incident, changement d'assignation
- Gestion des permissions et tokens

#### 11. **📄 BACKEND: Génération automatique des documents**

- Créer service de génération PDF (puppeteer ou similar)
- Template pour Document de réception
- Inclure: infos client, produits livrés, signature, heure
- Stockage automatique dans S3
- Endpoint de téléchargement sécurisé

### 📌 **PRIORITÉ BASSE** (Post-MVP ou optionnel)

#### 12. **📱 MOBILE: Implémenter la navigation multi-tournées**

- Système d'onglets pour switcher entre tournées
- État indépendant par tournée
- Indicateur de progression par tournée
- Synchronisation séparée par tournée
- Gestion mémoire optimisée

#### 13. **💻 FRONTEND: Interface de gestion des imports**

- Liste des imports avec statut et progression
- Détails des erreurs d'import
- Re-traitement des imports échoués
- Logs détaillés par import
- Statistiques d'import

#### 14. **📱 RÉCEPTIONNISTE: Créer l'interface dédiée**

- Page de contrôle des agrès par type
- Calcul automatique des écarts
- Système d'alertes au manager
- Liste des tournées terminées
- Commentaires sur les écarts

#### 15. **📍 Implémenter le tracking GPS**

- Mobile: Service de géolocalisation en arrière-plan
- Capture GPS lors des signatures
- Backend: Stockage des positions
- Frontend: Visualisation sur carte
- Respect RGPD et vie privée

#### 16. **💻 FRONTEND: Module de planification annuelle**

- Interface calendrier pour planification
- Gestion des 20 tournées fixes + 5 temporaires
- Templates de tournées récurrentes
- Gestion des congés/absences
- Export planning mensuel/annuel

#### 17. **📊 Créer le module de rapports et analytics**

- Backend: Agrégation des données
- Frontend: Tableaux de bord personnalisables
- Rapports: Performance chauffeur, taux livraison, agrès
- Export PDF des rapports
- Planification d'envoi automatique

#### 18. **🧪 Tests et qualité**

- Tests E2E du flow de livraison complet
- Tests unitaires des composants critiques
- Tests d'intégration API
- Tests de charge pour synchronisation
- Documentation technique complète

---
#### 19. **📋 Plan de développement MVP - 4 semaines **

## 🗓️ Vue d'ensemble
- **Début** : 09 juillet 2025
- **Fin MVP** : 06 août 2025
- **Équipe suggérée** : 2 devs full-stack + 1 dev mobile

---

## 📅 SEMAINE 1 (09-16 juillet) - Backend & Fondations Mobile

### ✅ TICKET 4 - Backend API Workflow complet [5 jours]
- [ ] **Module Packaging** (1.5 jours)
  - [ ] Créer `apps/backend/src/modules/packaging/packaging.module.ts`
  - [ ] Créer `apps/backend/src/modules/packaging/packaging.controller.ts`
  - [ ] Créer `apps/backend/src/modules/packaging/packaging.service.ts`
  - [ ] Créer entités `packaging-control.entity.ts` et `packaging-discrepancy.entity.ts`
  - [ ] Implémenter DTOs de validation

- [ ] **Endpoints Validation Chargement** (0.5 jour)
  - [ ] `POST /tours/:id/validate-loading`
  - [ ] `PUT /tours/:id/delivery-order`

- [ ] **Endpoints Workflow Livraison** (2 jours)
  - [ ] `POST /deliveries/:id/start`
  - [ ] `PUT /deliveries/:id/packaging` 
  - [ ] `PUT /deliveries/:id/payment`
  - [ ] `POST /deliveries/:id/photos` (avec upload S3)
  - [ ] `POST /deliveries/:id/signature` (avec upload S3)
  - [ ] `POST /deliveries/:id/complete`

- [ ] **Endpoints Gestion Agrès** (1 jour)
  - [ ] `GET /packaging/categories`
  - [ ] `GET /packaging/types`
  - [ ] `POST /packaging/control/before-loading`
  - [ ] `POST /packaging/control/after-delivery`
  - [ ] `GET /packaging/discrepancies`
  - [ ] Service calcul automatique des écarts

### ✅ TICKET 1 - Mobile Validation Chargement (début) [2 jours]
- [ ] **Setup et navigation** (0.5 jour)
  - [ ] Créer `apps/mobile/src/pages/driver/LoadValidation.tsx`
  - [ ] Configurer routing dans l'app

- [ ] **Composants UI** (1.5 jours)
  - [ ] Créer `LoadValidationTabs.tsx` avec IonSegment
  - [ ] Implémenter liste clients avec checkboxes
  - [ ] Ajouter calcul poids dynamique
  - [ ] Styling selon mockups

---

## 📅 SEMAINE 2 (17-23 juillet) - Mobile Chauffeur & Gestionnaire

### ✅ TICKET 1 - Mobile Validation Chargement (fin) [1 jour]
- [ ] **Logique métier** (1 jour)
  - [ ] Créer `apps/mobile/src/hooks/useLoadValidation.ts`
  - [ ] Créer `apps/mobile/src/store/slices/loadingSlice.ts`
  - [ ] Intégration API backend
  - [ ] Gestion mode hors ligne

### ✅ TICKET 2 - Mobile Process Livraison [4 jours]
- [ ] **Écran liste livraisons** (1 jour)
  - [ ] Créer `apps/mobile/src/pages/driver/DeliveryList.tsx`
  - [ ] Cards avec badges tournées
  - [ ] Bouton "ARRIVÉE" et navigation

- [ ] **Workflow dépose/ramasse agrès** (1 jour)
  - [ ] Créer `apps/mobile/src/pages/driver/DeliveryFlow/DepositPackages.tsx`
  - [ ] Créer `apps/mobile/src/pages/driver/DeliveryFlow/CollectPackages.tsx`
  - [ ] Composant `PackageQuantityControl.tsx` réutilisable
  - [ ] Animations checkboxes orange

- [ ] **Photo et commentaires** (1 jour)
  - [ ] Créer `apps/mobile/src/pages/driver/DeliveryFlow/PhotoComment.tsx`
  - [ ] Intégrer Capacitor Camera
  - [ ] Grille 2x2 pour photos
  - [ ] Limite 4 photos + compression

- [ ] **Signature électronique** (1 jour)
  - [ ] Créer `apps/mobile/src/pages/driver/DeliveryFlow/Signature.tsx`
  - [ ] Créer `apps/mobile/src/components/driver/SignaturePad.tsx`
  - [ ] Formulaire client (email, prénom, nom)
  - [ ] Génération PDF côté backend

### ✅ TICKET 3 - Mobile Gestionnaire Agrès [2 jours]
- [ ] **Page liste tournées** (0.5 jour)
  - [ ] Créer `apps/mobile/src/pages/warehouse/ToursList.tsx`
  - [ ] Cards avec infos chauffeur/horaire
  - [ ] Navigation bottom tab

- [ ] **Contrôle agrès** (1.5 jours)
  - [ ] Créer `apps/mobile/src/pages/warehouse/PackagingControl.tsx`
  - [ ] Réutiliser `PackageQuantityControl` du chauffeur
  - [ ] Bouton "TERMINER" au lieu de "SUIVANT"
  - [ ] Service synchronisation avec backend

---

## 📅 SEMAINE 3 (24-30 juillet) - Frontend Web & Alertes

### ✅ TICKET 5 - Frontend Dashboard Manager [3 jours]
- [ ] **Dashboard principal** (1.5 jours)
  - [ ] Refactorer `apps/frontend/src/pages/dashboard/Dashboard.tsx`
  - [ ] Créer `TourProgressBar.tsx` avec animations
  - [ ] Créer `CalendarPlanning.tsx` avec weekends/fériés
  - [ ] Créer `AvailableDrivers.tsx` avec drag & drop
  - [ ] Hook `usePolling.ts` pour refresh 30s

- [ ] **Page Contrôle Agrès** (1.5 jours)
  - [ ] Créer `apps/frontend/src/pages/packaging/PackagingControl.tsx`
  - [ ] Créer `DiscrepancyTable.tsx` avec tri/filtre
  - [ ] Styling lignes rouges pour écarts
  - [ ] Export Excel avec SheetJS
  - [ ] Intégration polling temps réel

### ✅ TICKET 6 - Système Alertes [3 jours]
- [ ] **Backend alertes** (1.5 jours)
  - [ ] Créer module `apps/backend/src/modules/notifications/`
  - [ ] Entity `alert.entity.ts` avec types/sévérité
  - [ ] Service email avec templates HTML
  - [ ] Logique création auto alertes écarts

- [ ] **Frontend alertes** (1.5 jours)
  - [ ] Créer `apps/frontend/src/store/slices/alertSlice.ts`
  - [ ] Créer `AlertCenter.tsx` avec badges
  - [ ] Hook `useAlertPolling.ts`
  - [ ] Intégration son notification (optionnel)
  - [ ] Marquage lu/non lu

---

## 📅 SEMAINE 4 (31 juillet - 06 août) - Finalisation & Tests

### 🧪 Tests & Corrections [3 jours]
- [ ] **Tests unitaires backend** (1 jour)
  - [ ] Tests controllers avec mocks
  - [ ] Tests services métier
  - [ ] Tests calcul écarts

- [ ] **Tests E2E mobile** (1 jour)
  - [ ] Workflow complet chauffeur
  - [ ] Workflow gestionnaire agrès
  - [ ] Mode hors ligne

- [ ] **Tests intégration frontend** (1 jour)
  - [ ] Dashboard polling
  - [ ] Export Excel
  - [ ] Drag & drop réaffectation

### 🚀 Déploiement & Documentation [2 jours]
- [ ] **Préparation production** (1 jour)
  - [ ] Variables environnement
  - [ ] Configuration S3
  - [ ] Build optimisé

- [ ] **Documentation** (1 jour)
  - [ ] README technique
  - [ ] Guide utilisateur
  - [ ] Procédure déploiement

---

## 📊 Répartition des ressources suggérée

### Dev 1 (Full-stack orienté backend)
- Semaine 1 : TICKET 4 complet
- Semaine 2 : Support TICKETS 2-3 (intégrations)
- Semaine 3 : TICKET 6 backend
- Semaine 4 : Tests backend + déploiement

### Dev 2 (Mobile)
- Semaine 1 : TICKET 1
- Semaine 2 : TICKET 2 + TICKET 3
- Semaine 3 : Support frontend + corrections mobile
- Semaine 4 : Tests E2E mobile

### Dev 3 (Frontend)
- Semaine 1 : Préparation composants communs
- Semaine 2 : Support mobile (composants partagés)
- Semaine 3 : TICKET 5 + TICKET 6 frontend
- Semaine 4 : Tests frontend + documentation

## ⚠️ Points d'attention
- Prioriser le workflow chauffeur (critique pour MVP)
- Tests en continu, pas seulement semaine 4
- Daily meetings pour synchronisation
- Demo client fin semaine 2 et 3
