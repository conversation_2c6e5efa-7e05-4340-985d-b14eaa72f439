{"name": "lrg-monorepo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "backend:dev": "pnpm --filter @lrg/backend run start:dev --preserveWatchOutput", "frontend:dev": "pnpm --filter @lrg/frontend run dev", "frontend:build": "pnpm --filter @lrg/frontend run build", "backend:build": "pnpm --filter @lrg/backend run build", "mobile:dev": "pnpm --filter @lrg/mobile run dev", "dev": "concurrently --kill-others-on-fail --restart-tries=3 --restart-after=2000 --max-restarts=5 -n \"BACKEND,FRONTEND,MOBILE\" -c \"bgBlue.bold,bgGreen.bold,bgMagenta.bold\" \"pnpm run backend:dev\" \"pnpm run frontend:dev\" \"pnpm run mobile:dev\"", "tidy": "pnpm --filter @lrg/backend run tidy && pnpm --filter @lrg/frontend run tidy && pnpm --filter @lrg/mobile run tidy", "lint": "pnpm --filter @lrg/backend run lint && pnpm --filter @lrg/frontend run lint && pnpm --filter @lrg/mobile run lint", "lint:cross-workspace": "eslint apps/*/src/**/*.{ts,tsx} --config eslint.config.js"}, "devDependencies": {"concurrently": "9.1.2"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.0.0", "pnpm": {"patchedDependencies": {"keycloak-js": "patches/keycloak-js.patch"}}, "dependencies": {}}