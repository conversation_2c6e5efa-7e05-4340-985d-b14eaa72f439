FROM node:22-alpine AS base

WORKDIR /app

# Enable and prepare pnpm 10.0.0
RUN corepack enable && \
    corepack prepare pnpm@10.0.0 --activate && \
    pnpm --version

# Stage for dependencies installation
FROM base AS deps

# Copy only package files first for better cache utilization
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/backend/package.json ./apps/backend/
COPY apps/frontend/package.json ./apps/frontend/
COPY patches/ ./patches/

# Install all dependencies with cache mount for pnpm store
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    pnpm install --frozen-lockfile


# Frontend builder
FROM deps AS frontend-builder

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/frontend/node_modules ./apps/frontend/node_modules

# Copy frontend source
COPY apps/frontend/ ./apps/frontend/

# Build frontend with cache mount
RUN pnpm run frontend:build

# Backend builder
FROM deps AS backend-builder

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/backend/node_modules ./apps/backend/node_modules

# Copy backend source
COPY apps/backend/ ./apps/backend/

RUN pnpm run backend:build

# Production dependencies - OPTIMISÉ
FROM base AS production-deps

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/backend/package.json ./apps/backend/
COPY patches/ ./patches/

# Install only production dependencies with cache mount
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store \
    pnpm install --frozen-lockfile --prod --ignore-scripts --no-optional  && \
    # Supprimer les fichiers inutiles en production
    find . -name "*.md" -type f -delete && \
    find . -name "*.ts" -type f -delete && \
    find . -name "*.map" -type f -delete && \
    find . -name ".npmignore" -type f -delete && \
    find . -name "LICENSE*" -type f -delete && \
    find . -name "CHANGELOG*" -type f -delete && \
    find . -name "test" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "__tests__" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "*.test.js" -type f -delete && \
    find . -name "*.spec.js" -type f -delete

# NOUVEAU: Image finale avec distroless (plus légère)
FROM gcr.io/distroless/nodejs22-debian12 AS production

WORKDIR /app

# Copy seulement les fichiers nécessaires
COPY --from=production-deps /app/node_modules ./node_modules
COPY --from=production-deps /app/apps/backend/node_modules ./apps/backend/node_modules

# Copy built artifacts
COPY --from=backend-builder /app/apps/backend/dist ./apps/backend/dist
COPY --from=frontend-builder /app/apps/frontend/dist ./apps/backend/client

# Copy backend assets (email templates, etc.)
COPY apps/backend/asset ./apps/backend/asset

# Les package.json sont nécessaires pour la résolution des modules
COPY --from=production-deps /app/apps/backend/package.json ./apps/backend/

EXPOSE 3000

# Distroless utilise un user non-root par défaut
CMD ["apps/backend/dist/main.js"]