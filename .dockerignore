# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Build output
build/
dist/
.next/
.nuxt/
.cache/
.vercel/
.turbo/

# Environment variables
.env
.env.*
!.env.example

# IDE and editor files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Testing
coverage/
.nyc_output/

# Logs
logs
*.log

# Local development
.cache/
.temp/
.tmp/

# System Files
Thumbs.db

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Git
.git/
.gitignore

# Application specific
apps/frontend/.next/
apps/mobile/android/
apps/mobile/ios/
apps/mobile/build/
apps/mobile/node_modules/
apps/frontend/node_modules/
apps/backend/node_modules/

# Misc
*.md
LICENSE

services/
.dev-resources/