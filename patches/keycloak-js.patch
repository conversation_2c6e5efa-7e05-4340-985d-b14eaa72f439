diff --git a/lib/keycloak.d.ts b/lib/keycloak.d.ts
index 507d31ecaf7d4d481c4b18f1545db78ef8d9edaf..ed5ab86338db014db033a70602ab892f3f14c686 100644
--- a/lib/keycloak.d.ts
+++ b/lib/keycloak.d.ts
@@ -18,6 +18,42 @@
  * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
  * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  */
+
+// Internal Promise type - not meant for direct use
+export interface KeycloakPromiseWrapper<TSuccess, TError> {
+	setSuccess(result: TSuccess): void;
+	setError(result: TError): void;
+	readonly promise: Promise<TSuccess>;
+}
+
+// Internal Callback type - not meant for direct use
+export interface KeycloakCallback<TSuccess, TError> {
+	(result: TSuccess | TError): void;
+
+}
+
+// Internal OAuth result type - not meant for direct use
+export interface KeycloakOAuthResult {
+	code?: string;
+	error?: string;
+	error_description?: string;
+	prompt?: 'none' | 'login' | 'consent';
+	access_token?: string;
+	id_token?: string;
+	state?: string;
+	session_state?: string;
+	expires_in?: number;
+	kc_action_status?: string;
+	kc_action?: string;
+	iss?: string;
+	valid?: boolean;
+	redirectUri?: string;
+	storedNonce?: string;
+	pkceCodeVerifier?: string;
+	newUrl?: string;
+	loginOptions?: KeycloakLoginOptions;
+}
+
 export type KeycloakOnLoad = 'login-required'|'check-sso';
 export type KeycloakResponseMode = 'query'|'fragment';
 export type KeycloakResponseType = 'code'|'id_token token'|'code id_token token';
@@ -650,6 +686,26 @@ declare class Keycloak {
 	* @private Undocumented.
 	*/
 	loadUserInfo(): Promise<{}>;
+
+	/**
+	 * @private Internal method for creating the promise structure used by Keycloak.
+	 */
+	createPromise<TSuccess, TError>(): KeycloakPromiseWrapper<TSuccess, TError>;
+
+	/**
+	 * @private Internal method for parsing callbacks.
+	 */
+	parseCallback(url: string): KeycloakOAuthResult | undefined;
+
+	/**
+	 * @private Internal method for processing callbacks.
+	 */
+	processCallback<TSuccess, TError>(oauth: KeycloakOAuthResult | undefined, promise: KeycloakPromiseWrapper<TSuccess, TError> | undefined): void;
+
+	/**
+	 * @private Internal method for setting the token.
+	 */
+	setToken(token: string, refreshToken: string, idToken: string): void;
 }
 
 export default Keycloak;
diff --git a/lib/keycloak.js b/lib/keycloak.js
index 47225f4dd433948e0bdf5e7523188f8612bc333d..89541a2bdb27f1433d2ab67f6d642d531ffe2fc6 100644
--- a/lib/keycloak.js
+++ b/lib/keycloak.js
@@ -708,6 +708,12 @@ function Keycloak (config) {
         }
     }
 
+    // expose internal methods needed for custom adapters
+    kc.createPromise = createPromise;
+    kc.parseCallback = parseCallback;
+    kc.processCallback = processCallback;
+    kc.setToken = setToken;
+
     function getRealmUrl() {
         if (typeof kc.authServerUrl !== 'undefined') {
             if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {
