# Product Requirements Document (PRD)

## Dématérialisation des Bons de Livraison LRG - MVP

**Version:** 1.0  
**Date:** Janvier 2025  
**Statut:** En cours de validation  

---

## 1. Résumé Exécutif

### 1.1 Vision Produit

Remplacer le système papier actuel des bons de livraison par une solution digitale moderne permettant aux chauffeurs de gérer leurs tournées via une application mobile, aux réceptionnistes de contrôler les agrès, et aux managers de superviser l'ensemble des opérations en temps réel.

### 1.2 Objectifs MVP

- **Digitaliser** le processus de livraison de bout en bout
- **Simplifier** le travail des chauffeurs sur le terrain
- **Tracer** en temps réel l'état des tournées et livraisons
- **Sécuriser** la gestion des agrès et des paiements

### 1.3 Périmètre MVP

Le MVP couvre les fonctionnalités essentielles pour remplacer le processus papier actuel tout en apportant une valeur immédiate aux utilisateurs terrain et au management.

---

## 2. Contexte et Problématique

### 2.1 Situation Actuelle

- **20 tournées fixes** quotidiennes gérées sur papier
- **~250 livraisons/jour** avec bons de livraison papier
- Gestion manuelle des agrès (palettes, rolls, caisses)
- Pas de visibilité temps réel pour le management
- Ressaisie manuelle des informations

### 2.2 Problèmes Identifiés

- ❌ Perte de temps dans la gestion administrative
- ❌ Erreurs de saisie et de comptage des agrès
- ❌ Manque de traçabilité des livraisons
- ❌ Impossibilité de réagir rapidement aux incidents
- ❌ Difficulté à gérer les non-paiements

### 2.3 Opportunités

- ✅ Moderniser l'image de l'entreprise
- ✅ Réduire l'empreinte carbone (zéro papier)
- ✅ Améliorer la satisfaction client
- ✅ Optimiser les processus opérationnels

---

## 3. Utilisateurs et Personas

### 3.1 Chauffeur

**Profil:** Acteur terrain, 20 chauffeurs principaux + 5 temporaires  
**Besoins principaux:**

- Interface simple et intuitive sur mobile
- Accès rapide aux informations de livraison
- Saisie facilitée des données terrain
- Gestion des incidents en temps réel

**Frustrations actuelles:**

- Paperasse importante
- Ressaisie des informations
- Manque de flexibilité dans l'ordre des livraisons

### 3.2 Réceptionniste (Gestionnaire des agrès)

**Profil:** Sédentaire, contrôle les agrès au retour  
**Besoins principaux:**

- Saisie rapide des quantités d'agrès
- Détection automatique des écarts
- Vision des tournées terminées

**Frustrations actuelles:**

- Comptage manuel fastidieux
- Écarts découverts tardivement

### 3.3 Manager

**Profil:** Superviseur, gère la planification et le suivi  
**Besoins principaux:**

- Vue d'ensemble en temps réel
- Gestion des incidents et réaffectations
- Tableaux de bord de suivi

**Frustrations actuelles:**

- Pas de visibilité temps réel
- Réactivité limitée aux problèmes

---

## 4. Fonctionnalités MVP

### 4.1 Application Mobile - Chauffeur

#### 4.1.1 Authentification

- Connexion SSO via Microsoft Entra ID
- Gestion de session sécurisée
- Mode hors-ligne avec synchronisation

#### 4.1.2 Écran d'Accueil

- Affichage de la/les tournée(s) du jour
- Numéro de camion associé
- Bouton "Commencer la tournée"

#### 4.1.3 Workflow de Tournée

**A. Phase de Chargement**

- Liste des clients par ordre de livraison inversé
- Validation ligne par ligne (checkbox)
- Indicateur de paiement comptant ($)
- Pas de comptage d'agrès à cette étape

**B. Phase de Livraison**

- Liste des 3 prochaines livraisons
- Pour chaque livraison :
  - Nom et adresse du client
  - Horaire de livraison
  - Statut de paiement
  - Lien GPS (Waze/Google Maps)
- Choix : "Livraison effective" ou "Client absent"

**C. Gestion des Agrès**

- 3 onglets : Surgelés / Frais / Ramasse
- Saisie des quantités par type d'agrès
- Champs obligatoires pour la dépose

**D. Validation de Livraison**

- Signature électronique obligatoire
- Saisie email client (sauvegardé pour futures livraisons)
- Prise de photo(s) si client absent
- Commentaire optionnel (256 caractères max)

#### 4.1.4 Gestion des Incidents

- Bouton accessible à tout moment
- Types d'incidents :
  - Livraison partielle
  - Livraison impossible
  - Refus du client
  - Incident camion
- Écran de confirmation pour éviter les erreurs

### 4.2 Application Mobile - Réceptionniste

#### 4.2.1 Sélection des Tournées

- Calendrier pour sélection de la date
- Liste des tournées du jour

#### 4.2.2 Contrôle des Agrès

- Saisie par type d'agrès (palette, roll, caisse)
- Affichage du total en gros caractères
- Détection automatique des écarts avec déclaration chauffeur
- Alerte manager en cas d'écart

### 4.3 Interface Web - Manager

#### 4.3.1 Dashboard Principal

- Vue synthétique des tournées en cours avec rafraîchissement automatique (polling toutes les minutes)
- Progression en pourcentage par tournée
- Nombre de livraisons complétées vs total
- Alertes incidents et non-paiements
- Statistiques du jour
- Note: Pas besoin de temps réel strict, un rafraîchissement périodique est suffisant

#### 4.3.2 Gestion des Tournées

- Liste des tournées avec filtres
- Assignation chauffeur ↔ tournée
- Réaffectation en cas d'incident
- Consultation temps réel des livraisons

#### 4.3.3 Gestion des Incidents

- Notifications en temps réel
- Actions possibles :
  - Réaffecter une tournée
  - Annuler une livraison
  - Valider un écart d'agrès

---

## 5. Spécifications Techniques

### 5.1 Architecture Globale

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  App Mobile     │     │  App Mobile     │     │  Interface Web  │
│  Chauffeur      │     │  Réceptionniste │     │  Manager        │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                         │
         └───────────────────────┴─────────────────────────┘
                                 │
                          ┌──────▼──────┐
                          │   API REST  │
                          │   NestJS    │
                          └──────┬──────┘
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
         ┌──────▼──────┐  ┌──────▼──────┐  ┌────▼────┐
         │ PostgreSQL  │  │  Amazon S3  │  │   FTP   │
         │     BDD     │  │   Fichiers  │  │   XML   │
         └─────────────┘  └─────────────┘  └─────────┘
```

### 5.2 Stack Technique

- **Frontend Web:** React 18
- **Mobile:** Ionic 7 + React 18 + TailwindCSS
- **Backend:** NestJS 10 (Node 18)
- **Base de données:** PostgreSQL 15
- **Stockage:** Amazon S3
- **Authentification:** Microsoft Entra ID (SSO)

### 5.3 Intégration des Données

- Import XML quotidien via FTP
- Mapping automatique vers les tables PostgreSQL
- Format des tournées : Tour_XXXX_DateLivr_AAAAMMJJ.xml

---

## 6. Modèle de Données Simplifié

### 6.1 Entités Principales

#### Tour (Tournée)

- `tour_number`: Identifiant unique (ex: 4011)
- `delivery_date`: Date de livraison
- `status`: planned | in_progress | completed
- `truck_id`: Camion assigné
- `driver_id`: Chauffeur assigné

#### Stop (Arrêt/Livraison)

- `tour_id`: Référence à la tournée
- `client_id`: Client à livrer
- `sequence`: Ordre dans la tournée
- `delivery_window`: Créneau horaire
- `status`: planned | completed | cancelled
- `payment_status`: paid | not_paid | partial

#### Agre Movement (Mouvement d'Agrès)

- `stop_id`: Livraison concernée
- `agre_type`: Type (palette, roll, caisse)
- `quantity_declared`: Quantité chauffeur
- `quantity_verified`: Quantité réceptionniste
- `has_discrepancy`: Écart détecté

#### Incident

- `tour_id`: Tournée concernée
- `stop_id`: Livraison concernée (optionnel)
- `type`: Type d'incident
- `description`: Commentaire
- `requires_action`: Nécessite action manager

---

## 7. Workflows Principaux

### 7.1 Workflow Livraison Standard

```mermaid
graph LR
    A[Chargement] --> B[En Route]
    B --> C[Arrivée Client]
    C --> D{Client Présent?}
    D -->|Oui| E[Dépose Agrès]
    D -->|Non| F[Photos + Dépose]
    E --> G[Ramasse Agrès]
    F --> G
    G --> H[Signature/Validation]
    H --> I[Livraison Suivante]
```

### 7.2 Workflow Gestion Incident

```mermaid
graph TD
    A[Incident Déclaré] --> B{Type?}
    B -->|Mineur| C[Notification Manager]
    B -->|Majeur| D[Alerte Urgente]
    C --> E[Poursuite Tournée]
    D --> F{Action Manager}
    F -->|Réaffectation| G[Nouveau Chauffeur]
    F -->|Annulation| H[Tournée Annulée]
```

---

## 8. Critères de Succès MVP

### 8.1 KPIs Opérationnels

- ✅ 100% des tournées digitalisées
- ✅ Temps de saisie < 2 min par livraison
- ✅ Taux d'adoption > 90% des chauffeurs
- ✅ Réduction de 50% des erreurs d'agrès

### 8.2 Critères d'Acceptance

- [ ] Workflow complet sans papier
- [ ] Synchronisation temps réel
- [ ] Fonctionnement hors-ligne
- [ ] Génération automatique des BL PDF
- [ ] Traçabilité complète des livraisons

---

## 9. Contraintes et Limitations MVP

### 9.1 Hors Périmètre MVP

- ❌ Optimisation automatique des tournées
- ❌ Calcul d'itinéraires
- ❌ Gestion des plannings annuels
- ❌ Statistiques avancées
- ❌ Gestion détaillée des types d'agrès lors du chargement

### 9.2 Contraintes Techniques

- Connexion internet requise pour synchronisation
- Smartphones Android 10+ / iOS 14+
- Navigateurs Chrome/Edge pour interface manager

---

## 10. Planning de Livraison

### 10.1 Jalons Principaux

- **Sprint 1-2** (Juin 2025): Core API + Authentification
- **Sprint 3-4** (Juillet 2025): Apps mobiles + Interface manager
- **Tests** (1-14 Août 2025): Recette fonctionnelle
- **Pilote** (15 Août 2025): Déploiement avec chauffeurs volontaires

### 10.2 Stratégie de Déploiement

1. Phase pilote avec 3-5 chauffeurs
2. Corrections et ajustements
3. Déploiement progressif par tournée
4. Formation continue des utilisateurs

---

## 11. Risques et Mitigation

| Risque | Impact | Probabilité | Mitigation |
|--------|--------|-------------|------------|
| Résistance au changement | Élevé | Moyen | Formation approfondie, ambassadeurs |
| Problèmes de connectivité | Élevé | Faible | Mode hors-ligne robuste |
| Erreurs d'import XML | Moyen | Faible | Validation et alertes automatiques |
| Adoption réceptionnistes | Moyen | Moyen | Interface ultra-simple, formation |

---
