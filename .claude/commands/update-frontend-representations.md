sur le backend on defini des entités et des controlleurs qui sont répercutés sous forme de services dans le frontend et sur l'app mobile

Assure toi que les services et interfaces du frontend et de l'app mobile sont à jour avec les entités et les controlleurs du backend.

pour cela dans un premier temps verifie les interfaces, enums et les services dans le dossier frontend/src/interfaces et frontend/src/lib/api-service

met a jour le differentiel en suivant bien le pattern existant sur le frontend.

Compact ton context.

fait de même pour l'app mobile, les interfaces sont dans le dossier mobile/src/interfaces et les services dans le dossier mobile/src/services
