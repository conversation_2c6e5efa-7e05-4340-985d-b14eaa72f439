---
description: When createing or updateing on react hooks
globs: 
alwaysApply: false
---
# Mobile Custom Hooks Patterns

## Hook Design Principles

### Naming Convention
- Préfixe obligatoire `use` (ex: `usePagination`, `useAuth`)
- Nom descriptif de la fonctionnalité (camelCase)
- Suffixe optionnel pour spécialisation (`useApiPagination`, `useFormValidation`)

### File Organization
- Un hook par fichier dans `src/hooks/`
- Export par défaut du hook principal
- Export nommé des types/interfaces associés

## Pagination Hook Pattern

### Structure Standard
```typescript
interface UseHookNameProps {
  // Props d'initialisation
}

interface UseHookNameReturn {
  // État exposé
  // Actions/handlers
  // Utilitaires
}

export const useHookName = (props: UseHookNameProps): UseHookNameReturn => {
  // Implémentation
}
```

Référence: [usePagination.ts](mdc:apps/mobile/src/hooks/usePagination.ts)

### Pagination Hook Specifics
- État local pour les paramètres de pagination
- État séparé pour les métadonnées de pagination
- Handlers pour page, limit, et tri
- Reset automatique de la page lors du changement de limite
- Toggle du tri sur le même champ

## State Management in Hooks

### Local State Patterns
- `useState` pour l'état simple du hook
- `useReducer` pour l'état complexe avec actions multiples
- État immutable avec spread operator ou Immer

### Side Effects
- `useEffect` pour les effets de bord
- Cleanup functions pour éviter les memory leaks
- Dependencies array optimisé

## API Integration Hooks

### Data Fetching Pattern
```typescript
export const useApiData = <T>(endpoint: string, params?: any) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch logic with error handling
  // Return { data, loading, error, refetch }
}
```

### Pagination + API Pattern
- Combinaison du hook pagination avec fetch
- Gestion automatique du loading state
- Refetch automatique lors du changement de paramètres
- Cache optionnel pour les données

## Form Hooks Integration

### React Hook Form Pattern
- Intégration avec `useForm` de React Hook Form
- Validation avec Zod schemas
- Gestion d'erreurs unifiée
- Submit handlers avec loading states

### Custom Form Hooks
```typescript
export const useFormWithValidation = <T>(schema: ZodSchema<T>) => {
  const form = useForm<T>({
    resolver: zodResolver(schema)
  });
  
  // Additional form logic
  // Return enhanced form object
}
```

## Authentication Hooks

### Auth State Hook
- Intégration avec Redux store
- Selectors pour l'état d'authentification
- Actions pour login/logout
- Token refresh automatique

### Protected Route Hook
```typescript
export const useAuthGuard = (requiredRoles?: string[]) => {
  // Check authentication status
  // Verify roles if provided
  // Return { isAuthenticated, hasPermission, user }
}
```

## Storage Hooks

### Capacitor Preferences Hook
```typescript
export const useStoredState = <T>(key: string, defaultValue: T) => {
  // Sync with Capacitor Preferences
  // Return [value, setValue] like useState
  // Automatic serialization/deserialization
}
```

### Cache Hook Pattern
- Gestion du cache local
- TTL (Time To Live) pour l'expiration
- Invalidation manuelle du cache

## Performance Optimization

### Memoization Patterns
- `useMemo` pour les calculs coûteux
- `useCallback` pour les fonctions passées en props
- Dépendances optimisées pour éviter les re-renders

### Debouncing Hook
```typescript
export const useDebounce = <T>(value: T, delay: number): T => {
  // Debounce implementation
  // Useful for search inputs, API calls
}
```

## Error Handling in Hooks

### Error Boundary Integration
- Hooks qui propagent les erreurs vers Error Boundaries
- Gestion gracieuse des erreurs async
- Retry mechanisms pour les échecs temporaires

### Validation Hooks
- Validation en temps réel
- Messages d'erreur localisés
- État de validation exposé

## Testing Custom Hooks

### Testing Patterns
- `@testing-library/react-hooks` pour les tests isolés
- Mock des dépendances externes (API, storage)
- Tests des edge cases et error states

### Test Structure
```typescript
describe('useHookName', () => {
  it('should initialize with default values', () => {
    // Test initial state
  });
  
  it('should handle actions correctly', () => {
    // Test state changes
  });
  
  it('should handle errors gracefully', () => {
    // Test error scenarios
  });
});
```

## Hook Composition

### Combining Hooks
- Composition de hooks simples en hooks complexes
- Éviter la duplication de logique
- Maintenir la séparation des responsabilités

### Higher-Order Hooks
- Hooks qui retournent d'autres hooks
- Pattern pour la réutilisabilité
- Configuration dynamique des hooks
