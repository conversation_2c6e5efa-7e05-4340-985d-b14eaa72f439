---
description: 
globs: **/mobile/**/*
alwaysApply: false
---
# Mobile Ionic React Patterns

## Ionic Components Usage

### Setup et Configuration
- Utiliser `setupIonicReact()` dans App.tsx avec configuration globale
- Mode 'md' (Material Design) par défaut
- Support du swipe back et animations activés

Référence: [App.tsx](mdc:apps/mobile/src/App.tsx)

### Navigation Patterns
- `IonReactRouter` comme router principal
- `IonRouterOutlet` pour le contenu principal
- Utilisation de `useHistory` pour la navigation programmatique
- Support des directions de navigation (`forward`, `back`, `root`, `none`)

### Layout Components
- Structure avec `IonApp` > `IonReactRouter` > `IonRouterOutlet`
- Composants Layout réutilisables pour la structure commune
- Séparation Header/Menu/Content dans des composants dédiés

### Button Component Patterns
- Composant Button polyvalent supportant :
  - Boutons standards avec onClick
  - Navigation avec history.push
  - Liens Ionic avec routerLink
- Props Ionic natives : `expand`, `fill`, `size`, `color`
- Gestion des directions de navigation

Référence: [Button.tsx](mdc:apps/mobile/src/components/ui/Button.tsx)

## Form Components

### Form Field Factory Pattern
- Composant `FormField` comme factory pour différents types d'inputs
- Switch/case pour rendre le bon composant selon le type
- Props communes : `name`, `required`, `error`, `register`
- Support React Hook Form intégré

Référence: [FormField.tsx](mdc:apps/mobile/src/components/ui/FormField.tsx)

### Input Components
- Composants spécialisés : `Input`, `Select`, `TextArea`, `Checkbox`, `RadioGroup`
- Intégration React Hook Form avec prop `register`
- Gestion d'erreurs unifiée
- Styling TailwindCSS + Ionic

## Styling Patterns

### CSS Imports Order
1. Core Ionic CSS (obligatoire)
2. CSS de base Ionic (normalize, structure, typography)
3. Utilitaires Ionic optionnels
4. Thème dark mode
5. CSS personnalisé

### TailwindCSS Integration
- Classes Tailwind pour le layout et spacing
- Classes Ionic pour les composants spécifiques
- Classes personnalisées pour les cas particuliers

## Capacitor Integration

### Plugins Usage
- `@capacitor/preferences` pour le stockage local
- `@capacitor/app`, `@capacitor/haptics`, `@capacitor/keyboard` pour les fonctionnalités natives
- `@capacitor/network` pour la détection réseau
- Configuration dans `capacitor.config.ts`

### Storage Patterns
- Service wrapper pour Capacitor Preferences
- Sérialisation JSON pour les objets complexes
- Gestion async/await pour toutes les opérations

## Menu et Navigation

### Tab Navigation
- Composant `Tabs` centralisé
- Configuration des routes avec métadonnées (`showInTabMenu`)
- Icônes Ionicons pour cohérence visuelle

### Menu Patterns
- Menu latéral avec `IonMenu`
- Filtrage des routes selon les permissions
- État actif basé sur l'URL courante

## Performance Patterns

### Lazy Loading
- Import dynamique des pages lourdes
- Suspense pour les états de chargement
- Code splitting par route

### State Optimization
- Redux pour l'état global partagé
- useState local pour l'état de composant
- Memoization avec useMemo/useCallback si nécessaire

## Error Handling

### API Errors
- Gestion centralisée dans ApiService
- Parsing des réponses d'erreur JSON/text
- Propagation des erreurs avec messages explicites

### Form Validation
- Validation côté client avec Zod
- Messages d'erreur localisés
- Validation en temps réel avec React Hook Form

## Testing Patterns

### Component Testing
- Testing Library React pour les composants
- Jest pour les tests unitaires
- Cypress pour les tests E2E

### Service Testing
- Mocking des services externes
- Tests d'intégration pour les flows complets
- Tests de régression pour les bugs critiques
