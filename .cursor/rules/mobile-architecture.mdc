---
description: 
globs: **/mobile/**/*
alwaysApply: false
---
# Mobile Application Architecture Rules

## Project Structure

L'application mobile suit une architecture en couches avec une séparation claire des responsabilités :

### Structure des dossiers
```
src/
├── components/          # Composants réutilisables
│   ├── ui/             # Composants UI de base
│   └── [Layout, Menu, etc.] # Composants de layout
├── pages/              # Pages/écrans de l'application
├── services/           # Services métier et API
├── stores/             # Gestion d'état Redux
├── hooks/              # Hooks personnalisés
├── interfaces/         # Types TypeScript
│   ├── entity/         # Entités métier
│   ├── dto/           # Data Transfer Objects
│   └── enum/          # Énumérations
├── config/            # Configuration et constantes
├── plugin/            # Plugins externes (Keycloak, etc.)
└── utils/             # Utilitaires

```

## Technologies Stack

- **Framework**: Ionic React avec Capacitor
- **State Management**: Redux Toolkit
- **Forms**: React Hook Form avec Zod
- **Authentication**: Keycloak
- **Styling**: TailwindCSS + Ionic CSS
- **HTTP Client**: Fetch API avec service wrapper
- **TypeScript**: Configuration stricte

## Naming Conventions

### Fichiers et Dossiers
- **Composants**: PascalCase (`Button.tsx`, `FormField.tsx`)
- **Services**: PascalCase avec suffixe Service (`ApiService.ts`, `UserService.ts`)
- **Hooks**: camelCase avec préfixe use (`usePagination.ts`)
- **Interfaces**: PascalCase avec préfixe I (`IUserEntity`, `IClientEntity`)
- **Enums**: kebab-case (`import-status.enum.ts`)
- **DTOs**: kebab-case avec suffixe dto (`user-update-request.dto.ts`)

### Variables et Fonctions
- **Variables**: camelCase descriptif (`paginationParams`, `currentUserState`)
- **Fonctions**: camelCase avec verbes d'action (`handlePageChange`, `updateCurrentUser`)
- **Constantes**: UPPER_SNAKE_CASE (`API_URL`, `DEFAULT_LIMIT`)

## Component Patterns

### Composants UI
- Utiliser des interfaces TypeScript pour les props
- Valeurs par défaut dans la destructuration
- Documentation JSDoc avec exemples d'utilisation
- Props optionnelles avec valeurs par défaut sensées

Exemple référence: [Button.tsx](mdc:apps/mobile/src/components/ui/Button.tsx)

### Composants de Page
- Un composant par page dans le dossier `pages/`
- Utilisation des hooks Ionic pour la navigation
- Gestion d'état locale avec useState ou Redux selon la portée

## Service Layer Patterns

### API Service
- Service centralisé pour toutes les requêtes HTTP
- Gestion automatique de l'authentification
- Support de la pagination intégré
- Gestion d'erreurs standardisée

Référence: [ApiService.ts](mdc:apps/mobile/src/services/ApiService.ts)

### Services Métier
- Un service par domaine métier
- Utilisation du service API centralisé
- Méthodes async/await
- Gestion des erreurs propagée

## State Management

### Redux Toolkit
- Un slice par domaine
- Actions async avec createAsyncThunk
- État typé avec TypeScript
- Persistence avec Capacitor Preferences

Référence: [currentUserSlice.ts](mdc:apps/mobile/src/stores/currentUserSlice.ts)

## TypeScript Patterns

### Interfaces
- Préfixe `I` pour les interfaces
- Héritage d'interfaces de base (`IBaseDomainEntity`)
- Séparation entity/dto/enum dans des dossiers dédiés

### Types
- Export des types depuis les stores (`RootState`, `AppDispatch`)
- Utilisation de types génériques pour la réutilisabilité
- Types stricts sans `any` (sauf exceptions justifiées)

## Authentication & Security

### Keycloak Integration
- Service dédié pour l'authentification
- Context Provider pour l'état d'authentification
- Token refresh automatique
- Logout avec nettoyage du state

Référence: [keycloak-service.ts](mdc:apps/mobile/src/plugin/keycloak/keycloak-service.ts)

## Routing & Navigation

### Configuration des Routes
- Centralisation dans `config/routes.ts`
- Métadonnées pour les menus et permissions
- Fonctions utilitaires pour filtrer les routes

Référence: [routes.ts](mdc:apps/mobile/src/config/routes.ts)

## Code Quality

### ESLint Rules
- Configuration stricte avec TypeScript
- Prettier intégré
- Règles de padding entre statements
- Interdiction des imports cross-workspace

### Best Practices
- Noms de variables/fonctions auto-documentés
- Commentaires uniquement si pertinents
- Principes SOLID
- Séparation des responsabilités
