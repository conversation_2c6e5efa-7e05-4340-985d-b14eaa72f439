---
description: When working on pagination on backend
globs: 
alwaysApply: false
---
# Système de Pagination Backend - Architecture et Utilisation

## Vue d'ensemble
Le backend implémente un système de pagination uniforme et réutilisable basé sur TypeORM et NestJS, suivant le pattern de conception SOLID avec séparation des responsabilités.

## 🏗️ Structure des composants

### 1. **DTOs de Pagination** ([pagination.dto.ts](mdc:apps/backend/src/application/dto/pagination.dto.ts))

#### PaginationParamsDto
DTO pour les paramètres d'entrée de pagination récupérés depuis la query string :
- `page?: number = 1` - Numéro de page (défaut: 1)
- `limit?: number = 10` - Nombre d'éléments par page (défaut: 10)
- `sortBy?: string` - Champ de tri
- `sortOrder?: 'asc' | 'desc' = 'asc'` - Ordre de tri

**Méthode utilitaire** :
- `toTypeOrmOptions()` - Convertit les paramètres en options TypeORM (`skip`, `take`, `order`)

#### PaginationMetaDto
Métadonnées de pagination pour la réponse :
- `page`, `limit`, `totalItems`, `totalPages`
- `hasNextPage`, `hasPreviousPage` - Booléens de navigation

#### PaginatedResponseDto<T>
Structure de réponse standardisée :
- `items: T[]` - Tableau des éléments paginés
- `meta: PaginationMetaDto` - Métadonnées de pagination

### 2. **Décorateur de Requête** ([pagination.decorator.ts](mdc:apps/backend/src/infrastructure/decorator/pagination.decorator.ts))

#### @PaginationQuery
Décorateur NestJS extrayant automatiquement les paramètres de pagination :
```typescript
@Get()
async findAll(@PaginationQuery() pagination: PaginationParamsDto): Promise<PaginatedResponseDto<Entity>> {
  // ...
}
```

Utilise `plainToInstance` pour valider et transformer les query params en DTO typé.

### 3. **Adaptateur de Pagination** ([pagination-adapter.ts](mdc:apps/backend/src/application/pagination/pagination-adapter.ts))

#### PaginationAdapter<T>
Classe générique encapsulant la logique de pagination TypeORM :
- **Constructeur** : Prend un `Repository<T>` TypeORM
- **Méthode `paginate()`** : 
  - Paramètres : `PaginationParamsDto` + options TypeORM additionnelles
  - Retourne : `{ items: T[], meta: PaginationMetaDto }`
  - Utilise `findAndCount()` pour récupérer données + total

**Factory function** `createPaginationAdapter<T>()` pour instanciation simplifiée.

### 4. **Builder de Réponse** ([list-builder.ts](mdc:apps/backend/src/application/dto/list-builder.ts))

#### ListBuilder<T, DTO>
Pattern Builder fluide pour construire les réponses paginées :
```typescript
const response = new ListBuilder(EntityDto)
  .items(entities)
  .meta(paginationMeta)
  .build();
```

- **Transformation automatique** : Utilise `plainToInstance` pour sérialiser les entités en DTOs
- **Type-safe** : Garantit la cohérence entre entité et DTO de réponse

## 🚀 Utilisation pratique

### Dans un Service ([client.service.ts](mdc:apps/backend/src/application/service/client.service.ts))
```typescript
@Injectable()
export class ClientService {
  private readonly paginationAdapter: PaginationAdapter<ClientEntity>;

  constructor(private readonly clientRepository: ClientRepository) {
    this.paginationAdapter = new PaginationAdapter(clientRepository);
  }

  async findAll(pagination: PaginationParamsDto): Promise<{ items: ClientEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(pagination);
  }
}
```

### Dans un Controller ([tour-assignment-manager.controller.ts](mdc:apps/backend/src/infrastructure/controller/manager/tour-assignment-manager.controller.ts))
```typescript
@Get()
@ApiOperation({ summary: 'Get all tour assignments' })
@ApiResponse({ status: 200, type: PaginatedResponseDto })
async findAll(@PaginationQuery() pagination: PaginationParamsDto): Promise<PaginatedResponseDto<TourAssignmentEntity>> {
  const { items, meta } = await this.service.findAllPaginated(pagination);
  return new ListBuilder(TourAssignmentEntity).items(items).meta(meta).build();
}
```

## 🔧 Options avancées

### Pagination avec filtres personnalisés
```typescript
async getPaginatedToursByDate(date: string, pagination: PaginationParamsDto) {
  return this.paginationAdapter.paginate(pagination, {
    where: { deliveryDate: date },
    relations: ['assignments']
  });
}
```

## 📋 Format de requête API

**Query Parameters** :
- `?page=2&limit=20&sortBy=createdAt&sortOrder=desc`

**Réponse JSON** :
```json
{
  "items": [...],
  "meta": {
    "page": 2,
    "limit": 20,
    "totalItems": 150,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPreviousPage": true
  }
}
```

## 🛡️ Avantages de l'architecture

- **Réutilisabilité** : Un seul adapter pour toutes les entités
- **Type Safety** : DTOs validés avec class-validator
- **Consistance** : Format de réponse uniforme dans toute l'API
- **Flexibilité** : Support des filtres et relations TypeORM
- **Séparation des responsabilités** : Décorateur → Service → Adapter → Repository
