---
description: 
globs: **/backend/**/*
alwaysApply: false
---
# Architecture Backend - Structure et Organisation

## Vue d'ensemble
Le backend suit une **architecture hexagonale** (Clean Architecture) avec séparation claire des responsabilités en 3 couches principales :

### 🏗️ Structure des couches

#### 1. **Domain** (`src/domain/`)
Couche métier contenant la logique pure du domaine, indépendante de toute technologie externe.

- **Entités** ([tour.entity.ts](mdc:apps/backend/src/domain/entity/tour.entity.ts), [user.entity.ts](mdc:apps/backend/src/domain/entity/user.entity.ts), [client.entity.ts](mdc:apps/backend/src/domain/entity/client.entity.ts))
- **Enums** pour les constantes métier ([user-role.enum.ts](mdc:apps/backend/src/domain/enum/user-role.enum.ts))
- **Value Objects** ([address.ts](mdc:apps/backend/src/domain/entity/address.ts), [tour-identifier.ts](mdc:apps/backend/src/domain/entity/tour-identifier.ts))

#### 2. **Application** (`src/application/`)
Couche orchestration contenant les cas d'usage et services applicatifs.

- **Services** métier ([tour.service.ts](mdc:apps/backend/src/application/service/tour.service.ts), [client.service.ts](mdc:apps/backend/src/application/service/client.service.ts))
- **DTOs** pour les transferts de données ([pagination.dto.ts](mdc:apps/backend/src/application/dto/pagination.dto.ts))
- **Mappers** pour les transformations ([tour-assignment.mapper.ts](mdc:apps/backend/src/application/mappers/tour-assignment.mapper.ts))
- **Use Cases** spécifiques ([import-xml-use-case.ts](mdc:apps/backend/src/application/service/import-xml-use-case.ts))

#### 3. **Infrastructure** (`src/infrastructure/`)
Couche technique gérant les détails d'implémentation et l'interface avec l'extérieur.

- **Controllers** API REST ([tour-manager.controller.ts](mdc:apps/backend/src/infrastructure/controller/manager/tour-manager.controller.ts))
- **Repositories** accès aux données ([tour.repository.ts](mdc:apps/backend/src/infrastructure/repository/tour.repository.ts))
- **Services** techniques ([keycloak-admin.service.ts](mdc:apps/backend/src/infrastructure/service/keycloak-admin.service.ts), [s3.service.ts](mdc:apps/backend/src/infrastructure/service/s3.service.ts))
- **Guards/Interceptors** sécurité ([current-user.interceptor.ts](mdc:apps/backend/src/infrastructure/interceptor/current-user.interceptor.ts))

## 🚀 Configuration et Point d'entrée

- **Module principal** : [app.module.ts](mdc:apps/backend/src/app.module.ts) - Configuration NestJS et injection de dépendances
- **Point d'entrée** : [main.ts](mdc:apps/backend/src/main.ts) - Démarrage serveur HTTP
- **Configuration DB** : [app.datasource.ts](mdc:apps/backend/src/app.datasource.ts) - TypeORM et PostgreSQL
- **Auth Keycloak** : [app.auth.ts](mdc:apps/backend/src/app.auth.ts) - Configuration authentification

## 🛠️ Technologies principales

- **Framework** : NestJS (avec decorators et DI)
- **ORM** : TypeORM + PostgreSQL
- **Authentification** : Keycloak (OIDC/JWT)
- **Cloud Storage** : AWS S3
- **Validation** : class-validator + class-transformer
- **CLI Commands** : nest-commander
- **Cache** : cache-manager

## 📋 Conventions de nommage

- **Controllers** : `{entity}-{context}.controller.ts` (ex: `tour-manager.controller.ts`)
- **Services** : `{entity}.service.ts` (logique métier) ou `{tech}-{purpose}.service.ts` (technique)
- **Entities** : `{entity}.entity.ts` avec héritage de [base.entity.ts](mdc:apps/backend/src/domain/entity/base.entity.ts)
- **DTOs** : `{purpose}.dto.ts` avec validation
- **Repositories** : `{entity}.repository.ts` étendant Repository TypeORM

## 🔐 Gestion des rôles

Système de rôles basé sur Keycloak avec middleware `@Roles()` :
- **Manager** : Gestion opérationnelle
- **Admin** : Administration système

## 📦 Organisation des modules

Les dépendances sont injectées dans [app.module.ts](mdc:apps/backend/src/app.module.ts) suivant les principes SOLID :
- Un module par domaine métier
- Séparation interface/implémentation
- Injection de dépendances explicite
