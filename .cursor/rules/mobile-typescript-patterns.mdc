---
description: 
globs: **/mobile/**/*
alwaysApply: false
---
# Mobile TypeScript Patterns

## Interface Design Patterns

### Entity Interfaces
- Préfixe `I` obligatoire pour toutes les interfaces (`IUserEntity`, `IClientEntity`)
- Héritage d'une interface de base `IBaseDomainEntity` pour les entités métier
- Propriétés communes : `id`, `createdAt`, `updatedAt`

Référence: [i-base-domain-entity.ts](mdc:apps/mobile/src/interfaces/entity/i-base-domain-entity.ts)

### Interface Composition
```typescript
// Interface de base
export interface IBaseDomainEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// Interface spécialisée
export interface IClientEntity extends IBaseDomainEntity {
  code: string;
  name?: string;
  address: IAddress;
  email?: string;
  stops: IStopEntity[];
}
```

### DTO Interfaces
- Suffixe `.dto.ts` pour les fichiers DTO
- Naming convention : `kebab-case` (`user-update-request.dto.ts`)
- Interfaces spécifiques aux opérations CRUD

## Type Organization

### File Structure
```
interfaces/
├── entity/          # Entités métier (IUserEntity, IClientEntity)
├── dto/            # Data Transfer Objects (requests/responses)
├── enum/           # Énumérations typées
└── pagination.ts   # Types de pagination réutilisables
```

### Import/Export Patterns
- Export nommé pour les interfaces (`export interface IUserEntity`)
- Import avec type annotation (`import type { IUserEntity }`)
- Re-export depuis index.ts pour simplifier les imports

## Generic Types

### API Response Types
```typescript
interface IPaginatedResponse<T> {
  data: T[];
  meta: IPaginationMeta;
}

interface IPaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
```

### Service Types
```typescript
class ApiService {
  async get<T>(endpoint: string): Promise<T> {
    // Implementation
  }
  
  async getPaginated<T>(
    endpoint: string, 
    params?: IPaginationParams
  ): Promise<IPaginatedResponse<T>> {
    // Implementation
  }
}
```

## Redux Types

### Store Types
```typescript
// Export des types depuis le store
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Hooks typés
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
```

### Slice State Types
```typescript
interface CurrentUserState {
  user: IUserEntity | null;
  loading: boolean;
  error: string | null;
  isInitialized: boolean;
}

const initialState: CurrentUserState = {
  user: null,
  loading: false,
  error: null,
  isInitialized: false,
};
```

## Component Props Types

### Component Interface Pattern
```typescript
interface ComponentNameProps {
  // Required props
  children: React.ReactNode;
  
  // Optional props with defaults
  className?: string;
  disabled?: boolean;
  
  // Event handlers
  onClick?: () => void;
  
  // Specific props
  variant?: 'primary' | 'secondary' | 'danger';
}

const ComponentName: React.FC<ComponentNameProps> = ({
  children,
  className = '',
  disabled = false,
  onClick,
  variant = 'primary'
}) => {
  // Implementation
};
```

### Props Destructuring with Defaults
- Valeurs par défaut dans la destructuration
- Types optionnels avec `?`
- Props rest avec `...rest` typé

## Enum Patterns

### String Enums
```typescript
export enum ImportStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}
```

### Const Assertions
```typescript
export const TOUR_STATUS = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED'
} as const;

export type TourStatus = typeof TOUR_STATUS[keyof typeof TOUR_STATUS];
```

## Utility Types

### Conditional Types
```typescript
type ApiResponse<T> = T extends string 
  ? { message: T } 
  : { data: T };

type UpdateRequest<T> = Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;
```

### Mapped Types
```typescript
type FormFields<T> = {
  [K in keyof T]: {
    value: T[K];
    error?: string;
    touched: boolean;
  };
};
```

## Error Handling Types

### API Error Types
```typescript
interface ApiError {
  message: string;
  statusCode: number;
  timestamp: string;
  path: string;
}

type ApiResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: ApiError;
};
```

### Form Validation Types
```typescript
interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

type FieldValidator<T> = (value: T) => string | undefined;
```

## Async Types

### Promise Types
```typescript
type AsyncState<T> = {
  data: T | null;
  loading: boolean;
  error: string | null;
};

type AsyncAction<T> = 
  | { type: 'LOADING' }
  | { type: 'SUCCESS'; payload: T }
  | { type: 'ERROR'; error: string };
```

### Hook Return Types
```typescript
interface UsePaginationReturn {
  paginationParams: IPaginationParams;
  paginationMeta: IPaginationMeta | null;
  setPaginationMeta: (meta: IPaginationMeta) => void;
  handlePageChange: (newPage: number) => void;
  handleLimitChange: (newLimit: number) => void;
  handleSortChange: (field: string) => void;
}
```

## Type Guards

### Runtime Type Checking
```typescript
function isUserEntity(obj: any): obj is IUserEntity {
  return obj && 
    typeof obj.id === 'string' &&
    typeof obj.email === 'string' &&
    Array.isArray(obj.roles);
}

function assertIsUserEntity(obj: any): asserts obj is IUserEntity {
  if (!isUserEntity(obj)) {
    throw new Error('Object is not a valid UserEntity');
  }
}
```

## Configuration Types

### Environment Types
```typescript
interface AppConfig {
  ENV: {
    API_URL: string;
    KEYCLOAK_URL: string;
    REALM: string;
    CLIENT_ID: string;
  };
  PAGINATION: {
    DEFAULT_LIMIT: number;
    MAX_LIMIT: number;
  };
  SORT: {
    DEFAULT_FIELD: string;
    DEFAULT_ORDER: 'asc' | 'desc';
  };
}
```

## Best Practices

### Type Safety Rules
- Éviter `any` sauf cas exceptionnels justifiés
- Utiliser `unknown` pour les types inconnus
- Préférer les unions de types aux enums quand approprié
- Utiliser `strict: true` dans tsconfig.json

### Performance Considerations
- Types complexes dans des fichiers séparés
- Lazy loading des types lourds
- Éviter les types récursifs profonds
