---
description: 
globs: **/frontend/**/*
alwaysApply: false
---
# Frontend Component Patterns - Structure et Réutilisabilité

## 🎯 Vue d'ensemble
Le frontend suit des patterns stricts de séparation des responsabilités pour maximiser la réutilisabilité et la maintenabilité des composants.

## 🏗️ Pattern Form/Dialog - Séparation obligatoire

### ❌ Anti-pattern (À éviter)
```
create-user-dialog.tsx  // Mélange dialog + logique métier
├─ Dialog UI
├─ Form logic  
├─ Validation
├─ API calls
└─ State management
```

### ✅ Pattern correct (À suivre)
```
user-form/
└─ index.tsx           // Form pur avec logique métier

dialogs/
└─ create-user-dialog.tsx  // Dialog simple qui utilise le Form
```

## 📁 Structure des composants services

### Form Components (Logique métier pure)
**Localisation** : `components/services/{entity}/{entity}-form/index.tsx`
**Exemples** : [VehicleForm](mdc:apps/frontend/src/components/services/vehicle/vehicle-form/index.tsx), [UserForm](mdc:apps/frontend/src/components/services/user/user-form/index.tsx)

**Interface standard** :
```tsx
interface {Entity}FormProps {
  onSuccess?: () => void;  // Callback au parent
}

export function {Entity}Form({ onSuccess }: {Entity}FormProps) {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState<FormData>({...});
  const [error, setError] = useState('');

  const mutation = useMutation({
    mutationFn: (data: CreateDto) => apiService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['{entity}'] });
      onSuccess?.();
    },
    onError: (error: Error) => {
      setError(error.message);
    },
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Form fields */}
      {error && <p className="text-sm text-destructive">{error}</p>}
      <Button type="submit" disabled={mutation.isPending}>
        {mutation.isPending ? 'Création...' : 'Créer'}
      </Button>
    </form>
  );
}
```

### Dialog Components (Interface pure)
**Localisation** : `components/services/{entity}/{entity}-datatable/create-{entity}-dialog.tsx`

**Structure standard** :
```tsx
interface Create{Entity}DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function Create{Entity}Dialog({ open, onOpenChange }: Create{Entity}DialogProps) {
  const handleSuccess = () => {
    toast.success('{Entity} créé avec succès');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Créer un nouveau {entity}</DialogTitle>
        </DialogHeader>
        <{Entity}Form onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  );
}
```

## 🔧 Patterns useMutation

### ✅ Correct - Fonction fléchée (préserve le contexte)
```tsx
const mutation = useMutation({
  mutationFn: (data: CreateDto) => apiService.create(data),
  onSuccess: () => { /* ... */ },
});
```

### ❌ Incorrect - Référence directe (perd le contexte `this`)
```tsx
const mutation = useMutation({
  mutationFn: apiService.create,  // ❌ Perte de contexte
  onSuccess: () => { /* ... */ },
});
```

## 📡 Patterns Service API

### Structure standard des services
**Localisation** : `lib/api-service/{entity}-api-service.ts`
**Exemple** : [UserApiService](mdc:apps/frontend/src/lib/api-service/user-api-service.ts)

```tsx
export class {Entity}ApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async create(data: Create{Entity}Dto): Promise<I{Entity}Entity> {
    const response = await this.apiClient.post('/api/{entity}', data);
    return response.data;
  }

  // Autres méthodes CRUD
}

export const {entity}ApiService = new {Entity}ApiService(apiClient);
```

### DTOs Frontend
**Localisation** : `lib/dto/{entity}-creation.dto.ts`

```tsx
export interface Create{Entity}RequestDto {
  // Champs requis
  name: string;
  email: string;
  
  // Champs optionnels
  description?: string;
  metadata?: Record<string, any>;
}
```

## 🎁 Avantages de ces patterns

### 1. **Réutilisabilité maximale**
```tsx
// Dans un dialog
<Dialog>
  <UserForm onSuccess={() => closeDialog()} />
</Dialog>

// Dans une page
<UserForm onSuccess={() => navigate('/users')} />

// Dans un drawer  
<Drawer>
  <UserForm onSuccess={() => closeDrawer()} />
</Drawer>
```

### 2. **Séparation des responsabilités**
- **Form** : Logique métier (validation, API, state)
- **Dialog** : Interface utilisateur (modal, responsive)

### 3. **Maintenabilité**
- Modification logique → 1 seul endroit (Form)
- Modification UI → 1 seul endroit (Dialog)

### 4. **Tests facilitées**
- Tester logique → Test Form isolément
- Tester UI → Test Dialog avec mock

## 📋 Conventions de nommage

### Composants
- **Forms** : `{Entity}Form` (ex: `UserForm`, `VehicleForm`)
- **Dialogs** : `Create{Entity}Dialog` (ex: `CreateUserDialog`)
- **DataTables** : `{Entity}sDataTable` (ex: `UsersDataTable`)

### Fichiers
- **Forms** : `{entity}-form/index.tsx`
- **Dialogs** : `create-{entity}-dialog.tsx`
- **Services** : `{entity}-api-service.ts`
- **DTOs** : `{entity}-creation.dto.ts`

### Props et callbacks
- **Success callback** : `onSuccess?: () => void`
- **Form data** : `{entity}Data: Create{Entity}Dto`
- **Error state** : `error: string`

## 🔍 Validation des patterns

### Checklist Form Component
- [ ] Props minimales (`onSuccess` uniquement)
- [ ] State interne encapsulé
- [ ] useMutation avec fonction fléchée
- [ ] Gestion d'erreur locale (`setError`)
- [ ] Validation côté client
- [ ] Nettoyage des données avant envoi
- [ ] Invalidation des queries après succès

### Checklist Dialog Component  
- [ ] Interface simple (open/onOpenChange)
- [ ] Utilise le Form component
- [ ] Toast de succès
- [ ] Fermeture automatique après succès
- [ ] Pas de logique métier

## 🚀 Exemple de référence

Suivre exactement les patterns de [VehicleForm](mdc:apps/frontend/src/components/services/vehicle/vehicle-form/index.tsx) et [VehiclesDataTable](mdc:apps/frontend/src/components/services/vehicle/vehicles-datatable/index.tsx) qui implémentent parfaitement ces conventions.

## ⚠️ Points d'attention

1. **Jamais de logique métier dans les Dialogs**
2. **Toujours utiliser des fonctions fléchées dans useMutation**  
3. **Props minimales pour maximiser la réutilisabilité**
4. **Validation côté client ET serveur**
5. **Nettoyage des données (trim, champs vides exclus)**

