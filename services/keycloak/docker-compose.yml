services:
  keycloak:
      image: localhost/keycloak
      build:
        dockerfile: dev.dockerfile
        context: .
      extra_hosts:
        - "host.docker.internal:host-gateway"
      volumes:
        - ./themes:/opt/keycloak/themes
        - ./scripts/disable-theme-cache.cli:/opt/startup-scripts/disable-theme-cache.cli
        - ./imports:/opt/keycloak/data/import
        - ./plugins/otp-email/target/arkyan-keycloak-email-otp-1.0.0.jar:/opt/keycloak/providers/arkyan-keycloak-email-otp-1.0.0.jar
      environment:
        KC_HOSTNAME: localhost
        KC_DB: postgres
        KC_DB_URL: jdbc:postgresql://${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
        KC_DB_USERNAME: ${POSTGRES_USER}
        KC_DB_PASSWORD: ${POSTGRES_PASSWORD}
        KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USER}
        KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
        KC_DB_POOL_MAX-SIZE: 10
      command:
        - "start-dev"
        - "--import-realm"
      ports:
        - ${EXPOSED_HTTP_PORT}:8080
        - ${EXPOSED_HTTPS_PORT}:8443
