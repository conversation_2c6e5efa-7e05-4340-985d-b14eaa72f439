FROM quay.io/keycloak/keycloak:25.0 AS builder

ENV KC_DB=postgres

# COPY ./themes/XXXX /opt/keycloak/themes/XXXX

RUN /opt/keycloak/bin/kc.sh build

FROM quay.io/keycloak/keycloak:25.0 AS runner

WORKDIR /opt/keycloak

COPY --from=builder /opt/keycloak/ /opt/keycloak/

# HTTP
EXPOSE 8080 

# HTTPS
EXPOSE 8443

# Management (Healthcheck)
EXPOSE 9000

ENTRYPOINT ["./bin/kc.sh"]

CMD ["start", "--health-enabled=true"]
