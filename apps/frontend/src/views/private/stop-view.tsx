import StopsDataTable from '@/components/services/stop/stops-datatable';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator.tsx';
import { Button } from '@/components/ui/button';
import { FileSpreadsheetIcon } from 'lucide-react';
import { triggerTableExport } from '@/lib/events/data-table-export-events';
import { useEffect, useState } from 'react';
import { Navigate, useNavigate, useParams } from 'react-router';

export function StopView() {
  const { date } = useParams<{ date: string }>();
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState<string>('');

  useEffect(() => {
    if (date) {
      setSelectedDate(date);
    }
  }, [date]);

  // Si pas de date dans l'URL, rediriger vers la date du jour
  if (!date) {
    const today = new Date().toISOString().split('T')[0];
    return <Navigate to={`/stop/by-date/${today}`} replace />;
  }

  const handleDateChange = (newDate: string) => {
    if (newDate && newDate !== date) {
      navigate(`/stop/by-date/${newDate}`);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Livraisons</h1>
        <div className="flex items-center gap-2">
          <Label htmlFor="date-filter" className="text-sm font-medium">
            Date :
          </Label>
          <DatePicker
            value={selectedDate}
            onChange={handleDateChange}
            placeholder="Sélectionner une date de livraison"
            className="min-w-[200px]"
          />


        </div>
      </div>


      <StopsDataTable fixedDate={date} hideInternalDateFilter={true} />
    </div>
  );
}
