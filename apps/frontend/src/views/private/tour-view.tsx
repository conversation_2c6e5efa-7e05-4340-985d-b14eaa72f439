import ToursDataTable from '@/components/services/tour/tours-datatable';
import { Separator } from '@/components/ui/separator.tsx';
import { Button } from '@/components/ui/button';
import { FileSpreadsheetIcon } from 'lucide-react';
import { triggerTableExport } from '@/lib/events/data-table-export-events';

export function TourView() {
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Gestion des tournées</h1>
        <Button variant="outline" onClick={triggerTableExport}>
          <FileSpreadsheetIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
          Télécharger Excel
        </Button>
      </div>

      <Separator className="mb-6" />
      <ToursDataTable />
    </div>
  );
}
