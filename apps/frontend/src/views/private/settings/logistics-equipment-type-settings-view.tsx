import { LogisticsEquipmentTypeDataTable } from '@/components/services/logistic-equipment/logistics-equipment-type-datatable';
import { Button } from '@/components/ui/button';
import { triggerTableExport } from '@/lib/events/data-table-export-events';
import { FileSpreadsheetIcon } from 'lucide-react';

export default function LogisticsEquipmentTypeSettingsView() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold">Types d'équipement logistique</h1>
          <p className="text-muted-foreground">
            Gérez les types d'équipement logistique disponibles dans l'application.
          </p>
        </div>
        <Button variant="outline" onClick={triggerTableExport}>
          <FileSpreadsheetIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
          Télécharger Excel
        </Button>
      </div>

      <LogisticsEquipmentTypeDataTable />
    </div>
  );
}
