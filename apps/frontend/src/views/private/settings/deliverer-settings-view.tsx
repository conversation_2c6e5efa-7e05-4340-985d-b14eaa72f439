import { DeliverersDataTable } from '@/components/services/deliverer/deliverers-datatable';
import { Separator } from '@/components/ui/separator.tsx';
import { Button } from '@/components/ui/button';
import { FileSpreadsheetIcon } from 'lucide-react';
import { triggerTableExport } from '@/lib/events/data-table-export-events';

export function DelivererSettingsView() {
  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold">Gestion des chauffeurs</h1>
          <p className="text-sm text-muted-foreground"><PERSON><PERSON><PERSON> et visualisez les chauffeurs</p>
        </div>
        <Button variant="outline" onClick={triggerTableExport}>
          <FileSpreadsheetIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
          Télécharger Excel
        </Button>
      </div>

      <Separator className="mb-6" />

      <DeliverersDataTable />
    </div>
  );
}
