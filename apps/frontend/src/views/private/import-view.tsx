import ImportsDataTable from '@/components/services/import/imports-datatable';
import TriggerImportDialog from '@/components/services/import/trigger-import-dialog';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileSpreadsheetIcon, PlayIcon, LoaderIcon } from 'lucide-react';
import { triggerTableExport } from '@/lib/events/data-table-export-events';
import { importManagerApiService } from '@/lib/api-service/import-manager-api-service';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

export function ImportView() {
  const [showTriggerDialog, setShowTriggerDialog] = useState(false);

  const { data: importStatus } = useQuery({
    queryKey: ['sftp-import-status'],
    queryFn: () => importManagerApiService.getSftpImportStatus(),
    refetchInterval: 2000,
    refetchIntervalInBackground: true,
  });

  const isImportInProgress = importStatus?.isImportInProgress ?? false;

  const handleTriggerImport = () => {
    setShowTriggerDialog(true);
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <h1 className="text-2xl font-semibold">Gestion des imports</h1>
          {isImportInProgress && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <LoaderIcon className="h-3 w-3 animate-spin" />
              Import en cours
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={triggerTableExport}>
            <FileSpreadsheetIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
            Télécharger Excel
          </Button>
          <Button
            onClick={handleTriggerImport}
            className="flex items-center gap-2"
            disabled={isImportInProgress}
          >
            <PlayIcon className="h-4 w-4" />
            Déclencher un import
          </Button>
        </div>
      </div>

      <Separator className="mb-6" />

      <ImportsDataTable onTriggerImport={handleTriggerImport} />

      <TriggerImportDialog open={showTriggerDialog} onOpenChange={setShowTriggerDialog} />
    </div>
  );
}
