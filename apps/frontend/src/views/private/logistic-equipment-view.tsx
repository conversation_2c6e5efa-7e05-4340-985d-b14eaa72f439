import { LogisticsEquipmentsDataTable } from '@/components/services/logistic-equipment/logistics-equipments-datatable';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator.tsx';
import { triggerTableExport } from '@/lib/events/data-table-export-events';
import { FileSpreadsheetIcon } from 'lucide-react';

export function LogisticEquipmentView() {
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Contrôle des Agrès</h1>
        <Button variant="outline" onClick={triggerTableExport}>
          <FileSpreadsheetIcon className="opacity-60" size={16} />
          Télécharger Excel
        </Button>
      </div>

      <Separator className="mb-6" />

      <LogisticsEquipmentsDataTable />
    </div>
  );
}
