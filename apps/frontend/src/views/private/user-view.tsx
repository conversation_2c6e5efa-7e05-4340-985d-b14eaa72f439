import UsersDataTable from '@/components/services/user/users-datatable';
import { Separator } from '@/components/ui/separator.tsx';
import { Button } from '@/components/ui/button';
import { FileSpreadsheetIcon } from 'lucide-react';
import { triggerTableExport } from '@/lib/events/data-table-export-events';

export function UserView() {
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Gestion des utilisateurs</h1>
        <Button variant="outline" onClick={triggerTableExport}>
          <FileSpreadsheetIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
          Télécharger Excel
        </Button>
      </div>

      <Separator className="mb-6" />
      <UsersDataTable />
    </div>
  );
}
