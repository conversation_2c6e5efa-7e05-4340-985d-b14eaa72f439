import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { keycloakService } from '@/lib/services/keycloak.service';
import { useAppSelector } from '@/hooks/redux';

export function LoginView() {
  const user = useAppSelector((state) => state.currentUser.currentUser);
  const location = useLocation();
  const from = location.state?.from?.pathname || '/dashboard';

  if (user) {
    return <Navigate to={from} replace />;
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Connexion</CardTitle>
          <CardDescription>
            Connectez-vous à votre compte pour accéder à l'application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => keycloakService.login()} className="w-full" size="lg">
            Se connecter
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
