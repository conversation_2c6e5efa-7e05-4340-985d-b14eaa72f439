import { Layout, PrivateLayout } from '@/components/layout';
import { FullPageLoader } from '@/components/layout/full-page-loader';
import { initAuth } from '@/lib/store';
import { LoginView } from '@/views/login-view.tsx';
import { DashboardView } from '@/views/private/dashboard-view.tsx';
import { ImportView } from '@/views/private/import-view.tsx';
import { LogisticEquipmentView } from '@/views/private/logistic-equipment-view.tsx';
import { DelivererSettingsView } from '@/views/private/settings/deliverer-settings-view.tsx';
import { IncidentTypeSettingsView } from '@/views/private/settings/incident-type-settings-view.tsx';
import { LogisticEquipmentSettingsView } from '@/views/private/settings/logistic-equipment-settings-view.tsx';
import { VehicleSettingsView } from '@/views/private/settings/vehicle-settings-view.tsx';
import { StopView } from '@/views/private/stop-view.tsx';
import { TourDetailView } from '@/views/private/tour-detail-view.tsx';
import { TourView } from '@/views/private/tour-view.tsx';
import { UserView } from '@/views/private/user-view.tsx';
import { Suspense, useEffect } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Navigate, Route, Routes } from 'react-router';
import { ErrorFallback } from '../components/error/error-fallback';
import { NotFound } from '../components/error/not-found';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { ProfileView } from './private/profile-view';

export function RootView() {
  const dispatch = useAppDispatch();
  const { isInitialized } = useAppSelector((state) => state.currentUser);

  useEffect(() => {
    if (!isInitialized) {
      dispatch(initAuth());
    }
  }, [dispatch, isInitialized]);

  if (!isInitialized) {
    return <FullPageLoader />;
  }

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback} onReset={() => window.location.reload()}>
      <Suspense fallback={<FullPageLoader />}>
        <Routes>
          <Route path="/" element={<Navigate to="/login" replace />} />
          <Route path="/login" element={<LoginView />} />
          <Route element={<Layout />}></Route>
          <Route element={<PrivateLayout />}>
            <Route path="/dashboard" element={<DashboardView />} />
            <Route path="/tour" element={<TourView />} />
            <Route path="/tours/:id" element={<TourDetailView />} />
            <Route path="/stop" element={<StopView />} />
            <Route path="/stop/by-date/:date" element={<StopView />} />
            <Route path="/settings/user" element={<UserView />} />
            <Route path="/logistic-equipment" element={<LogisticEquipmentView />} />
            <Route
              path="/settings/logistic-equipment"
              element={<LogisticEquipmentSettingsView />}
            />
            <Route path="/settings/deliverer" element={<DelivererSettingsView />} />
            <Route path="/settings/incident-types" element={<IncidentTypeSettingsView />} />
            <Route path="/settings/vehicle" element={<VehicleSettingsView />} />
            <Route path="/imports" element={<ImportView />} />
            <Route path="/profile" element={<ProfileView />} />
          </Route>

          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
}
