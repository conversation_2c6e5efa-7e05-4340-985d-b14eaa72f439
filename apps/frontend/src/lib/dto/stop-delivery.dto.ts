export enum DeliveryCompletionType {
  FULL = 'FULL',
  PARTIAL = 'PARTIAL',
  NONE = 'NONE',
}

export interface IEquipmentCountDto {
  palletCount?: number;
  rollCount?: number;
  packageCount?: number;
}

export interface ILogisticsEquipmentDetailDto {
  logisticsEquipmentTypeId: string;
  quantity: number;
}

export interface ICompleteStopDeliveryDto {
  signatureFile?: {
    base64: string;
    filename: string;
    mimeType: string;
  };
  signatureFirstName?: string;
  signatureLastName?: string;
  signatureEmail?: string;
  photoFiles?: {
    base64: string;
    filename: string;
    mimeType: string;
  }[];
  incidentTypeId?: string;
  comments?: string;
  deliveryCompletionType?: DeliveryCompletionType;
  isSecureLocation?: boolean;
  returnedEquipmentCount?: IEquipmentCountDto;
  unloadedEquipmentCount?: IEquipmentCountDto;
  returnedEquipmentDetails?: ILogisticsEquipmentDetailDto[];
  unloadedEquipmentDetails?: ILogisticsEquipmentDetailDto[];
  // Location data
  latitude?: number;
  longitude?: number;
  precision?: number;
}
