import { useEffect, useCallback, useRef } from 'react';
import {
  dataTableExportEmitter,
  type DataTableExportEvent,
} from '../events/data-table-export-events';

export interface UseDataTableExportTriggerOptions {
  onExportTrigger: () => void | Promise<void>;
  enabled?: boolean;
}

export function useDataTableExportTrigger({
  onExportTrigger,
  enabled = true,
}: UseDataTableExportTriggerOptions) {
  const onExportTriggerRef = useRef(onExportTrigger);
  onExportTriggerRef.current = onExportTrigger;

  const handleExportEvent = useCallback(
    (event: DataTableExportEvent) => {
      if (!enabled) return;
      onExportTriggerRef.current();
    },
    [enabled],
  );

  useEffect(() => {
    if (!enabled) return;

    dataTableExportEmitter.on('table:export', handleExportEvent);

    return () => {
      dataTableExportEmitter.off('table:export', handleExportEvent);
    };
  }, [handleExportEvent, enabled]);

  const triggerExport = useCallback(() => {
    onExportTriggerRef.current();
  }, []);

  return {
    triggerExport,
  };
}
