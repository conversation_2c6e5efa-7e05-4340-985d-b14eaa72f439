import { useState, useCallback } from 'react';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';
import type { DataTableConfig } from '../../interfaces/datatable';

export interface UseDataTableExportOptions<
  TData,
  TSearchParams extends Record<string, unknown> = Record<string, unknown>,
> {
  queryFn: (params: IPaginationParams & TSearchParams) => Promise<IPaginatedResponse<TData>>;
  currentParams: IPaginationParams & TSearchParams;
  config: DataTableConfig<TData, TSearchParams>;
  maxPagesLimit?: number;
}

export interface UseDataTableExportResult<TData> {
  exportData: () => Promise<TData[]>;
  isExporting: boolean;
  exportProgress: number;
  error: Error | null;
}

export function useDataTableExport<
  TData,
  TSearchParams extends Record<string, unknown> = Record<string, unknown>,
>({
  queryFn,
  currentParams,
  config,
  maxPagesLimit = 100,
}: UseDataTableExportOptions<TData, TSearchParams>): UseDataTableExportResult<TData> {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [error, setError] = useState<Error | null>(null);

  const exportData = useCallback(async (): Promise<TData[]> => {
    setIsExporting(true);
    setExportProgress(0);
    setError(null);

    const allData: TData[] = [];

    try {
      const firstPageParams = {
        ...currentParams,
        page: 1,
        limit: 100,
      };

      const firstPageResponse = await queryFn(firstPageParams);
      allData.push(...firstPageResponse.items);

      const totalPages = Math.min(firstPageResponse.meta.totalPages, maxPagesLimit);
      setExportProgress(1 / totalPages);

      const pagePromises: Promise<IPaginatedResponse<TData>>[] = [];
      for (let page = 2; page <= totalPages; page++) {
        const pageParams = {
          ...currentParams,
          page,
          limit: 100,
        };
        pagePromises.push(queryFn(pageParams));
      }

      const batchSize = 5;
      for (let i = 0; i < pagePromises.length; i += batchSize) {
        const batch = pagePromises.slice(i, i + batchSize);
        const batchResults = await Promise.all(batch);

        batchResults.forEach((response) => {
          allData.push(...response.items);
        });

        const currentProgress = Math.min((i + batchSize + 1) / totalPages, 1);
        setExportProgress(currentProgress);
      }

      return allData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err : new Error('Export failed');
      setError(errorMessage);
      throw errorMessage;
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  }, [queryFn, currentParams, maxPagesLimit]);

  return {
    exportData,
    isExporting,
    exportProgress,
    error,
  };
}
