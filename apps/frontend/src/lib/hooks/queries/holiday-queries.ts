import { useQuery } from '@tanstack/react-query';
import { holidayApiService } from '@/lib/api-service/holiday-api-service';
import { keycloakService } from '@/lib/services/keycloak.service';
import type { PlanningHoliday } from '@/components/layout/planning/types';

export const holidayQueryKeys = {
  all: ['holidays'] as const,
  byYear: (year: number) => [...holidayQueryKeys.all, 'year', year] as const,
};

export const useHolidaysQuery = (year: number) => {
  return useQuery({
    queryKey: holidayQueryKeys.byYear(year),
    queryFn: async (): Promise<PlanningHoliday[]> => {
      if (!keycloakService.keycloak.authenticated) {
        return [];
      }
      const holidaysData = await holidayApiService.getHolidaysByYear(year);
      // Return as PlanningHoliday (which is the same as IHolidayEntity based on the current usage)
      return holidaysData;
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60 * 60 * 24, // 24 hours - holidays don't change often
  });
};
