import type { ITourProgress } from '@/interfaces/entity/i-tour-progress';
import { keycloakService } from '@/lib/services/keycloak.service';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { tourManagerApiService } from '../../api-service/tour-manager-api-service';

export const tourProgressQueryKeys = {
  all: ['tour-progress'] as const,
  byDate: (date: Date) => [...tourProgressQueryKeys.all, format(date, 'yyyy-MM-dd')] as const,
};

export const useTourProgressQuery = (date: Date) => {
  return useQuery({
    queryKey: tourProgressQueryKeys.byDate(date),
    queryFn: async (): Promise<ITourProgress[]> => {
      if (!keycloakService.keycloak.authenticated) {
        return [];
      }
      return tourManagerApiService.getToursProgress(format(date, 'yyyy-MM-dd'));
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60, // 1 minute
  });
};
