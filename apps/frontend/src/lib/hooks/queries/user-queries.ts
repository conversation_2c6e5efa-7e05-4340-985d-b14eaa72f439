import { useQuery } from '@tanstack/react-query';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { userApiService } from '@/lib/api-service/user-api-service';
import { keycloakService } from '@/lib/services/keycloak.service';

export const userQueryKeys = {
  all: ['users'] as const,
  allUsers: () => [...userQueryKeys.all, 'all'] as const,
};

export const useUsersQuery = () => {
  return useQuery({
    queryKey: userQueryKeys.allUsers(),
    queryFn: async (): Promise<IUserEntity[]> => {
      if (!keycloakService.keycloak.authenticated) {
        return [];
      }
      return userApiService.getAllUsers();
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};
