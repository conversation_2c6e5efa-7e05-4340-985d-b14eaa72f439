import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';

export interface PaginatedQueryConfig<TData, TSearchParams = Record<string, unknown>> {
  queryKey: readonly unknown[];
  queryFn: (params: IPaginationParams & TSearchParams) => Promise<IPaginatedResponse<TData>>;
  pagination: IPaginationParams;
  searchParams?: TSearchParams;
  enabled?: boolean;
  staleTime?: number | undefined;
  options?: Omit<UseQueryOptions<IPaginatedResponse<TData>>, 'queryKey' | 'queryFn' | 'enabled'>;
}

export interface UsePaginatedQueryResult<TData> {
  data: TData[];
  meta: IPaginatedResponse<TData>['meta'] | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isFetching: boolean;
  isPlaceholderData: boolean;
  refetch: () => void;
}

export function usePaginatedQuery<TData, TSearchParams = Record<string, unknown>>({
  queryKey,
  queryFn,
  pagination,
  searchParams = {} as TSearchParams,
  enabled = true,
  staleTime,
  options = {},
}: PaginatedQueryConfig<TData, TSearchParams>): UsePaginatedQueryResult<TData> {
  const enhancedQueryKey = useMemo(
    () => [...queryKey, 'paginated', pagination, searchParams],
    [queryKey, pagination, searchParams],
  );

  const queryResult = useQuery({
    queryKey: enhancedQueryKey,
    queryFn: () => queryFn({ ...pagination, ...searchParams }),
    enabled,
    staleTime,
    placeholderData: (previousData) => previousData,
    ...options,
  });

  const refetch = useCallback(() => {
    queryResult.refetch();
  }, [queryResult]);

  return {
    data: queryResult.data?.items ?? [],
    meta: queryResult.data?.meta,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
    isFetching: queryResult.isFetching,
    isPlaceholderData: queryResult.isPlaceholderData,
    refetch,
  };
}
