import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

export interface LoaderState {
  isLoading: boolean;
  message?: string;
}

const initialState: LoaderState = {
  isLoading: false,
};

const slice = createSlice({
  name: 'loader',
  initialState,
  reducers: {
    showLoader: (state, action: PayloadAction<string | undefined>) => {
      state.isLoading = true;
      state.message = action.payload;
    },
    hideLoader: (state) => {
      state.isLoading = false;
      state.message = undefined;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
      if (!action.payload) {
        state.message = undefined;
      }
    },
  },
});

export const { showLoader, hideLoader, setLoading } = slice.actions;
export const selectIsLoading = (state: { loading: LoaderState }) => state.loading.isLoading;
export const selectLoadingMessage = (state: { loading: LoaderState }) => state.loading.message;

export default slice.reducer;
