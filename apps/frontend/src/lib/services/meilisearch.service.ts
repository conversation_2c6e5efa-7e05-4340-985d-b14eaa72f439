import { Index, MeiliSearch, type RecordAny } from 'meilisearch';
import { getEnv } from '../get-env.utils';

export class Meilisearch<T extends RecordAny> {
  private client: MeiliSearch;
  private index: Index<T>;

  constructor(
    indexName: string,
    host: string = getEnv('VITE_MEILISEARCH_HOST') as string,
    apiKey: string = getEnv('VITE_MEILISEARCH_API_KEY') as string,
  ) {
    this.client = new MeiliSearch({
      host,
      apiKey,
    });
    this.index = this.client.index(indexName);
  }

  async search(...searchParams: Parameters<Index['search']>) {
    return this.index.search(...searchParams);
  }
}
