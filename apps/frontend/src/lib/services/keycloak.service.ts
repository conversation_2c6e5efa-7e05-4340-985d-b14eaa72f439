import Keycloak, {
  type KeycloakConfig,
  type KeycloakError,
  type KeycloakInitOptions,
} from 'keycloak-js';
import mitt, { type Emitter } from 'mitt';
import { z } from 'zod';
import { getEnv } from '../get-env.utils';

// Define event types for the keycloak events
export type KeycloakEvents = {
  onReady: boolean;
  onAuthSuccess: void;
  onAuthError: KeycloakError;
  onAuthRefreshSuccess: void;
  onAuthRefreshError: KeycloakError;
  onAuthLogout: void;
  onTokenExpired: void;
  onTokenUpdate: void;
};

export class KeycloakService {
  public readonly keycloak: Keycloak;
  private readonly events: Emitter<KeycloakEvents>;

  constructor(
    private readonly keycloakConfig: KeycloakConfig,
    private readonly keycloakInitOptions: KeycloakInitOptions,
    private readonly refreshCheckInterval: number = 10000,
  ) {
    this.keycloak = new Keycloak(this.keycloakConfig);
    this.events = mitt<KeycloakEvents>();
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.keycloak.onReady = (authenticated) => {
      this.events.emit('onReady', authenticated || false);
    };

    this.keycloak.onAuthSuccess = () => {
      this.events.emit('onAuthSuccess');
    };

    this.keycloak.onAuthError = (error) => {
      this.events.emit('onAuthError', error);
    };

    this.keycloak.onAuthRefreshSuccess = () => {
      this.events.emit('onAuthRefreshSuccess');
    };

    // Use any for callback type handling due to Keycloak typing inconsistencies
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (this.keycloak as any).onAuthRefreshError = (error: KeycloakError) => {
      this.events.emit('onAuthRefreshError', error);
    };

    this.keycloak.onAuthLogout = () => {
      this.events.emit('onAuthLogout');
    };

    this.keycloak.onTokenExpired = () => {
      this.events.emit('onTokenExpired');
    };
  }

  // Method to subscribe to keycloak events
  public on<K extends keyof KeycloakEvents>(
    event: K,
    handler: (data: KeycloakEvents[K]) => void,
  ): void {
    this.events.on(event, handler);
  }

  // Method to unsubscribe from keycloak events
  public off<K extends keyof KeycloakEvents>(
    event: K,
    handler: (data: KeycloakEvents[K]) => void,
  ): void {
    this.events.off(event, handler);
  }

  public async init(): Promise<boolean> {
    const initPromise = this.keycloak.init(this.keycloakInitOptions).then((authenticated) => {
      this.startRefreshTokenCheck();
      return authenticated;
    });

    return initPromise;
  }

  private startRefreshTokenCheck() {
    setInterval(() => {
      this.updateToken();
    }, this.refreshCheckInterval);
  }

  public async login() {
    return this.keycloak.login();
  }

  public async logout() {
    return this.keycloak.logout();
  }

  public getRoles() {
    const realmRoles = this.keycloak.realmAccess?.roles || [];
    const resourceRoles = this.keycloak.resourceAccess?.[this.keycloakConfig.clientId]?.roles || [];

    return [...realmRoles, ...resourceRoles];
  }

  public getTokenRemainingValidity() {
    const exp = this.keycloak.tokenParsed?.exp;
    const timeSkew = this.keycloak.timeSkew;

    if (!exp || !timeSkew) {
      return 0;
    }

    const currentTime = new Date().getTime() / 1000;

    return exp ? Math.round(exp - currentTime - timeSkew) : 0;
  }

  public async updateToken(): Promise<boolean> {
    const updated = await this.keycloak.updateToken(70);

    if (updated) {
      this.events.emit('onTokenUpdate');
    }

    return updated;
  }
}

const keycloakConfig = {
  url: z.string().parse(getEnv('VITE_AUTH_URL')),
  realm: z.string().parse(getEnv('VITE_AUTH_REALM')),
  clientId: z.string().parse(getEnv('VITE_AUTH_CLIENT_ID')),
} satisfies KeycloakConfig;

const keycloakInitOptions = {
  onLoad: 'check-sso',
  checkLoginIframe: false,
  silentCheckSsoRedirectUri: `${window.location.origin}/static/silent-check-sso.html`,
} satisfies KeycloakInitOptions;

export const keycloakService = new KeycloakService(keycloakConfig, keycloakInitOptions);
