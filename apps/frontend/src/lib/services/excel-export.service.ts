import * as XLSX from 'xlsx';
import type { ColumnDef } from '@tanstack/react-table';
import { flexRender } from '@tanstack/react-table';

export type ValueTransformer<TData = unknown> = (
  value: unknown,
  row: TData,
  columnKey: string,
) => string | number | boolean | Date;

export interface ExcelExportOptions<TData = Record<string, unknown>> {
  data: TData[];
  columns: ColumnDef<TData>[];
  fileName?: string;
  sheetName?: string;
  columnMap?: Record<string, string>;
  excludeColumns?: string[];
  includeOnlyColumns?: string[];
  valueTransformers?: Record<string, ValueTransformer<TData>>;
}

export class ExcelExportService {
  static exportToExcel<TData>({
    data,
    columns,
    fileName = 'export',
    sheetName = 'Sheet1',
    columnMap = {},
    excludeColumns = [],
    includeOnlyColumns = [],
    valueTransformers = {},
  }: ExcelExportOptions<TData>): void {
    if (!data || data.length === 0) {
      throw new Error('No data to export');
    }

    const exportColumns = columns.filter((column) => {
      const columnKey =
        column.id || ('accessorKey' in column ? (column.accessorKey as string) : undefined);

      if (!columnKey) return false;

      if (columnKey === 'select' || columnKey === 'actions') {
        return false;
      }

      if (includeOnlyColumns.length > 0) {
        return includeOnlyColumns.includes(columnKey);
      }

      if (excludeColumns.length > 0) {
        return !excludeColumns.includes(columnKey);
      }

      return true;
    });

    const headers: string[] = [];
    const accessors: string[] = [];

    exportColumns.forEach((column) => {
      const columnKey =
        column.id || ('accessorKey' in column ? (column.accessorKey as string) : '');
      if (columnKey) {
        const header = columnMap[columnKey] || this.extractHeaderText(column);
        headers.push(header);
        accessors.push(columnKey);
      }
    });

    const worksheetData = [headers];

    data.forEach((row) => {
      const rowData: (string | number | boolean | Date)[] = [];

      exportColumns.forEach((column) => {
        let value: unknown = '';
        const columnKey =
          column.id || ('accessorKey' in column ? (column.accessorKey as string) : '');

        if ('accessorKey' in column && column.accessorKey) {
          value = this.getNestedValue(row, column.accessorKey as string);
        } else if ('accessorFn' in column && column.accessorFn) {
          value = column.accessorFn(row, 0);
        } else if (column.id) {
          value = (row as Record<string, unknown>)[column.id];
        }

        // Apply custom transformer if available
        if (columnKey && valueTransformers[columnKey]) {
          value = valueTransformers[columnKey](value, row, columnKey);
        } else {
          // Default transformations
          value = this.defaultValueTransform(value);
        }

        // Ensure value is a valid Excel type
        if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean' ||
          value instanceof Date
        ) {
          rowData.push(value);
        } else {
          rowData.push(String(value || ''));
        }
      });

      worksheetData.push(rowData as string[]);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    const columnWidths = headers.map((header, index) => {
      const maxLength = Math.max(
        header.length,
        ...worksheetData.slice(1).map((row) => String(row[index] || '').length),
      );
      return { wch: Math.min(maxLength + 2, 50) };
    });

    worksheet['!cols'] = columnWidths;

    const timestamp = new Date().toISOString().slice(0, 10);
    const fullFileName = `${fileName}_${timestamp}.xlsx`;

    XLSX.writeFile(workbook, fullFileName);
  }

  private static extractHeaderText<TData>(column: ColumnDef<TData>): string {
    if (typeof column.header === 'string') {
      return column.header;
    }

    if (typeof column.header === 'function') {
      // Skip complex header function rendering to avoid type issues
      return (
        column.id || ('accessorKey' in column ? (column.accessorKey as string) : '') || 'Column'
      );
    }

    return column.id || ('accessorKey' in column ? (column.accessorKey as string) : '') || 'Column';
  }

  private static getNestedValue(obj: unknown, path: string): unknown {
    return path
      .split('.')
      .reduce((current, key) => (current as Record<string, unknown>)?.[key], obj);
  }

  private static defaultValueTransform(value: unknown): string | number | boolean | Date {
    if (value instanceof Date) {
      return value.toLocaleDateString('fr-FR');
    } else if (Array.isArray(value)) {
      return value.join(', ');
    } else if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value);
    } else if (value === null || value === undefined) {
      return '';
    }
    return value as string | number | boolean | Date;
  }

  // Predefined transformers
  static readonly commonTransformers = {
    arrayJoin:
      (separator: string = ', ') =>
      (value: unknown) => {
        return Array.isArray(value) ? value.join(separator) : String(value || '');
      },

    dateFormat:
      (format: 'fr' | 'en' | 'iso' = 'fr') =>
      (value: unknown) => {
        if (value instanceof Date) {
          switch (format) {
            case 'fr':
              return value.toLocaleDateString('fr-FR');
            case 'en':
              return value.toLocaleDateString('en-US');
            case 'iso':
              return value.toISOString().split('T')[0];
            default:
              return value.toLocaleDateString('fr-FR');
          }
        }
        return String(value || '');
      },

    boolean:
      (trueValue: string = 'Oui', falseValue: string = 'Non') =>
      (value: unknown) => {
        if (typeof value === 'boolean') {
          return value ? trueValue : falseValue;
        }
        return String(value || '');
      },

    enumMap: (mapping: Record<string, string>) => (value: unknown) => {
      const stringValue = String(value || '');
      return mapping[stringValue] || stringValue;
    },
  } as const;

  static downloadExcelTemplate(headers: string[], fileName: string = 'template'): void {
    const worksheet = XLSX.utils.aoa_to_sheet([headers]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');

    const columnWidths = headers.map((header) => ({ wch: Math.max(header.length + 2, 15) }));
    worksheet['!cols'] = columnWidths;

    XLSX.writeFile(workbook, `${fileName}.xlsx`);
  }
}
