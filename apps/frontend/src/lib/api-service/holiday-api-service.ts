import type { IHolidayEntity } from '../../interfaces/entity/i-holiday-entity';
import { apiClient, type ApiClient } from '../api-client/api-client';

export class HolidayApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getHolidays(): Promise<IHolidayEntity[]> {
    const response = await this.apiClient.get('/api/holidays');
    return response.data;
  }

  async getHolidaysByYear(year: number): Promise<IHolidayEntity[]> {
    const response = await this.apiClient.get(`/api/holidays/year/${year}`);
    return response.data;
  }
}

export const holidayApiService = new HolidayApiService(apiClient);
