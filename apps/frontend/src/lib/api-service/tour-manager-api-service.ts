import type { ITourEntity } from '../../interfaces/entity/i-tour-entity';
import type { IStopEntity } from '../../interfaces/entity/i-stop-entity';
import type { ITourIncidentDto } from '../../interfaces/entity/i-tour-incident-dto';
import { ITourProgress } from '../../interfaces/entity/i-tour-progress';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';
import { apiClient, type ApiClient } from '../api-client/api-client';

export class TourManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getToursByDate(
    date: string,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ITourEntity>> {
    const response = await this.apiClient.get(`/api/manager/tours/by-date/${date}`, { params });
    return response.data;
  }

  async getTodayTours(params?: IPaginationParams): Promise<IPaginatedResponse<ITourEntity>> {
    const response = await this.apiClient.get('/api/manager/tours/today', { params });
    return response.data;
  }

  async getToursProgress(date: string): Promise<ITourProgress[]> {
    const response = await this.apiClient.get(`/api/manager/tours/progress/${date}`);
    return response.data;
  }

  async getTourById(id: string): Promise<ITourEntity> {
    const response = await this.apiClient.get(`/api/manager/tours/${id}`);
    return response.data;
  }

  async getTourStops(
    id: string,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<IStopEntity>> {
    const response = await this.apiClient.get(`/api/manager/tours/${id}/stops`, { params });
    return response.data;
  }

  async getTourIncidents(id: string): Promise<ITourIncidentDto[]> {
    const response = await this.apiClient.get(`/api/manager/tours/${id}/incidents`);
    return response.data;
  }
}

export const tourManagerApiService = new TourManagerApiService(apiClient);
