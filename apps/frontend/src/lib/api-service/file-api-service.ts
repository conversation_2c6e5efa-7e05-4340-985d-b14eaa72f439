import type { IFileEntity } from '../../interfaces/entity/i-file-entity';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';
import { apiClient, type ApiClient } from '../api-client/api-client';

export class FileApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async uploadFile(file: Blob, metadata?: Record<string, unknown>): Promise<IFileEntity> {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }
    const response = await this.apiClient.post('/api/file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getFileById(id: string): Promise<IFileEntity> {
    const response = await this.apiClient.get(`/api/file/${id}`);
    return response.data;
  }

  getFileDownloadUrl(id: string): string {
    return `${this.apiClient.getBaseUrl()}/api/file/${id}/download`;
  }

  async getFiles(params?: IPaginationParams): Promise<IPaginatedResponse<IFileEntity>> {
    const response = await this.apiClient.get('/api/file', { params });
    return response.data;
  }
}

export const fileApiService = new FileApiService(apiClient);
