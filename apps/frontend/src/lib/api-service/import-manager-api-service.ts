import type { IImportEntity } from '../../interfaces/entity/i-import-entity';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';
import { apiClient, type ApiClient } from '../api-client/api-client';

export class ImportManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getImportStatus(id: string): Promise<IImportEntity> {
    const response = await this.apiClient.get(`/api/manager/imports/${id}`);
    return response.data;
  }

  async getImportList(params?: IPaginationParams): Promise<IPaginatedResponse<IImportEntity>> {
    const response = await this.apiClient.get('/api/manager/imports', { params });
    return response.data;
  }

  async triggerSftpImport(date: string): Promise<IImportEntity> {
    const response = await this.apiClient.post('/api/manager/imports/sftp', { date });
    return response.data;
  }

  async getImportDetails(id: string): Promise<IImportEntity> {
    const response = await this.apiClient.get(`/api/manager/imports/${id}/details`);
    return response.data;
  }

  async getSftpImportStatus(): Promise<{ isImportInProgress: boolean }> {
    const response = await this.apiClient.get('/api/manager/imports/sftp/status');
    return response.data;
  }
}

export const importManagerApiService = new ImportManagerApiService(apiClient);
