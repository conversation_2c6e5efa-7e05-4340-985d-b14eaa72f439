import type { ITourEntity } from '../../interfaces/entity/i-tour-entity';
import type { ITourProgress } from '../../interfaces/entity/i-tour-progress';
import type { ILoadTourDto } from '../dto/load-tour.dto';
import { apiClient, type ApiClient } from '../api-client/api-client';

export class TourDeliverApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getTodayTours(): Promise<ITourEntity[]> {
    const response = await this.apiClient.get('/api/deliver/tours/today');
    return response.data;
  }

  async getToursProgress(date: string): Promise<ITourProgress[]> {
    const response = await this.apiClient.get(`/api/deliver/tours/progress/${date}`);
    return response.data;
  }

  async startTour(tourId: string): Promise<ITourEntity> {
    const response = await this.apiClient.post(`/api/deliver/tours/${tourId}/start`);
    return response.data;
  }

  async downloadDeliveryNote(tourId: string, deliveryNoteId: string): Promise<Blob> {
    const response = await this.apiClient.get(
      `/api/deliver/tours/${tourId}/delivery-notes/${deliveryNoteId}/download`,
      {
        responseType: 'blob',
      },
    );
    return response.data;
  }

  async loadTour(tourId: string, data: ILoadTourDto): Promise<ITourEntity> {
    const response = await this.apiClient.post(`/api/deliver/tours/${tourId}/load`, data);
    return response.data;
  }

  async preloadTour(tourId: string, data: ILoadTourDto): Promise<ITourEntity> {
    const response = await this.apiClient.post(`/api/deliver/tours/${tourId}/preload`, data);
    return response.data;
  }
}

export const tourDeliverApiService = new TourDeliverApiService(apiClient);
