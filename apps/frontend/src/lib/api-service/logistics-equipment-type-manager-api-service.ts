import type { ILogisticsEquipmentTypeEntity } from '../../interfaces/entity/i-logistics-equipment-type-entity';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';
import { apiClient, type ApiClient } from '../api-client/api-client';
import type {
  ICreateLogisticsEquipmentTypeDto,
  IUpdateLogisticsEquipmentTypeDto,
} from '../dto/logistics-equipment-type.dto';

export class LogisticsEquipmentTypeManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async create(data: ICreateLogisticsEquipmentTypeDto): Promise<ILogisticsEquipmentTypeEntity> {
    const response = await this.apiClient.post('/api/manager/logistics-equipment-types', data);
    return response.data;
  }

  async findAll(
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ILogisticsEquipmentTypeEntity>> {
    const response = await this.apiClient.get('/api/manager/logistics-equipment-types', { params });
    return response.data;
  }

  async findOne(id: string): Promise<ILogisticsEquipmentTypeEntity> {
    const response = await this.apiClient.get(`/api/manager/logistics-equipment-types/${id}`);
    return response.data;
  }

  async update(
    id: string,
    data: IUpdateLogisticsEquipmentTypeDto,
  ): Promise<ILogisticsEquipmentTypeEntity> {
    const response = await this.apiClient.patch(
      `/api/manager/logistics-equipment-types/${id}`,
      data,
    );
    return response.data;
  }

  async delete(id: string): Promise<void> {
    await this.apiClient.delete(`/api/manager/logistics-equipment-types/${id}`);
  }
}

export const logisticsEquipmentTypeManagerApiService = new LogisticsEquipmentTypeManagerApiService(
  apiClient,
);
