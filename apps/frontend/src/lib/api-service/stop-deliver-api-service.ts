import { apiClient, type ApiClient } from '../api-client/api-client';
import type { ICompleteStopDeliveryDto } from '../dto/stop-delivery.dto';
import type { IStopEntity } from '../../interfaces/entity/i-stop-entity';

export class StopDeliverApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async completeStopDelivery(stopId: string, data: ICompleteStopDeliveryDto): Promise<IStopEntity> {
    const response = await this.apiClient.post(`/api/deliver/stops/${stopId}/complete`, data);
    return response.data;
  }
}

export const stopDeliverApiService = new StopDeliverApiService(apiClient);
