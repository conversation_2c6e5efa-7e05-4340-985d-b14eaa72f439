import type { IUserEntity } from '../../interfaces/entity/i-user-entity';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';
import { apiClient, type ApiClient } from '../api-client/api-client';
import type { CreateUserRequestDto } from '../dto/user-creation.dto';
import type { UpdateUserRequestDto } from '../dto/user-update.dto';
import { depaginate } from '../utils';

export class UserApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getCurrentUser(): Promise<IUserEntity> {
    const response = await this.apiClient.get('/api/user/me');
    return response.data;
  }

  async updateCurrentUser(data: {
    locale?: string | null;
    color?: string | null;
  }): Promise<IUserEntity> {
    const response = await this.apiClient.patch('/api/user/me', data);
    return response.data;
  }

  async createUser(userData: CreateUserRequestDto): Promise<IUserEntity> {
    const response = await this.apiClient.post('/api/manager/users', userData);
    return response.data;
  }

  async updateUser(id: string, userData: UpdateUserRequestDto): Promise<IUserEntity> {
    const response = await this.apiClient.patch(`/api/manager/users/${id}`, userData);
    return response.data;
  }

  async getUserById(id: string): Promise<IUserEntity> {
    const response = await this.apiClient.get(`/api/manager/users/${id}`);
    return response.data;
  }

  async getUsers(params?: IPaginationParams): Promise<IPaginatedResponse<IUserEntity>> {
    const response = await this.apiClient.get('/api/manager/users', { params });
    return response.data;
  }

  async getAllUsers(params?: Omit<IPaginationParams, 'page'>): Promise<IUserEntity[]> {
    return depaginate(
      (paginationParams: IPaginationParams) => this.getUsers(paginationParams),
      params,
    );
  }

  async getDefaultColors(): Promise<string[]> {
    const response = await this.apiClient.get('/api/manager/users/default-colors');
    return response.data;
  }

  async resetUserPassword(
    userId: string,
    password: string,
    temporary: boolean = false,
  ): Promise<void> {
    const response = await this.apiClient.post(`/api/manager/users/${userId}/reset-password`, {
      password,
      temporary,
    });
    return response.data;
  }

  async resetMyPassword(password: string, temporary: boolean = false): Promise<void> {
    const response = await this.apiClient.post('/api/user/me/reset-password', {
      password,
      temporary,
    });
    return response.data;
  }
}

export const userApiService = new UserApiService(apiClient);
