const META_TAG_NAME = 'env';

/**
 * Get env from runtime environment
 * in develpment variable will be set from env so we can use it with import.meta.env
 * in production, since it's static build, in runtime we have to use meta tag
 * @param key - The key to get from the environment
 * @returns The value of the key
 */

export function getEnv(key: string): string | undefined {
  if (import.meta.env.MODE === 'development') {
    return getEnvForDevelopment(key);
  }

  return getEnvForProduction(key);
}

function getEnvForDevelopment(key: string): string | undefined {
  const envValue = import.meta.env[key];

  if (!envValue || envValue === '') {
    return undefined;
  }

  return String(envValue);
}

function getEnvForProduction(key: string): string | undefined {
  const metaTag = document.querySelector(`meta[name="${META_TAG_NAME}"]`);

  if (!metaTag) {
    throw new Error(`Meta tag with name ${META_TAG_NAME} not found`);
  }

  const metaContent = metaTag.getAttribute('content');

  if (!metaContent) {
    throw new Error(`Meta tag with name ${META_TAG_NAME} has no content`);
  }

  const envValue = JSON.parse(metaContent)[key];

  if (!envValue) {
    return undefined;
  }

  return String(envValue);
}
