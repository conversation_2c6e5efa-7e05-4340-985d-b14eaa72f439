import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getContainer(): HTMLElement {
  const mapContainer = document.getElementById('map');

  if (mapContainer) {
    return mapContainer;
  }

  return document.getElementById('root')!;
}

/**
 * Utilitaire pour récupérer tous les éléments d'une API paginée
 * @param fetchFunction - Fonction qui effectue la requête paginée
 * @param params - Paramètres de pagination (sans page)
 * @returns Promise contenant tous les éléments de toutes les pages
 */
export async function depaginate<T>(
  fetchFunction: (params: IPaginationParams) => Promise<IPaginatedResponse<T>>,
  params?: Omit<IPaginationParams, 'page'>,
): Promise<T[]> {
  const allItems: T[] = [];
  let currentPage = 1;
  let hasNextPage = true;

  while (hasNextPage) {
    const paginationParams: IPaginationParams = {
      ...params,
      page: currentPage,
      limit: params?.limit || 100, // Limite élevée pour optimiser les requêtes
    };

    const response = await fetchFunction(paginationParams);
    allItems.push(...response.items);

    hasNextPage = response.meta.hasNextPage;
    currentPage++;
  }

  return allItems;
}
