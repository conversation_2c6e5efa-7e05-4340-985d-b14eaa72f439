/**
 * Palette de couleurs par défaut garantissant une bonne lisibilité avec du texte blanc
 * Couleurs sélectionnées pour leur contraste et leur distinction visuelle
 */
const READABLE_COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#8B5CF6', // violet-500
  '#F59E0B', // amber-500
  '#EF4444', // red-500
  '#06B6D4', // cyan-500
  '#84CC16', // lime-500
  '#EC4899', // pink-500
  '#6366F1', // indigo-500
  '#14B8A6', // teal-500
  '#F97316', // orange-500
  '#A855F7', // purple-500
] as const;

/**
 * Génère un hash simple à partir d'une chaîne de caractères
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Attribue une couleur par défaut basée sur l'ID utilisateur
 * Utilise un hash de l'ID pour garantir la cohérence (même ID = même couleur)
 *
 * @param userId - L'identifiant unique de l'utilisateur
 * @returns Une couleur hexadécimale de la palette
 */
export function getUserDefaultColor(userId: string): string {
  if (!userId) {
    return READABLE_COLORS[0]; // Couleur par défaut si pas d'ID
  }

  const hash = simpleHash(userId);
  const colorIndex = hash % READABLE_COLORS.length;
  return READABLE_COLORS[colorIndex];
}

/**
 * Retourne la couleur de l'utilisateur (définie ou par défaut)
 *
 * @param userColor - Couleur définie par l'utilisateur (peut être null/undefined)
 * @param userId - ID de l'utilisateur pour générer une couleur par défaut
 * @returns La couleur finale à utiliser
 */
export function getUserColor(userColor: string | null | undefined, userId: string): string {
  return userColor || getUserDefaultColor(userId);
}

/**
 * Palette de couleurs disponibles pour référence
 */
export { READABLE_COLORS };
