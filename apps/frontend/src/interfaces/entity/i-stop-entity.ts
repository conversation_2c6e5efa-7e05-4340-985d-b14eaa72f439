import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { IOriginalClientInfo } from './i-original-client-info';
import type { IClientEntity } from './i-client-entity';
import type { IShipmentLineEntity } from './i-shipment-line-entity';
import type { IDeliveryNoteEntity } from './i-delivery-note-entity';
import type { ITourEntity } from './i-tour-entity';
import type { IStopCompletion } from './i-stop-completion-entity';

/**
 * Stop entity interface
 * Corresponds to StopEntity in backend
 */
export interface IStopEntity extends IBaseDomainEntity {
  tour: ITourEntity;
  tourId: string;
  sequenceInTour: number;
  originalClientInfo: IOriginalClientInfo;
  client: IClientEntity;
  sortingCode: string;
  deliveryTimeWindow: string;
  shipmentLines: IShipmentLineEntity[];
  deliveryNotes: IDeliveryNoteEntity[];
  completion: IStopCompletion;
}
