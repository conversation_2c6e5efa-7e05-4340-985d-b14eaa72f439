import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { ITourIdentifier } from './i-tour-identifier';
import type { IUserEntity } from './i-user-entity';

/**
 * Tour assignment entity interface
 * Corresponds to TourAssignmentEntity in backend
 */
export interface ITourAssignmentEntity extends IBaseDomainEntity {
  tourIdentifier: ITourIdentifier;
  fromDate: string;
  toDate?: string;
  notes?: string;
  user: IUserEntity;
  userId: string;
}
