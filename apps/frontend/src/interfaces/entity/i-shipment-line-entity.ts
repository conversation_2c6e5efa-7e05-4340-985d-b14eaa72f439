import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { ShipmentOperationType } from '../enum/shipment-operation-type.enum';
import type { IStopEntity } from './i-stop-entity';

/**
 * Shipment line entity interface
 * Corresponds to ShipmentLineEntity in backend
 */
export interface IShipmentLineEntity extends IBaseDomainEntity {
  stop: IStopEntity;
  stopId: string;
  operation: ShipmentOperationType;
  isFrozen: boolean;
  shipperName: string;
  weightKg: number;
  palletCount?: number;
  rollCount?: number;
  packageCount?: number;
  amount?: number;
  sequenceInStop: number;
}
