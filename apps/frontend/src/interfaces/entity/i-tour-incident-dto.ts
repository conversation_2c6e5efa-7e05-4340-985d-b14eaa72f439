import type { IIncidentTypeEntity } from './i-incident-type-entity';
import { DeliveryCompletionType } from '../enum/stop-completion.enum';

/**
 * Interface pour les incidents des stops récupérés pour une tournée
 *
 * Cette interface représente les incidents qui se sont produits sur les stops
 * d'une tournée spécifique. Les incidents ne sont pas des entités standalone
 * mais sont liés aux completions des stops.
 *
 * Correspond au TourIncidentDto du backend.
 */
export interface ITourIncidentDto {
  /** ID du stop où l'incident s'est produit */
  stopId: string;

  /** Numéro de séquence du stop dans la tournée */
  stopSequence: number;

  /** Nom du client */
  clientName: string;

  /** Code du client */
  clientCode: string;

  /** Type d'incident (référence vers IncidentType) */
  incidentType: IIncidentTypeEntity;

  /** Type de completion de livraison suite à l'incident */
  deliveryCompletionType: DeliveryCompletionType | null;

  /** Commentaires sur l'incident */
  comments: string | null;

  /** Date et heure de completion du stop */
  completedAt: Date | null;
}
