import type { ColumnDef } from '@tanstack/react-table';
import type { ReactNode } from 'react';
import type { IPaginatedResponse, IPaginationParams } from './pagination';

export interface DataTableSearchConfig {
  placeholder?: string;
  searchKey?: string;
  enabled?: boolean;
}

export interface DataTableFilterConfig<TData> {
  columnKey: keyof TData | string;
  label: string;
  type: 'select' | 'multiselect' | 'text' | 'date' | 'daterange';
  options?: Array<{ label: string; value: string | number }>;
  placeholder?: string;
}

export interface DataTableAction<TData> {
  label: string;
  icon?: ReactNode;
  onClick: (selectedRows: TData[]) => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  disabled?: (selectedRows: TData[]) => boolean;
  requiresSelection?: boolean;
}

export interface DataTableExportConfig {
  enabled?: boolean;
  fileName?: string;
  sheetName?: string;
  columnMap?: Record<string, string>;
  excludeColumns?: string[];
  includeOnlyColumns?: string[];
  maxPagesLimit?: number;
  buttonLabel?: string;
  buttonIcon?: ReactNode;
  valueTransformers?: Record<
    string,
    (value: unknown, row: unknown, columnKey: string) => string | number | boolean | Date
  >;
}

export interface DataTableConfig<TData, TSearchParams = Record<string, unknown>> {
  columns: ColumnDef<TData>[];
  search?: DataTableSearchConfig;
  filters?: DataTableFilterConfig<TData>[];
  actions?: DataTableAction<TData>[];
  enableSelection?: boolean;
  enableColumnVisibility?: boolean;
  defaultPageSize?: number;
  pageSizeOptions?: number[];
  searchParams?: TSearchParams;
  onSearchParamsChange?: (params: TSearchParams) => void;
  export?: DataTableExportConfig;
}

export interface DataTableState<TSearchParams = Record<string, unknown>> {
  pagination: IPaginationParams;
  searchParams: TSearchParams;
  globalFilter: string;
  columnFilters: Record<string, unknown>;
}

export interface DataTableCallbacks<TData, TSearchParams = Record<string, unknown>> {
  onPaginationChange: (pagination: IPaginationParams) => void;
  onSearchParamsChange: (params: TSearchParams) => void;
  onGlobalFilterChange: (filter: string) => void;
  onColumnFiltersChange: (filters: Record<string, unknown>) => void;
  onRowSelectionChange?: (selectedRows: TData[]) => void;
}

export interface PaginatedDataTableProps<TData, TSearchParams = Record<string, unknown>> {
  queryKey: readonly unknown[];
  queryFn: (params: IPaginationParams & TSearchParams) => Promise<IPaginatedResponse<TData>>;
  config: DataTableConfig<TData, TSearchParams>;
  enabled?: boolean;
  className?: string;
  tableId?: string;
}
