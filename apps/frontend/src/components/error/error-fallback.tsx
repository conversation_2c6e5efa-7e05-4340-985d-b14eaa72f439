import { Button } from '@/components/ui/button';

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

export function ErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
  const handleReset = () => {
    resetErrorBoundary();
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] text-center p-8">
      <h1 className="text-7xl font-bold text-destructive mb-4">Erreur</h1>
      <h2 className="text-3xl font-semibold mb-4"><PERSON><PERSON><PERSON> chose s'est mal passé</h2>

      <div className="mb-6 p-4 bg-destructive/10 rounded-md text-left max-w-2xl w-full">
        <p className="font-mono text-sm text-destructive">{error.message || 'Erreur inconnue'}</p>
        {process.env.NODE_ENV === 'development' && error.stack && (
          <pre className="mt-2 p-2 bg-background rounded text-xs overflow-auto">
            <code>{error.stack}</code>
          </pre>
        )}
      </div>

      <div className="flex gap-4">
        <Button variant="outline" onClick={handleReset}>
          Réessayer
        </Button>
        <Button variant="outline" onClick={() => (window.location.href = '/')}>
          Revenir à l'accueil
        </Button>
      </div>
    </div>
  );
}
