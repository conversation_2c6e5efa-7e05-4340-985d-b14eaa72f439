import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return this.props.fallback || <ErrorPage error={this.state.error} />;
    }

    return this.props.children;
  }
}

export function ErrorPage({
  error,
  resetErrorBoundary,
}: {
  error: Error | null;
  resetErrorBoundary?: () => void;
}) {
  const handleReset = () => {
    if (resetErrorBoundary) {
      resetErrorBoundary();
    } else {
      window.location.href = '/';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] text-center p-8">
      <h1 className="text-7xl font-bold text-destructive mb-4">Erreur</h1>
      <h2 className="text-3xl font-semibold mb-4">Quelque chose s'est mal passé</h2>

      {error && (
        <div className="mb-6 p-4 bg-destructive/10 rounded-md text-left max-w-2xl w-full">
          <p className="font-mono text-sm text-destructive">{error.message || 'Erreur inconnue'}</p>
          {process.env.NODE_ENV === 'development' && error.stack && (
            <pre className="mt-2 p-2 bg-background rounded text-xs overflow-auto">
              <code>{error.stack}</code>
            </pre>
          )}
        </div>
      )}

      <div className="flex gap-4">
        <Button variant="outline" onClick={handleReset}>
          Revenir à l'accueil
        </Button>
        {process.env.NODE_ENV === 'development' && (
          <Button variant="outline" onClick={() => window.location.reload()}>
            Recharger la page
          </Button>
        )}
      </div>
    </div>
  );
}
