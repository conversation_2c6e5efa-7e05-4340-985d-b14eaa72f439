import { useNavigate } from 'react-router';
import { Button } from '@/components/ui/button';

export function NotFound() {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] text-center p-8">
      <h1 className="text-7xl font-bold text-primary mb-4">404</h1>
      <h2 className="text-3xl font-semibold mb-8">Oups ! Page non trouvée</h2>
      <p className="mb-8 max-w-[500px] text-muted-foreground">
        Désolé, nous n'avons pas trouvé la page que vous cherchez. Elle a peut-être été déplacée ou
        supprimée.
      </p>
      <Button size="lg" onClick={() => navigate('/')}>
        Retour à l'accueil
      </Button>
    </div>
  );
}
