import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar.tsx';
import { Button } from '@/components/ui/button.tsx';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu.tsx';
import { useAppDispatch, useAppSelector } from '@/hooks/redux.ts';
import { signOut } from '@/lib/store';
import { ChevronDown, LogOut, Settings, UserIcon, Users } from 'lucide-react';
import { useNavigate } from 'react-router';

export default function UserDropdownMenu() {
  const user = useAppSelector((state) => state.currentUser.currentUser);
  const dispatch = useAppDispatch();

  const navigate = useNavigate();

  const handleGoToUsers = () => {
    navigate('/settings/user');
  };

  const handleSignOut = async () => {
    await dispatch(signOut());
  };

  const getInitials = (firstName: string | null, lastName: string | null) => {
    const first = firstName?.charAt(0) || '';
    const last = lastName?.charAt(0) || '';
    return `${first}${last}`.toUpperCase() || 'U';
  };

  const getFullName = (firstName: string | null, lastName: string | null) => {
    const first = firstName ?? '';
    const last = lastName ?? '';
    const fullName = `${first} ${last}`.trim();
    return fullName || 'Utilisateur';
  };

  return (
    <>
      {user && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-2 p-1">
              <Avatar className="h-8 w-8">
                <AvatarImage
                  src={`https://api.dicebear.com/7.x/initials/svg?seed=${getFullName(user.firstName, user.lastName)}`}
                  alt={getFullName(user.firstName, user.lastName)}
                />
                <AvatarFallback>{getInitials(user.firstName, user.lastName)}</AvatarFallback>
              </Avatar>
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>{getFullName(user.firstName, user.lastName)}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <UserIcon className="mr-2 h-4 w-4" />
                <span>Profil</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleGoToUsers}>
                <Users className="mr-2 h-4 w-4" />
                <span>Utilisateurs</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Paramètres</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>Déconnexion</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </>
  );
}
