import React from 'react';
import { Planning, type PlanningProps } from './Planning';
import { PlanningProvider } from './context/PlanningContext';

export interface PlanningWithProviderProps extends PlanningProps {
  initialMonth?: Date;
}

export const PlanningWithProvider: React.FC<PlanningWithProviderProps> = ({
  initialMonth,
  ...planningProps
}) => {
  return (
    <PlanningProvider initialMonth={initialMonth}>
      <Planning {...planningProps} setFullScreen={planningProps.setFullScreen} />
    </PlanningProvider>
  );
};
