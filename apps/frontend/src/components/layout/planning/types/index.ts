import type { IHolidayEntity } from '@/interfaces/entity/i-holiday-entity';
import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import type { ITourAssignmentEntity } from '@/interfaces/entity/i-tour-assignment-entity';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import type { TourStatus } from '@/interfaces/enum/tour.enums';

// Modern Planning-specific interfaces using backend entities
export interface PlanningTourItem {
  tourIdentifier: ITourIdentifier;
  deliveryDate: Date;
  status: TourStatus;
  assignedUsers?: IUserEntity[];
  color?: string;
  displayName: string;
}

export interface PlanningSelectedCell {
  tourIdentifier: ITourIdentifier;
  date: Date;
  existingAssignment?: ITourAssignmentEntity;
}

export interface PlanningDraggedUser {
  userId: string;
  sourceCell?: {
    tourIdentifier: ITourIdentifier;
    date: Date;
  };
}

export type PlanningLabels = Record<string, string>;

// Re-export backend entity as type alias for consistency
export type PlanningHoliday = IHolidayEntity;
