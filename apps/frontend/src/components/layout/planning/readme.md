# Planning Component

Un composant React modulaire et générique pour la gestion de planning avec assignation d'utilisateurs à des ressources.

## Fonctionnalités

- 📅 Vue calendrier mensuelle avec navigation
- 👥 Assignation d'utilisateurs à des créneaux (matin/après-midi)
- 🔄 Drag & Drop pour déplacer les assignations
- 📆 Support des weekends et jours fériés avec affichage du nom
- ⏰ Gestion des périodes (assignation sur plusieurs jours)
- 🎨 Couleurs générées automatiquement pour chaque ressource
- ✏️ Édition inline des noms de ressources
- ➕ Ajout/suppression dynamique de ressources
- 🌐 Labels personnalisables pour s'adapter à tout métier
- 📱 Interface responsive
- 🔒 Protection contre l'édition des dates passées

## Structure du composant

```
planning/
├── components/              # Composants UI
│   ├── index.ts            # Export centralisé
│   ├── Header.tsx          # En-tête avec navigation mensuelle
│   ├── ResourceList.tsx    # Liste éditable des ressources (colonne gauche)
│   ├── Timeline.tsx        # Grille du calendrier principal
│   ├── PeriodCell.tsx      # Cellule individuelle (matin/après-midi)
│   ├── AssignmentModal.tsx # Modal pour assigner un utilisateur
│   ├── Legend.tsx          # Légende en bas
│   └── DraggableUsersList.tsx # Liste des utilisateurs disponibles
├── hooks/                  # Hooks personnalisés
│   └── useAssignments.ts   # Logique de gestion des assignations
├── types/                  # Types TypeScript
│   └── index.ts            # Toutes les interfaces
├── utils/                  # Fonctions utilitaires
│   ├── dateHelpers.ts      # Formatage et helpers de dates
│   └── colorGenerator.ts   # Génération aléatoire de couleurs
├── constants/              # Constantes
│   └── index.ts            # Labels par défaut, couleurs, dimensions
├── Planning.tsx            # Composant principal (conteneur)
├── index.ts               # Point d'entrée du module
└── README.md              # Documentation
```

## Installation

```bash
npm install date-fns lucide-react @radix-ui/react-dialog @radix-ui/react-select
```

## Utilisation de base

```tsx
import { Planning } from './planning';
import type { User, Resource, Assignment, Holiday } from './planning/types';

const MyComponent = () => {
  const users: User[] = [
    { id: 1, nom: 'Alice Martin', avatar: 'AM' },
    { id: 2, nom: 'Bob Laurent', avatar: 'BL' }
  ];

  const resources: Resource[] = [
    { id: 1, nom: 'Ressource 1', couleur: 'bg-blue-500' },
    { id: 2, nom: 'Ressource 2', couleur: 'bg-green-500' }
  ];

  const holidays: Holiday[] = [
    { date: new Date(2025, 0, 1), name: 'Nouvel An' },
    { date: new Date(2025, 11, 25), name: 'Noël' }
  ];

  const handleAssignmentsChange = (assignments: Assignment[]) => {
    console.log('Nouvelles assignations:', assignments);
    // Envoyer à votre API
  };

  const handleResourcesChange = (resources: Resource[]) => {
    console.log('Ressources modifiées:', resources);
    // Envoyer à votre API
  };

  return (
    <Planning
      users={users}
      initialResources={resources}
      holidays={holidays}
      onAssignmentsChange={handleAssignmentsChange}
      onResourcesChange={handleResourcesChange}
    />
  );
};
```

## Structure des données

### User
```typescript
interface User {
  id: number;
  nom: string;
  avatar: string; // Initiales ou URL d'image
}
```

### Resource
```typescript
interface Resource {
  id: number;
  nom: string;
  couleur: string; // Classe Tailwind (ex: 'bg-blue-500')
  defaultUserId?: number; // Utilisateur par défaut pour l'année
}
```

### Assignment
```typescript
interface Assignment {
  userId: number | null;
  resourceId: number;
  date: Date;
  period: 'morning' | 'afternoon' | 'full';
  isExplicitRemoval?: boolean;
}
```

### Holiday
```typescript
interface Holiday {
  date: Date;
  name: string;
}
```

## Modèle de données SQL

Voici un modèle de base de données complet pour intégrer le composant Planning avec votre API :

### Table: users
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  nom VARCHAR(255) NOT NULL,
  avatar VARCHAR(50) NOT NULL,
  email VARCHAR(255) UNIQUE,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour améliorer les performances
CREATE INDEX idx_users_active ON users(active);
CREATE INDEX idx_users_nom ON users(nom);
```

### Table: resources
```sql
CREATE TABLE resources (
  id SERIAL PRIMARY KEY,
  nom VARCHAR(255) NOT NULL,
  couleur VARCHAR(50) NOT NULL,
  default_user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  active BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour améliorer les performances
CREATE INDEX idx_resources_active ON resources(active);
CREATE INDEX idx_resources_default_user ON resources(default_user_id);
CREATE INDEX idx_resources_order ON resources(display_order);
```

### Table: assignments
```sql
CREATE TABLE assignments (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  resource_id INTEGER NOT NULL REFERENCES resources(id) ON DELETE CASCADE,
  assignment_date DATE NOT NULL,
  period VARCHAR(20) NOT NULL CHECK (period IN ('morning', 'afternoon', 'full')),
  is_explicit_removal BOOLEAN DEFAULT false,
  created_by INTEGER REFERENCES users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  -- Contrainte unique pour éviter les doublons
  UNIQUE(resource_id, assignment_date, period)
);

-- Index pour améliorer les performances
CREATE INDEX idx_assignments_date ON assignments(assignment_date);
CREATE INDEX idx_assignments_resource_date ON assignments(resource_id, assignment_date);
CREATE INDEX idx_assignments_user ON assignments(user_id);
CREATE INDEX idx_assignments_removal ON assignments(is_explicit_removal);
```

### Table: holidays
```sql
CREATE TABLE holidays (
  id SERIAL PRIMARY KEY,
  holiday_date DATE NOT NULL,
  name VARCHAR(255) NOT NULL,
  country_code VARCHAR(2) DEFAULT 'FR',
  is_recurring BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(holiday_date, country_code)
);

-- Index pour améliorer les performances
CREATE INDEX idx_holidays_date ON holidays(holiday_date);
CREATE INDEX idx_holidays_country ON holidays(country_code);
```

### Table: planning_labels (optionnel - pour stocker les configurations de labels)
```sql
CREATE TABLE planning_labels (
  id SERIAL PRIMARY KEY,
  context VARCHAR(100) NOT NULL UNIQUE, -- ex: 'tournees', 'salles', 'vehicules'
  labels JSONB NOT NULL, -- Stockage JSON des labels personnalisés
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Vues utiles

```sql
-- Vue pour obtenir les assignations avec les informations complètes
CREATE VIEW v_assignments_full AS
SELECT 
  a.id,
  a.assignment_date,
  a.period,
  a.is_explicit_removal,
  u.id as user_id,
  u.nom as user_nom,
  u.avatar as user_avatar,
  r.id as resource_id,
  r.nom as resource_nom,
  r.couleur as resource_couleur,
  r.default_user_id
FROM assignments a
LEFT JOIN users u ON a.user_id = u.id
INNER JOIN resources r ON a.resource_id = r.id
WHERE r.active = true;

-- Vue pour obtenir les ressources avec leur utilisateur par défaut
CREATE VIEW v_resources_with_default_user AS
SELECT 
  r.*,
  u.nom as default_user_nom,
  u.avatar as default_user_avatar
FROM resources r
LEFT JOIN users u ON r.default_user_id = u.id
WHERE r.active = true
ORDER BY r.display_order, r.id;
```

### Exemples de requêtes API

```typescript
// GET /api/planning/data - Récupérer toutes les données
async function getPlanningData(month: string, year: number) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);
  
  const [users, resources, assignments, holidays] = await Promise.all([
    db.query('SELECT * FROM users WHERE active = true ORDER BY nom'),
    db.query('SELECT * FROM v_resources_with_default_user ORDER BY display_order'),
    db.query(`
      SELECT * FROM v_assignments_full 
      WHERE assignment_date >= $1 AND assignment_date <= $2
    `, [startDate, endDate]),
    db.query(`
      SELECT * FROM holidays 
      WHERE holiday_date >= $1 AND holiday_date <= $2
    `, [startDate, endDate])
  ]);

  return { users, resources, assignments, holidays };
}

// POST /api/assignments - Sauvegarder les assignations
async function saveAssignments(assignments: Assignment[]) {
  const client = await db.getClient();
  
  try {
    await client.query('BEGIN');
    
    // Supprimer les anciennes assignations pour la période
    const dates = [...new Set(assignments.map(a => a.date))];
    const resourceIds = [...new Set(assignments.map(a => a.resourceId))];
    
    await client.query(`
      DELETE FROM assignments 
      WHERE resource_id = ANY($1) AND assignment_date = ANY($2)
    `, [resourceIds, dates]);
    
    // Insérer les nouvelles assignations
    for (const assignment of assignments) {
      await client.query(`
        INSERT INTO assignments 
        (user_id, resource_id, assignment_date, period, is_explicit_removal)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        assignment.userId,
        assignment.resourceId,
        assignment.date,
        assignment.period,
        assignment.isExplicitRemoval || false
      ]);
    }
    
    await client.query('COMMIT');
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}
```

## Exemples d'utilisation métier

### 1. Gestion de tournées et chauffeurs

```tsx
const TourneesPlanning = () => {
  const chauffeurs: User[] = [
    { id: 1, nom: 'Jean Dupont', avatar: 'JD' },
    { id: 2, nom: 'Marie Martin', avatar: 'MM' }
  ];

  const tournees: Resource[] = [
    { id: 1, nom: 'Tournée Nord - Paris', couleur: 'bg-blue-500', defaultUserId: 1 },
    { id: 2, nom: 'Tournée Sud - Marseille', couleur: 'bg-green-500' }
  ];

  const labels = {
    title: 'Planning des Tournées',
    userLabel: 'Chauffeur',
    userLabelPlural: 'Chauffeurs',
    resourceLabel: 'Tournée',
    resourceLabelPlural: 'Tournées',
    assignUserLabel: 'Assigner un chauffeur',
    dragDropHintLabel: 'Chauffeurs disponibles (glisser-déposer)'
  };

  return (
    <Planning
      users={chauffeurs}
      initialResources={tournees}
      labels={labels}
    />
  );
};
```

### 2. Gestion de salles et réservations

```tsx
const SallesPlanning = () => {
  const clients: User[] = [
    { id: 1, nom: 'Entreprise A', avatar: 'EA' },
    { id: 2, nom: 'Association B', avatar: 'AB' }
  ];

  const salles: Resource[] = [
    { id: 1, nom: 'Salle de conférence A', couleur: 'bg-purple-500' },
    { id: 2, nom: 'Salle de réunion B', couleur: 'bg-indigo-500' }
  ];

  const labels = {
    title: 'Planning des Salles',
    userLabel: 'Client',
    userLabelPlural: 'Clients',
    resourceLabel: 'Salle',
    resourceLabelPlural: 'Salles',
    assignUserLabel: 'Réserver pour un client',
    addResourceLabel: 'Ajouter une salle',
    dragDropHintLabel: 'Clients disponibles (glisser-déposer)'
  };

  return (
    <Planning
      users={clients}
      initialResources={salles}
      labels={labels}
    />
  );
};
```

### 3. Gestion de véhicules et locations

```tsx
const LocationsPlanning = () => {
  const locataires: User[] = [
    { id: 1, nom: 'Client Particulier 1', avatar: 'CP1' },
    { id: 2, nom: 'Entreprise Location', avatar: 'EL' }
  ];

  const vehicules: Resource[] = [
    { id: 1, nom: 'Renault Clio - AB-123-CD', couleur: 'bg-red-500' },
    { id: 2, nom: 'Peugeot 308 - EF-456-GH', couleur: 'bg-gray-500' }
  ];

  const labels = {
    title: 'Planning de Location',
    userLabel: 'Locataire',
    userLabelPlural: 'Locataires',
    resourceLabel: 'Véhicule',
    resourceLabelPlural: 'Véhicules',
    assignUserLabel: 'Assigner un locataire',
    morningLabel: 'Matin (8h-14h)',
    afternoonLabel: 'Après-midi (14h-20h)',
    fullDayLabel: 'Journée complète'
  };

  return (
    <Planning
      users={locataires}
      initialResources={vehicules}
      labels={labels}
    />
  );
};
```

## Personnalisation des labels

Le composant accepte un objet `labels` pour personnaliser tous les textes :

```typescript
interface PlanningLabels {
  title?: string;              // Titre du planning
  userLabel?: string;          // Label singulier pour utilisateur
  userLabelPlural?: string;    // Label pluriel pour utilisateurs
  resourceLabel?: string;      // Label singulier pour ressource
  resourceLabelPlural?: string;// Label pluriel pour ressources
  morningLabel?: string;       // Label pour la période du matin
  afternoonLabel?: string;     // Label pour la période de l'après-midi
  fullDayLabel?: string;       // Label pour journée complète
  todayLabel?: string;         // Label pour "Aujourd'hui"
  holidayLabel?: string;       // Label pour jour férié
  addResourceLabel?: string;   // Label du bouton d'ajout
  deleteResourceLabel?: string;// Label pour supprimer
  assignUserLabel?: string;    // Titre de la modal d'assignation
  selectUserLabel?: string;    // Placeholder de sélection
  removeAssignmentLabel?: string; // Option de suppression
  periodLabel?: string;        // Label pour la période
  startDateLabel?: string;     // Label date de début
  endDateLabel?: string;       // Label date de fin
  dragDropHintLabel?: string;  // Indication drag & drop
}
```

## Personnalisation du terme "Utilisateur"

Pour changer le terme "utilisateur" dans tout le code, modifiez simplement la constante dans `constants/index.ts` :

```typescript
// constants/index.ts
export const DEFAULT_LABELS: Required<PlanningLabels> = {
  userLabel: 'Chauffeur',        // Changez ici
  userLabelPlural: 'Chauffeurs', // Et ici
  // ... autres labels
};
```

## Connexion à une API

### 1. Récupération des données initiales

```tsx
const PlanningContainer = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [resources, setResources] = useState<Resource[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [holidays, setHolidays] = useState<Holiday[]>([]);

  useEffect(() => {
    // Charger les données depuis votre API
    Promise.all([
      fetch('/api/users').then(res => res.json()),
      fetch('/api/resources').then(res => res.json()),
      fetch('/api/assignments').then(res => res.json()),
      fetch('/api/holidays').then(res => res.json())
    ]).then(([usersData, resourcesData, assignmentsData, holidaysData]) => {
      setUsers(usersData);
      setResources(resourcesData);
      // Convertir les dates string en objets Date
      setAssignments(assignmentsData.map(a => ({
        ...a,
        date: new Date(a.assignment_date)
      })));
      setHolidays(holidaysData.map(h => ({
        ...h,
        date: new Date(h.holiday_date)
      })));
    });
  }, []);

  return (
    <Planning
      users={users}
      initialResources={resources}
      initialAssignments={assignments}
      holidays={holidays}
      onAssignmentsChange={handleAssignmentsChange}
      onResourcesChange={handleResourcesChange}
    />
  );
};
```

### 2. Sauvegarde des modifications

```tsx
const handleAssignmentsChange = async (assignments: Assignment[]) => {
  try {
    // Formater les dates pour l'API
    const formattedAssignments = assignments.map(a => ({
      ...a,
      assignment_date: format(a.date, 'yyyy-MM-dd')
    }));

    const response = await fetch('/api/assignments', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formattedAssignments)
    });
    
    if (!response.ok) {
      throw new Error('Erreur lors de la sauvegarde');
    }
  } catch (error) {
    console.error('Erreur:', error);
  }
};

const handleResourcesChange = async (resources: Resource[]) => {
  try {
    const response = await fetch('/api/resources', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(resources)
    });
    
    if (!response.ok) {
      throw new Error('Erreur lors de la sauvegarde');
    }
  } catch (error) {
    console.error('Erreur:', error);
  }
};
```

### 3. Optimistic Updates avec React Query

```tsx
import { useMutation, useQueryClient } from '@tanstack/react-query';

const useUpdateAssignments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (assignments: Assignment[]) => {
      const response = await fetch('/api/assignments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(assignments)
      });
      return response.json();
    },
    onMutate: async (newAssignments) => {
      await queryClient.cancelQueries({ queryKey: ['assignments'] });
      const previousAssignments = queryClient.getQueryData(['assignments']);
      queryClient.setQueryData(['assignments'], newAssignments);
      return { previousAssignments };
    },
    onError: (err, newAssignments, context) => {
      queryClient.setQueryData(['assignments'], context.previousAssignments);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['assignments'] });
    }
  });
};
```

## Gestion des couleurs

Les couleurs sont générées automatiquement pour chaque nouvelle ressource. Les couleurs disponibles sont définies dans `constants/index.ts` :

```typescript
export const COLOR_PALETTES = [
  'bg-blue-500',
  'bg-green-500',
  'bg-purple-500',
  'bg-orange-500',
  'bg-red-500',
  'bg-teal-500',
  'bg-pink-500',
  'bg-yellow-500',
  'bg-indigo-500',
  'bg-cyan-500',
  'bg-emerald-500',
  'bg-rose-500',
  'bg-amber-500',
  'bg-violet-500',
  'bg-lime-500',
  'bg-fuchsia-500',
];
```

Assurez-vous que ces couleurs sont incluses dans votre configuration Tailwind :

```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{tsx,ts}'],
  safelist: [
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-orange-500',
    'bg-red-500',
    'bg-teal-500',
    'bg-pink-500',
    'bg-yellow-500',
    'bg-indigo-500',
    'bg-cyan-500',
    'bg-emerald-500',
    'bg-rose-500',
    'bg-amber-500',
    'bg-violet-500',
    'bg-lime-500',
    'bg-fuchsia-500',
  ]
}
```

## Props du composant Planning

| Prop | Type | Description | Défaut |
|------|------|-------------|---------|
| `users` | `User[]` | Liste des utilisateurs | Requis |
| `initialResources` | `Resource[]` | Ressources initiales | `[]` |
| `initialAssignments` | `Assignment[]` | Assignations initiales | `[]` |
| `holidays` | `Holiday[]` | Jours fériés avec leurs noms | Jours fériés FR 2025 |
| `onAssignmentsChange` | `(assignments: Assignment[]) => void` | Callback changement assignations | - |
| `onResourcesChange` | `(resources: Resource[]) => void` | Callback changement ressources | - |
| `labels` | `PlanningLabels` | Labels personnalisés | Labels FR |
| `locale` | `'fr' \| 'en'` | Locale pour les dates | `'fr'` |

## Fonctionnalités avancées

### Validation côté serveur

```typescript
// Exemple d'endpoint API avec validation
app.post('/api/assignments', async (req, res) => {
  const assignments = req.body;
  
  // Valider qu'un utilisateur n'est pas assigné 
  // à plusieurs ressources en même temps
  const conflicts = findConflicts(assignments);
  
  if (conflicts.length > 0) {
    return res.status(400).json({ 
      error: 'Conflits détectés', 
      conflicts 
    });
  }
  
  // Sauvegarder en base
  await saveAssignments(assignments);
  res.json({ success: true });
});

function findConflicts(assignments: Assignment[]) {
  const conflicts = [];
  const userSchedule = new Map();

  for (const assignment of assignments) {
    if (assignment.isExplicitRemoval) continue;
    
    const key = `${assignment.userId}-${assignment.date}`;
    const existing = userSchedule.get(key);
    
    if (existing) {
      // Vérifier les conflits de période
      if (
        existing.period === 'full' || 
        assignment.period === 'full' ||
        existing.period === assignment.period
      ) {
        conflicts.push({
          userId: assignment.userId,
          date: assignment.date,
          resources: [existing.resourceId, assignment.resourceId]
        });
      }
    } else {
      userSchedule.set(key, assignment);
    }
  }
  
  return conflicts;
}
```

### Export des données

```tsx
const ExportButton = ({ assignments, resources, users }) => {
  const exportToCSV = () => {
    const headers = ['Date', 'Ressource', 'Utilisateur', 'Période'];
    const rows = assignments.map(a => {
      const resource = resources.find(r => r.id === a.resourceId);
      const user = users.find(u => u.id === a.userId);
      return [
        format(a.date, 'dd/MM/yyyy'),
        resource?.nom || '',
        user?.nom || 'Non assigné',
        a.period === 'morning' ? 'Matin' : a.period === 'afternoon' ? 'Après-midi' : 'Journée'
      ];
    });
    
    const csv = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'planning.csv';
    link.click();
  };
  
  return <button onClick={exportToCSV}>Export CSV</button>;
};
```

### Filtrage et recherche

```tsx
const FilteredPlanning = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterResource, setFilterResource] = useState('all');
  
  const filteredUsers = useMemo(() => {
    return users.filter(user => 
      user.nom.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [users, searchTerm]);
  
  const filteredResources = useMemo(() => {
    if (filterResource === 'all') return resources;
    return resources.filter(r => r.id === parseInt(filterResource));
  }, [resources, filterResource]);
  
  return (
    <>
      <div className="p-4 space-x-4">
        <input
          type="text"
          placeholder="Rechercher un utilisateur..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border px-3 py-2 rounded"
        />
        <select
          value={filterResource}
          onChange={(e) => setFilterResource(e.target.value)}
          className="border px-3 py-2 rounded"
        >
          <option value="all">Toutes les ressources</option>
          {resources.map(r => (
            <option key={r.id} value={r.id}>{r.nom}</option>
          ))}
        </select>
      </div>
      
      <Planning
        users={filteredUsers}
        initialResources={filteredResources}
      />
    </>
  );
};
```

## Performance

Pour optimiser les performances avec de grandes quantités de données :

1. **Virtualisation** : Utilisez `react-window` pour virtualiser la timeline
2. **Memoization** : Les hooks et composants utilisent déjà `useMemo` et `useCallback`
3. **Pagination** : Chargez les assignations par mois
4. **Debounce** : Ajoutez un debounce sur les sauvegardes API

```tsx
import { useDebouncedCallback } from 'use-debounce';

const debouncedSave = useDebouncedCallback(
  (assignments) => {
    saveToAPI(assignments);
  },
  500 // Attendre 500ms après le dernier changement
);
```

### Optimisation de la base de données

```sql
-- Création d'une fonction pour nettoyer les anciennes assignations
CREATE OR REPLACE FUNCTION cleanup_old_assignments()
RETURNS void AS $$
BEGIN
  DELETE FROM assignments
  WHERE assignment_date < CURRENT_DATE - INTERVAL '6 months'
  AND created_at < CURRENT_DATE - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql;

-- Création d'un job périodique (avec pg_cron)
SELECT cron.schedule('cleanup-old-assignments', '0 0 * * 0', 'SELECT cleanup_old_assignments()');
```

## Dépannage

### Erreurs TypeScript courantes

1. **"Type 'User | null | undefined' is not assignable to type 'User | undefined'"**
   - Solution : Vérifiez que vous utilisez `undefined` au lieu de `null` pour les valeurs manquantes

2. **"Unexpected any"**
   - Solution : Créez des interfaces spécifiques pour vos types au lieu d'utiliser `any`

3. **Erreurs d'import de types**
   - Solution : Utilisez `import type` pour les imports de types uniquement

### Problèmes de styles

1. **Les couleurs ne s'affichent pas**
   - Vérifiez que les classes Tailwind sont dans le `safelist` de votre configuration
   - Assurez-vous que les classes utilisées existent dans Tailwind

2. **Layout cassé**
   - Vérifiez que tous les conteneurs ont les bonnes largeurs
   - Assurez-vous que le CSS de Tailwind est importé

3. **Problèmes de responsive**
   - Le composant utilise `xl:` breakpoint (1280px) par défaut
   - Ajustez selon vos besoins avec `lg:` (1024px) ou `2xl:` (1536px)

### Problèmes de performance

1. **Ralentissements avec beaucoup de données**
   - Limitez l'affichage à un mois à la fois
   - Utilisez la virtualisation pour les longues listes
   - Optimisez les requêtes SQL avec des index appropriés

2. **Trop de requêtes API**
   - Utilisez le debouncing pour les sauvegardes
   - Groupez les opérations avec des transactions
   - Implémentez un cache côté client

## Support

Pour toute question ou problème, consultez la documentation des dépendances :
- [date-fns](https://date-fns.org/)
- [Radix UI](https://www.radix-ui.com/)
- [Lucide Icons](https://lucide.dev/)
- [Tailwind CSS](https://tailwindcss.com/)