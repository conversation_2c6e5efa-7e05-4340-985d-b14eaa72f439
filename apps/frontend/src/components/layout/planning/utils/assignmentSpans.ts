import type { ITourAssignmentEntity } from '@/interfaces/entity/i-tour-assignment-entity';
import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { addDays, differenceInDays, format, isSameDay } from 'date-fns';
// Updated interfaces to use backend entities
interface TimelineAssignment {
  userId: string;
  tourIdentifier: ITourIdentifier;
  date: Date;
}

export interface AssignmentSpan {
  startDate: Date;
  endDate: Date;
  spanDays: number;
  totalAssignmentDays: number; // Vraie durée de l'assignation (fromDate à toDate)
  user: IUserEntity;
  assignment: TimelineAssignment;
  startIndex: number; // Index du jour de début dans le mois
  stackIndex: number; // Index d'empilement pour les assignations multiples (maintenant slot fixe)
  isOpenEnded: boolean; // true si toDate est indéfini
  backendFromDate?: Date; // Vraie date de début de l'assignation depuis le backend
}

export interface AssignmentSpansResult {
  spans: AssignmentSpan[];
  totalSlots: number; // Nombre total de slots nécessaires pour cette tournée
}

/**
 * Calcule les slots fixes pour les utilisateurs d'une tournée
 * Chaque utilisateur ayant au moins une assignation sur la tournée dans la période
 * se voit attribuer un slot fixe basé sur l'ordre alphabétique de son nom
 */
function calculateUserSlotsForTour(
  tourIdentifier: ITourIdentifier,
  daysInMonth: Date[],
  getAssignments: (tourIdentifier: ITourIdentifier, date: Date) => TimelineAssignment[],
  users: IUserEntity[],
): { slotMapping: Map<string, number>; totalSlots: number } {
  // 1. Collecter tous les userIds qui ont une assignation sur cette tournée dans la période
  const userIdsWithAssignments = new Set<string>();

  daysInMonth.forEach((date) => {
    const assignments = getAssignments(tourIdentifier, date);
    assignments.forEach((assignment) => {
      if (assignment.userId) {
        userIdsWithAssignments.add(assignment.userId);
      }
    });
  });

  // 2. Récupérer les objets utilisateur et trier par nom complet pour stabilité
  const usersWithAssignments = users
    .filter((user) => userIdsWithAssignments.has(user.id))
    .sort((a, b) => {
      const nameA = `${a.firstName || ''} ${a.lastName || ''}`.trim();
      const nameB = `${b.firstName || ''} ${b.lastName || ''}`.trim();
      return nameA.localeCompare(nameB);
    });

  // 3. Créer le mapping userId -> slotIndex
  const slotMapping = new Map<string, number>();
  usersWithAssignments.forEach((user, index) => {
    slotMapping.set(user.id, index);
  });

  return {
    slotMapping,
    totalSlots: usersWithAssignments.length,
  };
}

/**
 * Calcule les spans d'assignation pour une tournée donnée
 * Supporte les assignations multiples par jour avec empilement
 */
export function calculateAssignmentSpans(
  tourIdentifier: ITourIdentifier,
  daysInMonth: Date[],
  getAssignments: (tourIdentifier: ITourIdentifier, date: Date) => TimelineAssignment[],
  users: IUserEntity[],
  backendAssignments?: ITourAssignmentEntity[], // Assignations backend avec vraies dates
): AssignmentSpan[] {
  const spans: AssignmentSpan[] = [];
  const processedUserDays = new Map<string, Set<string>>(); // date -> Set of userIds (string now)

  daysInMonth.forEach((currentDate, dayIndex) => {
    const dayKey = format(currentDate, 'yyyy-MM-dd');

    if (!processedUserDays.has(dayKey)) {
      processedUserDays.set(dayKey, new Set());
    }

    const assignments = getAssignments(tourIdentifier, currentDate);
    if (!assignments.length) {
      return;
    }

    assignments.forEach((assignment, assignmentIndex) => {
      if (!assignment.userId) return;

      // Skip si cet utilisateur a déjà été traité pour ce jour
      if (processedUserDays.get(dayKey)?.has(assignment.userId)) {
        return;
      }

      const user = users.find((u) => u.id === assignment.userId);
      if (!user) {
        return;
      }

      // Trouver la fin du span (jours consécutifs avec le même utilisateur)
      let endDate = currentDate;
      let spanDays = 1;
      let endIndex = dayIndex;
      let finalEndDate = currentDate;

      // Chercher les jours suivants avec le même utilisateur
      for (let i = dayIndex + 1; i < daysInMonth.length; i++) {
        const nextDate = daysInMonth[i];
        const nextAssignments = getAssignments(tourIdentifier, nextDate);

        // Vérifier si cet utilisateur est assigné le jour suivant
        const hasUserNextDay = nextAssignments.some((a) => a.userId === assignment.userId);

        if (!hasUserNextDay) {
          break;
        }

        // Vérifier que c'est bien le jour suivant (consécutif)
        const expectedNextDate = addDays(endDate, 1);
        if (!isSameDay(nextDate, expectedNextDate)) {
          break;
        }

        endDate = nextDate;
        spanDays++;
        endIndex = i;
      }

      // Calculer la vraie durée de l'assignation
      let totalAssignmentDays = spanDays;
      let isOpenEnded = false;
      let backendFromDate: Date | undefined = undefined;
      finalEndDate = endDate;

      // Si on a les assignations backend, calculer la vraie durée
      if (backendAssignments && backendAssignments.length > 0) {
        // Trouver une assignation backend pour ce tour et qui couvre la date actuelle
        const backendAssignment = backendAssignments.find((ba) => {
          if (ba.tourIdentifier?.number !== tourIdentifier.number) {
            return false;
          }

          const assignmentFromDate = new Date(ba.fromDate);
          const assignmentToDate = ba.toDate ? new Date(ba.toDate) : null;

          // Vérifier si currentDate est dans la plage [fromDate, toDate] ou si toDate est null
          if (assignmentToDate) {
            return currentDate >= assignmentFromDate && currentDate <= assignmentToDate;
          } else {
            // Pour les assignations ouvertes, vérifier seulement si on est après fromDate
            return currentDate >= assignmentFromDate;
          }
        });

        if (backendAssignment) {
          const assignmentFromDate = new Date(backendAssignment.fromDate);
          const assignmentToDate = backendAssignment.toDate
            ? new Date(backendAssignment.toDate)
            : null;

          backendFromDate = assignmentFromDate;

          if (assignmentToDate) {
            totalAssignmentDays = differenceInDays(assignmentToDate, assignmentFromDate) + 1;
            // Si l'assignation backend se termine avant notre span calculé, ajuster
            if (assignmentToDate < finalEndDate) {
              finalEndDate = assignmentToDate;
              spanDays = differenceInDays(finalEndDate, currentDate) + 1;
            }
          } else {
            // Assignation ouverte (toDate est null)
            isOpenEnded = true;
            totalAssignmentDays = -1; // Indéfini
            // Étendre le span jusqu'à la fin du mois visible
            const lastDayOfMonth = daysInMonth[daysInMonth.length - 1];
            finalEndDate = lastDayOfMonth;
            spanDays = differenceInDays(finalEndDate, currentDate) + 1;
          }
        }
      }

      // Recalculer endIndex avec la finalEndDate correcte
      let finalEndIndex = endIndex;
      if (finalEndDate !== endDate) {
        finalEndIndex = daysInMonth.findIndex((day) => isSameDay(day, finalEndDate));
        if (finalEndIndex === -1) {
          finalEndIndex = daysInMonth.length - 1; // Fin du mois si pas trouvé
        }
      }

      // Marquer tous les jours de ce span pour cet utilisateur comme traités
      for (let i = dayIndex; i <= finalEndIndex; i++) {
        const dayToMark = format(daysInMonth[i], 'yyyy-MM-dd');
        if (!processedUserDays.has(dayToMark)) {
          processedUserDays.set(dayToMark, new Set());
        }
        processedUserDays.get(dayToMark)!.add(assignment.userId);
      }

      // Créer le span avec l'index d'empilement et les vraies durées
      spans.push({
        startDate: currentDate,
        endDate: finalEndDate,
        spanDays,
        totalAssignmentDays,
        isOpenEnded,
        user,
        assignment,
        startIndex: dayIndex,
        stackIndex: assignmentIndex,
        backendFromDate,
      });
    });
  });

  return spans;
}

/**
 * Calcule les spans d'assignation avec des slots fixes pour éviter les chevauchements visuels
 * Traite chaque assignation backend individuellement pour éviter les problèmes de chevauchement
 */
export function calculateAssignmentSpansWithFixedSlots(
  tourIdentifier: ITourIdentifier,
  daysInMonth: Date[],
  getAssignments: (tourIdentifier: ITourIdentifier, date: Date) => TimelineAssignment[],
  users: IUserEntity[],
  backendAssignments?: ITourAssignmentEntity[],
): AssignmentSpansResult {
  // 1. Calculer les slots fixes pour cette tournée
  const { slotMapping, totalSlots } = calculateUserSlotsForTour(
    tourIdentifier,
    daysInMonth,
    getAssignments,
    users,
  );

  const spans: AssignmentSpan[] = [];

  // Si pas d'assignations backend, fallback vers l'ancienne méthode
  if (!backendAssignments || backendAssignments.length === 0) {
    return {
      spans: [],
      totalSlots,
    };
  }

  // Filtrer les assignations pour cette tournée
  const relevantAssignments = backendAssignments.filter(
    (assignment) =>
      assignment.tourIdentifier?.number === tourIdentifier.number &&
      assignment.tourIdentifier?.type === tourIdentifier.type,
  );

  // Dates de début et fin du mois visible
  const monthStart = daysInMonth[0];
  const monthEnd = daysInMonth[daysInMonth.length - 1];

  // Traiter chaque assignation backend individuellement
  relevantAssignments.forEach((backendAssignment) => {
    if (!backendAssignment.user?.id) return;

    const user = users.find((u) => u.id === backendAssignment.user.id);
    if (!user) return;

    const fixedSlotIndex = slotMapping.get(backendAssignment.user.id);
    if (fixedSlotIndex === undefined) return;

    const assignmentFromDate = new Date(backendAssignment.fromDate);
    const assignmentToDate = backendAssignment.toDate ? new Date(backendAssignment.toDate) : null;

    // Calculer l'intersection avec le mois visible
    const visibleStartDate = assignmentFromDate > monthStart ? assignmentFromDate : monthStart;
    let visibleEndDate: Date;
    let isOpenEnded = false;
    let totalAssignmentDays: number;

    if (assignmentToDate) {
      // Assignation avec date de fin définie
      visibleEndDate = assignmentToDate < monthEnd ? assignmentToDate : monthEnd;
      totalAssignmentDays = differenceInDays(assignmentToDate, assignmentFromDate) + 1;
      isOpenEnded = false;
    } else {
      // Assignation sans date de fin (toDate = null) - assignation ouverte/infinie
      // L'assignation s'étend jusqu'à la fin du mois visible
      visibleEndDate = monthEnd;
      totalAssignmentDays = -1; // Indéfini car ouvert
      isOpenEnded = true;
    }

    // Vérifier que l'assignation intersecte avec le mois visible
    if (visibleStartDate > monthEnd || visibleEndDate < monthStart) {
      return; // Pas d'intersection
    }

    // Calculer les indices dans le mois
    const startIndex = daysInMonth.findIndex((day) => isSameDay(day, visibleStartDate));
    const endIndex = daysInMonth.findIndex((day) => isSameDay(day, visibleEndDate));

    if (startIndex === -1 || endIndex === -1) {
      return; // Dates non trouvées dans le mois
    }

    const spanDays = endIndex - startIndex + 1;

    // Créer l'assignation temporaire pour la compatibilité
    const timelineAssignment: TimelineAssignment = {
      userId: backendAssignment.user.id,
      tourIdentifier,
      date: visibleStartDate,
    };

    // Créer le span
    spans.push({
      startDate: visibleStartDate,
      endDate: visibleEndDate,
      spanDays,
      totalAssignmentDays,
      isOpenEnded,
      user,
      assignment: timelineAssignment,
      startIndex,
      stackIndex: fixedSlotIndex,
      backendFromDate: assignmentFromDate,
    });
  });

  return {
    spans,
    totalSlots,
  };
}
