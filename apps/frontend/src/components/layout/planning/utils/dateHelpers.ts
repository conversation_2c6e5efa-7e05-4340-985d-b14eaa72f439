import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { PlanningHoliday } from '../types';

export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('fr-FR', {
    weekday: 'short',
    day: 'numeric',
    month: 'short',
  });
};

export const formatDateForInput = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'yyyy-MM-dd');
};

export const getHoliday = (
  date: Date,
  holidays: PlanningHoliday[],
): PlanningHoliday | undefined => {
  return holidays.find((holiday) => {
    const holidayDate = new Date(holiday.date);
    return (
      holidayDate.getDate() === date.getDate() &&
      holidayDate.getMonth() === date.getMonth() &&
      holidayDate.getFullYear() === date.getFullYear()
    );
  });
};

export const isHoliday = (date: Date, holidays: PlanningHoliday[]): boolean => {
  return getHoliday(date, holidays) !== undefined;
};
