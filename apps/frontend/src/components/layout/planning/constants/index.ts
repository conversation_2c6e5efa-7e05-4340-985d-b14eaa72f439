import type { PlanningLabels } from '../types';

export const CELL_WIDTH = 120;
export const CELL_HEIGHT = 32;
export const SIDEBAR_WIDTH = 192;

// Palettes de couleurs pour générer des couleurs aléatoires
export const COLOR_PALETTES = [
  'bg-blue-500',
  'bg-green-500',
  'bg-purple-500',
  'bg-orange-500',
  'bg-red-500',
  'bg-teal-500',
  'bg-pink-500',
  'bg-yellow-500',
  'bg-indigo-500',
  'bg-cyan-500',
  'bg-emerald-500',
  'bg-rose-500',
  'bg-amber-500',
  'bg-violet-500',
  'bg-lime-500',
  'bg-fuchsia-500',
];

// Labels par défaut en français
export const DEFAULT_LABELS: Required<PlanningLabels> = {
  title: 'Planning',
  userLabel: 'Utilisateur',
  userLabelPlural: 'Utilisateurs',
  todayLabel: "Aujourd'hui",
  holidayLabel: 'Férié',
  dragDropHintLabel: 'Utilisateurs disponibles (glisser-déposer)',
};
