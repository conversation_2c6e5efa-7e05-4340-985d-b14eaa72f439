import type { ITourAssignmentEntity } from '@/interfaces/entity/i-tour-assignment-entity';
import type { ITourEntity } from '@/interfaces/entity/i-tour-entity';
import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { TourStatus, TourType } from '@/interfaces/enum/tour.enums';
import { UserRole } from '@/interfaces/enum/user-role.enum';
import { queryClient } from '@/lib/api-client/tanstack-query';
import type {
  CreateTourAssignmentDto,
  UpdateTourAssignmentDto,
} from '@/lib/api-service/planning-api-service';
import { planningApiService } from '@/lib/api-service/planning-api-service';
import { useHolidaysQuery } from '@/lib/hooks/queries/holiday-queries';
import {
  planningQueryKeys,
  useTourAssignmentsQuery,
  useTourIdentifiersQuery,
  useToursQuery,
} from '@/lib/hooks/queries/planning-queries';
import { useUsersQuery } from '@/lib/hooks/queries/user-queries';
import { keycloakService } from '@/lib/services/keycloak.service';
import { endOfMonth, format, startOfMonth } from 'date-fns';
import React, { createContext, useCallback, useContext, useMemo, useRef, useState } from 'react';
import type { PlanningHoliday, PlanningTourItem } from '../types';

interface PlanningContextData {
  // Users
  deliverers: IUserEntity[];
  isLoadingUsers: boolean;
  usersError: string | null;

  // Planning data
  currentMonth: Date;
  tourIdentifiers: ITourIdentifier[];
  assignments: ITourAssignmentEntity[];
  planningData: PlanningTourItem[];
  isLoadingPlanning: boolean;
  planningError: string | null;

  // Holidays
  holidays: PlanningHoliday[];
  isLoadingHolidays: boolean;
  holidaysError: string | null;

  // Scroll synchronization
  resourceListRef: React.RefObject<HTMLDivElement | null>;
  timelineRef: React.RefObject<HTMLDivElement | null>;
  syncScroll: (source: 'resourceList' | 'timeline', scrollTop: number) => void;

  // Actions
  setCurrentMonth: (date: Date) => void;
  refetchUsers: () => void;
  refetchPlanning: () => void;
  refetchHolidays: () => void;
  assignUserToTour: (
    tourIdentifier: ITourIdentifier,
    userId: string,
    fromDate: Date,
    toDate?: Date | null,
    notes?: string,
  ) => Promise<{ success: boolean; data?: ITourAssignmentEntity; error?: string }>;
  updateAssignment: (
    id: string,
    updates: {
      fromDate?: Date;
      toDate?: Date | null;
      userId?: string;
      notes?: string;
    },
  ) => Promise<{ success: boolean; data?: ITourAssignmentEntity; error?: string }>;
  deleteAssignment: (id: string) => Promise<{ success: boolean; error?: string }>;
  getAssignmentsByDate: (date: Date) => ITourAssignmentEntity[];
}

const PlanningContext = createContext<PlanningContextData | null>(null);

export const usePlanningContext = () => {
  const context = useContext(PlanningContext);
  if (!context) {
    throw new Error('usePlanningContext must be used within PlanningProvider');
  }
  return context;
};

interface PlanningProviderProps {
  children: React.ReactNode;
  initialMonth?: Date;
}

export const PlanningProvider: React.FC<PlanningProviderProps> = ({
  children,
  initialMonth = new Date(),
}) => {
  // Month state
  const [currentMonth, setCurrentMonthState] = useState<Date>(startOfMonth(initialMonth));

  // Scroll synchronization refs
  const resourceListRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const isSyncing = useRef(false);

  // Calculate date range for queries
  const fromDate = useMemo(() => startOfMonth(currentMonth), [currentMonth]);
  const toDate = useMemo(() => endOfMonth(currentMonth), [currentMonth]);
  const currentYear = useMemo(() => currentMonth.getFullYear(), [currentMonth]);

  // Query hooks
  const usersQuery = useUsersQuery();
  const tourIdentifiersQuery = useTourIdentifiersQuery(fromDate, toDate);
  const toursQuery = useToursQuery(fromDate, toDate);
  const assignmentsQuery = useTourAssignmentsQuery(fromDate, toDate);
  const holidaysQuery = useHolidaysQuery(currentYear);

  // Extract data from queries with memoization
  const allUsers = useMemo(() => usersQuery.data ?? [], [usersQuery.data]);
  const tourIdentifiers = useMemo(
    () => tourIdentifiersQuery.data ?? [],
    [tourIdentifiersQuery.data],
  );
  const tours = useMemo(() => toursQuery.data ?? [], [toursQuery.data]);
  const assignments = useMemo(() => assignmentsQuery.data ?? [], [assignmentsQuery.data]);
  const allHolidays = useMemo(() => holidaysQuery.data ?? [], [holidaysQuery.data]);

  // Computed values from query data
  const deliverers = useMemo((): IUserEntity[] => {
    if (!keycloakService.keycloak.authenticated) {
      return [];
    }

    const currentUserRoles = keycloakService.getRoles();
    const isManager =
      currentUserRoles.includes(UserRole.Manager) || currentUserRoles.includes(UserRole.Admin);

    if (!isManager) {
      return [];
    }

    return allUsers.filter((user) => user.username !== undefined);
  }, [allUsers]);

  const holidays = useMemo((): PlanningHoliday[] => {
    return allHolidays;
  }, [allHolidays]);

  // Loading and error states
  const isLoadingUsers = usersQuery.isLoading;
  const usersError = usersQuery.error?.message ?? null;
  const isLoadingPlanning =
    tourIdentifiersQuery.isLoading || toursQuery.isLoading || assignmentsQuery.isLoading;
  const planningError =
    tourIdentifiersQuery.error?.message ??
    toursQuery.error?.message ??
    assignmentsQuery.error?.message ??
    null;
  const isLoadingHolidays = holidaysQuery.isLoading;
  const holidaysError = holidaysQuery.error?.message ?? null;

  // Memoized planning data calculation - use real tour data from backend
  const planningData = useMemo((): PlanningTourItem[] => {
    // Create a set of all unique tour identifiers from both tourIdentifiers and assignments
    const allTourIdentifiers = new Map<string, ITourIdentifier>();

    // Add tour identifiers from the API
    tourIdentifiers.forEach((tourIdentifier) => {
      const key = `${tourIdentifier.number}-${tourIdentifier.type}`;
      allTourIdentifiers.set(key, tourIdentifier);
    });

    // Add tour identifiers from assignments (in case some are missing from the API)
    assignments.forEach((assignment) => {
      if (assignment.tourIdentifier) {
        const key = `${assignment.tourIdentifier.number}-${assignment.tourIdentifier.type}`;
        if (!allTourIdentifiers.has(key)) {
          allTourIdentifiers.set(key, assignment.tourIdentifier);
        }
      }
    });

    // Group tours by tourIdentifier for the current month
    const tourMap = new Map<string, ITourEntity>();
    tours.forEach((tour) => {
      const key = `${tour.tourIdentifier.number}-${tour.tourIdentifier.type}`;
      tourMap.set(key, tour);
    });

    return Array.from(allTourIdentifiers.values()).map((tourIdentifier) => {
      const key = `${tourIdentifier.number}-${tourIdentifier.type}`;
      const tour = tourMap.get(key);

      const tourAssignments = assignments.filter(
        (assignment) =>
          assignment.tourIdentifier?.number === tourIdentifier.number &&
          assignment.tourIdentifier?.type === tourIdentifier.type,
      );

      const assignedUsersMap = new Map<string, IUserEntity>();
      tourAssignments.forEach((assignment) => {
        if (assignment.user) {
          assignedUsersMap.set(assignment.user.id, assignment.user);
        }
      });

      // Use real tour data if available, otherwise fallback to sensible defaults
      return {
        tourIdentifier,
        deliveryDate: tour ? new Date(tour.deliveryDate) : new Date(),
        status: tour?.status || TourStatus.Planned,
        assignedUsers: Array.from(assignedUsersMap.values()),
        color: tourIdentifier.type === TourType.Frozen ? '#8B5CF6' : '#3B82F6', // Purple for frozen tours
        displayName: `Tournée ${tourIdentifier.number}${tourIdentifier.type === TourType.Frozen ? ' (TK)' : ''}`,
      };
    });
  }, [tourIdentifiers, tours, assignments]);

  // Refetch functions using TanStack Query
  const refetchUsers = useCallback(() => {
    usersQuery.refetch();
  }, [usersQuery]);

  const refetchPlanning = useCallback(() => {
    tourIdentifiersQuery.refetch();
    toursQuery.refetch();
    assignmentsQuery.refetch();
  }, [tourIdentifiersQuery, toursQuery, assignmentsQuery]);

  const refetchHolidays = useCallback(() => {
    holidaysQuery.refetch();
  }, [holidaysQuery]);

  // API Actions with query invalidation
  const assignUserToTour = useCallback(
    async (
      tourIdentifier: ITourIdentifier,
      userId: string,
      fromDate: Date,
      toDate?: Date | null,
      notes?: string,
    ) => {
      try {
        const data: CreateTourAssignmentDto = {
          tourIdentifier,
          fromDate: format(fromDate, 'yyyy-MM-dd'),
          userId,
          notes,
        };
        if (toDate) {
          data.toDate = format(toDate, 'yyyy-MM-dd');
        }

        const assignment = await planningApiService.createTourAssignment(data);

        // Invalidate assignments queries
        queryClient.invalidateQueries({
          queryKey: planningQueryKeys.assignments(
            startOfMonth(currentMonth),
            endOfMonth(currentMonth),
          ),
        });

        return { success: true, data: assignment };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
      }
    },
    [currentMonth],
  );

  const updateAssignment = useCallback(
    async (
      id: string,
      updates: {
        fromDate?: Date;
        toDate?: Date | null;
        userId?: string;
        notes?: string;
      },
    ) => {
      try {
        const data: UpdateTourAssignmentDto = {
          userId: updates.userId,
          notes: updates.notes,
          fromDate: updates.fromDate ? format(updates.fromDate, 'yyyy-MM-dd') : undefined,
          toDate:
            updates.toDate === null
              ? null
              : updates.toDate
                ? format(updates.toDate, 'yyyy-MM-dd')
                : undefined,
        };

        const updatedAssignment = await planningApiService.updateTourAssignment(id, data);

        // Invalidate all related queries to ensure UI consistency
        queryClient.invalidateQueries({
          queryKey: planningQueryKeys.assignments(
            startOfMonth(currentMonth),
            endOfMonth(currentMonth),
          ),
        });

        // Also invalidate planning data query to refresh the UI
        queryClient.invalidateQueries({ queryKey: planningQueryKeys.all });

        return { success: true, data: updatedAssignment };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
      }
    },
    [currentMonth],
  );

  const deleteAssignment = useCallback(
    async (id: string) => {
      try {
        await planningApiService.deleteTourAssignment(id);

        // Invalidate assignments queries
        queryClient.invalidateQueries({
          queryKey: planningQueryKeys.assignments(
            startOfMonth(currentMonth),
            endOfMonth(currentMonth),
          ),
        });

        return { success: true };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
      }
    },
    [currentMonth],
  );

  // Helper functions
  const getAssignmentsByDate = useCallback(
    (date: Date): ITourAssignmentEntity[] => {
      const targetDate = format(date, 'yyyy-MM-dd');
      return assignments.filter((assignment) => {
        const fromDate = format(new Date(assignment.fromDate), 'yyyy-MM-dd');
        const toDate = assignment.toDate
          ? format(new Date(assignment.toDate), 'yyyy-MM-dd')
          : fromDate;
        return targetDate >= fromDate && targetDate <= toDate;
      });
    },
    [assignments],
  );

  const setCurrentMonth = useCallback((date: Date) => {
    setCurrentMonthState(startOfMonth(date));
  }, []);

  // Scroll synchronization function
  const syncScroll = useCallback((source: 'resourceList' | 'timeline', scrollTop: number) => {
    if (isSyncing.current) return;

    isSyncing.current = true;

    if (source === 'resourceList' && timelineRef.current) {
      timelineRef.current.scrollTop = scrollTop;
    } else if (source === 'timeline' && resourceListRef.current) {
      resourceListRef.current.scrollTop = scrollTop;
    }

    // Reset sync flag after a short delay
    setTimeout(() => {
      isSyncing.current = false;
    }, 10);
  }, []);

  const contextValue: PlanningContextData = {
    // Users
    deliverers,
    isLoadingUsers,
    usersError,

    // Planning data
    currentMonth,
    tourIdentifiers,
    assignments,
    planningData,
    isLoadingPlanning,
    planningError,

    // Holidays
    holidays,
    isLoadingHolidays,
    holidaysError,

    // Scroll synchronization
    resourceListRef,
    timelineRef,
    syncScroll,

    // Actions
    setCurrentMonth,
    refetchUsers,
    refetchPlanning,
    refetchHolidays,
    assignUserToTour,
    updateAssignment,
    deleteAssignment,
    getAssignmentsByDate,
  };

  return <PlanningContext.Provider value={contextValue}>{children}</PlanningContext.Provider>;
};
