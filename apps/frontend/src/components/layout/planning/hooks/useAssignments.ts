import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import { format } from 'date-fns';
import { useCallback } from 'react';
import type { PlanningTourItem } from '../types';
import { usePlanningData } from './usePlanningData';
import { useTourAssignments } from './useTourAssignments';

interface UseAssignmentsProps {
  currentMonth: Date;
  planningData?: PlanningTourItem[];
  onAssignmentsChange?: () => void;
}

interface TimelineAssignment {
  userId: string;
  tourIdentifier: ITourIdentifier;
  date: Date;
}

export const useAssignments = ({
  currentMonth,
  planningData: propsPlanningData,
  onAssignmentsChange,
}: UseAssignmentsProps) => {
  const {
    planningData: contextPlanningData,
    assignments: backendAssignments,
    tourIdentifiers,
  } = usePlanningData(currentMonth);

  // Use provided planningData or fallback to context data
  const planningData = propsPlanningData || contextPlanningData;
  const { assignUserToTour, deleteAssignment, updateAssignment } = useTourAssignments();

  const findTourByIdentifier = useCallback(
    (tourIdentifier: ITourIdentifier): PlanningTourItem | undefined => {
      return planningData.find((tour: PlanningTourItem) => {
        return (
          tour.tourIdentifier.number === tourIdentifier.number &&
          tour.tourIdentifier.type === tourIdentifier.type
        );
      });
    },
    [planningData],
  );

  const getAssignments = useCallback(
    (tourIdentifier: ITourIdentifier, date: Date): TimelineAssignment[] => {
      // Chercher toutes les assignations pour cette date et ce tour
      const targetDate = format(date, 'yyyy-MM-dd');
      const matchingAssignments = backendAssignments.filter((assignment) => {
        if (!assignment.tourIdentifier) return false;

        const fromDate = format(new Date(assignment.fromDate), 'yyyy-MM-dd');
        const toDate = assignment.toDate ? format(new Date(assignment.toDate), 'yyyy-MM-dd') : null; // Keep null for open-ended assignments

        const isInDateRange = assignment.toDate
          ? targetDate >= fromDate && targetDate <= (toDate || '')
          : targetDate >= fromDate; // Open-ended assignment: only check fromDate

        return (
          assignment.tourIdentifier.number === tourIdentifier.number &&
          assignment.tourIdentifier.type === tourIdentifier.type &&
          isInDateRange
        );
      });

      return matchingAssignments
        .filter((assignment) => assignment.user)
        .map((assignment) => ({
          userId: assignment.user!.id,
          tourIdentifier,
          date,
        }));
    },
    [backendAssignments],
  );

  const assignUserToPeriod = useCallback(
    async (
      userId: string | null,
      tourIdentifier: ITourIdentifier,
      startDate: Date,
      endDate: Date,
    ) => {
      const tour = findTourByIdentifier(tourIdentifier);
      if (!tour) {
        console.warn('No tour found for tourIdentifier:', tourIdentifier);
        return;
      }

      if (userId === null) {
        const existingAssignment = backendAssignments.find(
          (a) =>
            a.tourIdentifier.number === tour.tourIdentifier.number &&
            a.tourIdentifier.type === tour.tourIdentifier.type &&
            format(new Date(a.fromDate), 'yyyy-MM-dd') === format(startDate, 'yyyy-MM-dd'),
        );

        if (existingAssignment && existingAssignment.id) {
          await deleteAssignment(existingAssignment.id);
        }
      } else {
        const result = await assignUserToTour(tour.tourIdentifier, userId, startDate, endDate);

        if (result.success) {
          onAssignmentsChange?.();
        }
      }
    },
    [
      findTourByIdentifier,
      backendAssignments,
      assignUserToTour,
      deleteAssignment,
      onAssignmentsChange,
    ],
  );

  const moveAssignment = useCallback(
    async (
      userId: string,
      sourceTourIdentifier: ITourIdentifier,
      sourceDate: Date,
      targetTourIdentifier: ITourIdentifier,
      targetDate: Date,
    ) => {
      const sourceTour = findTourByIdentifier(sourceTourIdentifier);
      const targetTour = findTourByIdentifier(targetTourIdentifier);

      if (!sourceTour || !targetTour) {
        console.warn('Source or target tour not found');
        return;
      }

      const sourceAssignment = backendAssignments.find(
        (a) =>
          a.tourIdentifier.number === sourceTour.tourIdentifier.number &&
          a.tourIdentifier.type === sourceTour.tourIdentifier.type &&
          format(new Date(a.fromDate), 'yyyy-MM-dd') === format(sourceDate, 'yyyy-MM-dd') &&
          a.userId === userId,
      );

      if (sourceAssignment && sourceAssignment.id) {
        await deleteAssignment(sourceAssignment.id);
      }

      await assignUserToTour(targetTour.tourIdentifier, userId, targetDate, targetDate);
    },
    [findTourByIdentifier, backendAssignments, deleteAssignment, assignUserToTour],
  );

  const removeTourAssignments = useCallback(
    async (tourIdentifier: ITourIdentifier) => {
      const assignmentsToDelete = backendAssignments.filter(
        (a) =>
          a.tourIdentifier.number === tourIdentifier.number &&
          a.tourIdentifier.type === tourIdentifier.type,
      );

      await Promise.all(
        assignmentsToDelete.map((assignment) =>
          assignment.id ? deleteAssignment(assignment.id) : Promise.resolve(),
        ),
      );
    },
    [backendAssignments, deleteAssignment],
  );

  return {
    backendAssignments,
    getAssignments,
    assignUserToPeriod,
    moveAssignment,
    removeTourAssignments,
    updateAssignments: () => {},
  };
};
