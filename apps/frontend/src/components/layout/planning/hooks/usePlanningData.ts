import { useCallback } from 'react';
import { usePlanningContext } from '../context/PlanningContext';

/**
 * Hook to get planning data from context
 * Returns backend entities directly without conversions
 */
export const usePlanningData = (month?: Date) => {
  const {
    currentMonth,
    planningData,
    tourIdentifiers,
    assignments,
    isLoadingPlanning,
    planningError,
    setCurrentMonth,
    refetchPlanning,
    getAssignmentsByDate,
  } = usePlanningContext();

  const navigateToMonth = useCallback(
    (newMonth: Date) => {
      setCurrentMonth(newMonth);
    },
    [setCurrentMonth],
  );

  return {
    currentMonth,
    planningData, // PlanningTourItem[] - backend entities
    tourIdentifiers, // ITourIdentifier[] - backend entities
    assignments, // ITourAssignmentEntity[] - backend entities
    isLoading: isLoadingPlanning,
    errors: { planningError },
    refetch: refetchPlanning,
    navigateToMonth,
    getAssignmentsByDate,
  };
};
