import { usePlanningContext } from '../context/PlanningContext';

/**
 * Hook to get deliverers (users) from planning context
 * Returns backend entities directly
 */
export const useDeliverers = () => {
  const { deliverers, isLoadingUsers, usersError, refetchUsers } = usePlanningContext();

  return {
    deliverers,
    isLoading: isLoadingUsers,
    error: usersError,
    refetch: refetchUsers,
  };
};
