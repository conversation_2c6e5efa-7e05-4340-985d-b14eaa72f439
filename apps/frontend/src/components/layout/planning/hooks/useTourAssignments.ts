import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import { useCallback } from 'react';
import { usePlanningContext } from '../context/PlanningContext';

export interface CreateAssignmentParams {
  tourIdentifier: ITourIdentifier;
  fromDate: Date;
  toDate?: Date | null;
  userId: string;
  notes?: string;
}

export interface UpdateAssignmentParams {
  fromDate?: Date;
  toDate?: Date | null;
  userId?: string;
  notes?: string;
}

/**
 * Hook for tour assignment operations
 * Works directly with backend entities (ITourAssignmentEntity)
 */
export const useTourAssignments = () => {
  const {
    assignUserToTour: contextAssignUserToTour,
    updateAssignment: contextUpdateAssignment,
    deleteAssignment: contextDeleteAssignment,
  } = usePlanningContext();

  const assignUserToTour = useCallback(
    async (
      tourIdentifier: ITourIdentifier,
      userId: string,
      fromDate: Date,
      toDate?: Date | null,
      notes?: string,
    ) => {
      return await contextAssignUserToTour(tourIdentifier, userId, fromDate, toDate, notes);
    },
    [contextAssignUserToTour],
  );

  const updateAssignment = useCallback(
    async (id: string, updates: UpdateAssignmentParams) => {
      return await contextUpdateAssignment(id, updates);
    },
    [contextUpdateAssignment],
  );

  const deleteAssignment = useCallback(
    async (id: string) => {
      return await contextDeleteAssignment(id);
    },
    [contextDeleteAssignment],
  );

  return {
    assignUserToTour,
    updateAssignment,
    deleteAssignment,
  };
};
