import type { ITourAssignmentEntity } from '@/interfaces/entity/i-tour-assignment-entity';
import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { format, isToday, isWeekend } from 'date-fns';
import React, { useEffect } from 'react';
import { CELL_WIDTH } from '../constants';
import { usePlanningContext } from '../context/PlanningContext';
import type { PlanningHoliday, PlanningTourItem } from '../types';
import { calculateAssignmentSpansWithFixedSlots } from '../utils/assignmentSpans';
import { formatDate, getHoliday, isHoliday } from '../utils/dateHelpers';
import {
  getUserAvatar,
  getUserColor,
  getUserDisplayName,
  calculateCellHeight,
} from '../utils/user-utils';

interface SourceCell {
  tourIdentifier: ITourIdentifier;
  date: Date;
}

interface TimelineAssignment {
  userId: string;
  tourIdentifier: ITourIdentifier;
  date: Date;
}

interface TimelineProps {
  planningData: PlanningTourItem[];
  deliverers: IUserEntity[];
  daysInMonth: Date[];
  holidays: PlanningHoliday[];
  getAssignments: (tourIdentifier: ITourIdentifier, date: Date) => TimelineAssignment[];
  onCellClick: (tourIdentifier: ITourIdentifier, date: Date) => void;
  onAssignmentClick: (
    assignment: ITourAssignmentEntity,
    tourIdentifier: ITourIdentifier,
    date: Date,
  ) => void;
  onDragStart: (e: React.DragEvent, userId: string, sourceCell?: SourceCell) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent, tourIdentifier: ITourIdentifier, date: Date) => void;
  backendAssignments?: ITourAssignmentEntity[];
  labels: {
    todayLabel: string;
    holidayLabel: string;
  };
}

export const Timeline: React.FC<TimelineProps> = ({
  planningData,
  deliverers,
  daysInMonth,
  holidays,
  getAssignments,
  onCellClick,
  onAssignmentClick,
  onDragStart,
  onDragOver,
  onDrop,
  backendAssignments,
  labels,
}) => {
  const { timelineRef, syncScroll } = usePlanningContext();

  // Auto-scroll vers le jour courant
  useEffect(() => {
    if (!timelineRef.current) return;

    const todayIndex = daysInMonth.findIndex((date) => isToday(date));
    if (todayIndex === -1) return;

    // Calculer la position pour mettre "aujourd'hui" à gauche de l'écran
    const scrollPosition = Math.max(0, todayIndex * CELL_WIDTH);

    // Scroll avec animation douce
    timelineRef.current.scrollTo({
      left: scrollPosition,
      behavior: 'smooth',
    });
  }, [daysInMonth, timelineRef]);

  // Synchronisation du scroll vertical
  useEffect(() => {
    const handleScroll = () => {
      if (timelineRef.current) {
        syncScroll('timeline', timelineRef.current.scrollTop);
      }
    };

    const currentRef = timelineRef.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll);
      return () => currentRef.removeEventListener('scroll', handleScroll);
    }
  }, [timelineRef, syncScroll]);

  // Note: getAssignments is now passed as prop and returns multiple assignments directly

  return (
    <div ref={timelineRef} className="flex-1 overflow-x-auto overflow-y-auto">
      <div style={{ width: `${daysInMonth.length * CELL_WIDTH}px`, minWidth: '100%' }}>
        {/* En-tête des dates */}
        <div className="h-16 flex border-b bg-gray-100 sticky top-0 z-20">
          {daysInMonth.map((date, index) => {
            const holiday = getHoliday(date, holidays);
            const isWeekendDay = isWeekend(date);
            const isHolidayDate = holiday !== undefined;
            const isTodayDate = isToday(date);

            return (
              <div
                key={index}
                className={`w-30 flex-shrink-0 border-r border-gray-200 p-2 text-center ${
                  isTodayDate
                    ? 'bg-blue-100 border-blue-300'
                    : isWeekendDay || isHolidayDate
                      ? 'bg-gray-300'
                      : ''
                }`}
                style={{ width: `${CELL_WIDTH}px` }}
              >
                <div
                  className={`text-xs font-medium ${
                    isTodayDate
                      ? 'text-blue-600'
                      : isWeekendDay || isHolidayDate
                        ? 'text-gray-500'
                        : 'text-gray-600'
                  }`}
                >
                  {formatDate(date)}
                </div>
                {isTodayDate && (
                  <div className="text-xs text-blue-600 font-bold mt-1">{labels.todayLabel}</div>
                )}
                {holiday && (
                  <div className="text-xs text-red-600 font-medium mt-1">{holiday.name}</div>
                )}
              </div>
            );
          })}
        </div>

        {/* Grille des assignations */}
        {planningData.map((tour) => {
          // Calculer les spans d'assignation avec slots fixes pour cette tournée
          const { spans: assignmentSpans, totalSlots } = calculateAssignmentSpansWithFixedSlots(
            tour.tourIdentifier,
            daysInMonth,
            getAssignments,
            deliverers,
            backendAssignments,
          );

          // Calculer la hauteur basée sur le nombre total de slots (utilisateurs distincts)
          const cellHeight = calculateCellHeight(totalSlots);

          return (
            <div
              key={`${tour.tourIdentifier.number}-${tour.tourIdentifier.type}`}
              className="relative border-b border-gray-200"
              style={{ height: `${cellHeight}px` }}
            >
              {/* Cellules du calendrier avec gestion des interactions */}
              <div className="flex h-full relative">
                {daysInMonth.map((date, dateIndex) => {
                  const isHolidayDate = isWeekend(date) || isHoliday(date, holidays);

                  return (
                    <div
                      key={dateIndex}
                      className={`
                        w-full flex items-center justify-center relative cursor-pointer border-r border-gray-100
                        ${isHolidayDate ? 'bg-gray-200' : ''}
                        hover:bg-gray-50
                        ${isToday(date) && !isHolidayDate ? 'bg-blue-50' : ''}
                      `}
                      style={{ width: `${CELL_WIDTH}px`, height: `${cellHeight}px` }}
                      onClick={() => onCellClick(tour.tourIdentifier, date)}
                      onDragOver={onDragOver}
                      onDrop={(e) => onDrop(e, tour.tourIdentifier, date)}
                    />
                  );
                })}
              </div>

              {/* Couche d'assignations par-dessus avec empilement */}
              <div className="absolute inset-0 pointer-events-none z-10">
                {assignmentSpans.map((span, spanIndex) => (
                  <div
                    key={`span-${spanIndex}`}
                    className="absolute flex items-start justify-start pointer-events-auto overflow-visible"
                    style={{
                      left: `${CELL_WIDTH * span.startIndex + 4}px`,
                      width: `${CELL_WIDTH * span.spanDays - 8}px`,
                      top: `${8 + span.stackIndex * 32}px`, // Empilement vertical
                      height: '28px',
                    }}
                    onDragOver={onDragOver}
                    onDrop={(e) => onDrop(e, tour.tourIdentifier, span.startDate)}
                  >
                    {/* Barre de fond de l'assignation */}
                    <div
                      className="absolute inset-0 rounded"
                      style={{
                        backgroundColor: getUserColor(span.user) || '#3B82F6',
                        opacity: 0.3,
                        height: '24px',
                      }}
                    />

                    {/* Contenu sticky avec texte et avatar */}
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        // Trouver l'assignation backend correspondante
                        const correspondingAssignment = backendAssignments?.find(
                          (assignment) =>
                            assignment.user?.id === span.user.id &&
                            assignment.tourIdentifier.number === tour.tourIdentifier.number &&
                            assignment.tourIdentifier.type === tour.tourIdentifier.type &&
                            format(new Date(assignment.fromDate), 'yyyy-MM-dd') <=
                              format(span.startDate, 'yyyy-MM-dd') &&
                            (!assignment.toDate ||
                              format(new Date(assignment.toDate), 'yyyy-MM-dd') >=
                                format(span.startDate, 'yyyy-MM-dd')),
                        );

                        if (correspondingAssignment) {
                          onAssignmentClick(
                            correspondingAssignment,
                            tour.tourIdentifier,
                            span.startDate,
                          );
                        }
                      }}
                      className="sticky left-0 flex items-center space-x-1 px-2 py-1 rounded text-white text-xs font-medium cursor-pointer relative z-20"
                      style={{
                        backgroundColor: getUserColor(span.user) || '#3B82F6',
                        height: '24px',
                        minWidth: 'max-content',
                        maxWidth: '200px',
                      }}
                      title={`${getUserDisplayName(span.user)} - ${format(span.startDate, 'dd/MM')}${
                        span.spanDays > 1 ? ` au ${format(span.endDate, 'dd/MM')}` : ''
                      }${span.isOpenEnded ? ' (assignation ouverte)' : span.totalAssignmentDays > 0 ? ` (${span.totalAssignmentDays} jours au total)` : ''}`}
                    >
                      <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center text-xs font-bold text-gray-800 flex-shrink-0">
                        <span className="text-[10px] leading-none">{getUserAvatar(span.user)}</span>
                      </div>
                      <span className="truncate text-xs whitespace-nowrap">
                        {getUserDisplayName(span.user).split(' ')[0]}
                        {span.spanDays > 1 && (
                          <span className="ml-1 opacity-75">
                            (
                            {span.isOpenEnded
                              ? `depuis ${format(span.backendFromDate || span.startDate, 'dd/MM')}`
                              : span.totalAssignmentDays > 0
                                ? `${span.totalAssignmentDays}j`
                                : `${span.spanDays}j`}
                            )
                          </span>
                        )}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
