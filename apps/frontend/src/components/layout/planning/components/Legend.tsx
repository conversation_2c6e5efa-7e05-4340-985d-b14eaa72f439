// planning/components/Legend.tsx
import { User } from 'lucide-react';
import React from 'react';

interface LegendProps {
  labels: {
    userLabel: string;
    todayLabel: string;
  };
}

export const Legend: React.FC<LegendProps> = ({ labels }) => {
  return (
    <div className="p-4 border-b border-gray-200">
      <div className="flex items-center space-x-6 text-sm text-gray-600">
        <div className="flex items-center space-x-2">
          <User size={16} />
          <span>{labels.userLabel} assigné</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
          <span>{labels.todayLabel}</span>
        </div>
        <span className="text-xs">
          Défilez horizontalement ou utilisez les flèches pour naviguer
        </span>
      </div>
    </div>
  );
};
