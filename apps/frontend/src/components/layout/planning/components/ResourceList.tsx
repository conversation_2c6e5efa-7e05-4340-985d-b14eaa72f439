import React, { useEffect } from 'react';
import { usePlanningContext } from '../context/PlanningContext';
import type { PlanningTourItem } from '../types';
import { calculateCellHeight } from '../utils/user-utils';
import { calculateAssignmentSpansWithFixedSlots } from '../utils/assignmentSpans';
import type { ITourAssignmentEntity } from '@/interfaces/entity/i-tour-assignment-entity';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';

interface TimelineAssignment {
  userId: string;
  tourIdentifier: ITourIdentifier;
  date: Date;
}

interface ResourceListProps {
  planningData: PlanningTourItem[];
  deliverers: IUserEntity[];
  daysInMonth: Date[];
  getAssignments: (tourIdentifier: ITourIdentifier, date: Date) => TimelineAssignment[];
  backendAssignments?: ITourAssignmentEntity[];
}

export const ResourceList: React.FC<ResourceListProps> = ({
  planningData,
  deliverers,
  daysInMonth,
  getAssignments,
  backendAssignments,
}) => {
  const { resourceListRef, syncScroll } = usePlanningContext();

  useEffect(() => {
    const handleScroll = () => {
      if (resourceListRef.current) {
        syncScroll('resourceList', resourceListRef.current.scrollTop);
      }
    };

    const currentRef = resourceListRef.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll);
      return () => currentRef.removeEventListener('scroll', handleScroll);
    }
  }, [resourceListRef, syncScroll]);

  return (
    <div className="w-48 bg-gray-50 border-r flex flex-col">
      <div
        ref={resourceListRef}
        className="flex-1 overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
      >
        <div className="h-16 border-b bg-gray-100 sticky top-0 z-10"></div>

        {planningData.map((tour) => {
          const tourKey = `${tour.tourIdentifier.number}-${tour.tourIdentifier.type}`;

          // Calculer les slots de manière cohérente avec Timeline
          const { totalSlots } = calculateAssignmentSpansWithFixedSlots(
            tour.tourIdentifier,
            daysInMonth,
            getAssignments,
            deliverers,
            backendAssignments,
          );

          const cellHeight = calculateCellHeight(totalSlots);

          return (
            <div
              key={tourKey}
              className="flex items-center px-2 border-b border-gray-200"
              style={{ height: `${cellHeight}px` }}
            >
              <div className="flex items-center space-x-2 w-full">
                <div
                  className={`w-3 h-3 rounded-full ${tour.color || 'bg-blue-500'} flex-shrink-0`}
                ></div>
                <span
                  className="font-medium text-gray-800 text-sm flex-1 truncate"
                  title={tour.displayName}
                >
                  {tour.displayName}
                </span>
              </div>
            </div>
          );
        })}
      </div>
      {/* Placeholder pour simuler la hauteur de la scrollbar horizontale de Timeline */}
      <div className="h-[15px] bg-gray-50 "></div>
    </div>
  );
};
