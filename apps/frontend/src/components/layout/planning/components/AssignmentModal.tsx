import type { ITourAssignmentEntity } from '@/interfaces/entity/i-tour-assignment-entity';
import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import * as Dialog from '@radix-ui/react-dialog';
import * as Select from '@radix-ui/react-select';
import { format, isAfter, isBefore, startOfDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import { AlertCircle, CalendarDays, ChevronRight, Loader2, X } from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';
import { usePlanningContext } from '../context/PlanningContext';
import { formatDateForInput } from '../utils/dateHelpers';
import { getUserAvatar, getUserDisplayName } from '../utils/user-utils';

interface AssignmentModalProps {
  open: boolean;
  onClose: () => void;
  tourIdentifier: ITourIdentifier;
  tourDisplayName: string;
  clickedDate: Date;
  existingAssignment?: ITourAssignmentEntity;
  deliverers: IUserEntity[];
  onSuccess?: () => void;
}

interface FormState {
  userId: string | null;
  fromDate: Date;
  toDate: Date | null;
}

export const AssignmentModal: React.FC<AssignmentModalProps> = ({
  open,
  onClose,
  tourIdentifier,
  tourDisplayName,
  clickedDate,
  existingAssignment,
  deliverers,
  onSuccess,
}) => {
  const { assignUserToTour, updateAssignment, deleteAssignment } = usePlanningContext();

  // Form state
  const [formState, setFormState] = useState<FormState>({
    userId: null,
    fromDate: clickedDate,
    toDate: clickedDate,
  });

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Initialize form when modal opens
  useEffect(() => {
    if (open) {
      if (existingAssignment) {
        // Edit mode: use existing assignment data
        setFormState({
          userId: existingAssignment.user?.id || null,
          fromDate: new Date(existingAssignment.fromDate),
          toDate: existingAssignment.toDate ? new Date(existingAssignment.toDate) : null,
        });
      } else {
        // Create mode: use clicked date
        setFormState({
          userId: null,
          fromDate: clickedDate,
          toDate: clickedDate,
        });
      }
      setError(null);
      setShowDeleteConfirm(false);
    }
  }, [open, existingAssignment, clickedDate]);

  // Computed values
  const isEditMode = !!existingAssignment;

  const currentUserId = existingAssignment?.user?.id || null;
  const currentFromDate = existingAssignment
    ? format(new Date(existingAssignment.fromDate), 'yyyy-MM-dd')
    : null;
  const currentToDate = existingAssignment
    ? format(
        existingAssignment.toDate
          ? new Date(existingAssignment.toDate)
          : new Date(existingAssignment.fromDate),
        'yyyy-MM-dd',
      )
    : null;

  const userChanged = formState.userId !== currentUserId;
  const fromDateChanged = currentFromDate !== format(formState.fromDate, 'yyyy-MM-dd');
  const toDateChanged =
    currentToDate !== (formState.toDate ? format(formState.toDate, 'yyyy-MM-dd') : null);

  const hasChanges = isEditMode && (userChanged || fromDateChanged || toDateChanged);

  const selectedUser = deliverers.find((u) => u.id === formState.userId);
  const dayCount =
    formState.toDate !== null
      ? Math.floor(
          (formState.toDate.getTime() - formState.fromDate.getTime()) / (1000 * 60 * 60 * 24),
        ) + 1
      : 1;
  const isOpenEnded = formState.toDate === null;
  const today = startOfDay(new Date());

  // Validation
  const validateForm = useCallback((): string | null => {
    if (!formState.userId && !isEditMode) {
      return 'Veuillez sélectionner un utilisateur';
    }

    if (formState.toDate && isBefore(formState.toDate, formState.fromDate)) {
      return 'La date de fin doit être après la date de début';
    }

    return null;
  }, [formState, isEditMode]);

  // Event handlers
  const handleUserChange = (value: string) => {
    setFormState((prev) => ({ ...prev, userId: value === 'none' ? null : value }));
    setError(null);
  };

  const handleFromDateChange = (date: Date) => {
    setFormState((prev) => ({
      ...prev,
      fromDate: date,
      // Adjust toDate if it's before the new fromDate
      toDate: prev.toDate ? (isAfter(prev.toDate, date) ? prev.toDate : date) : null,
    }));
    setError(null);
  };

  const handleToDateChange = (date: Date) => {
    setFormState((prev) => ({ ...prev, toDate: date }));
    setError(null);
  };

  const handleOpenEndedChange = (checked: boolean) => {
    setFormState((prev) => ({
      ...prev,
      toDate: checked ? null : prev.fromDate,
    }));
    setError(null);
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      let result;

      if (isEditMode && existingAssignment?.id) {
        if (formState.userId === null) {
          // Delete assignment
          result = await deleteAssignment(existingAssignment.id);
        } else {
          // Update assignment
          const updateData = {
            userId: formState.userId,
            fromDate: formState.fromDate,
            toDate: formState.toDate,
          };
          result = await updateAssignment(existingAssignment.id, updateData);
        }
      } else if (formState.userId) {
        // Create new assignment
        result = await assignUserToTour(
          tourIdentifier,
          formState.userId,
          formState.fromDate,
          formState.toDate ?? undefined,
        );
      } else {
        setError('Action non valide');
        setIsSubmitting(false);
        return;
      }

      if (result.success) {
        onSuccess?.();
        onClose();
      } else {
        setError(result.error || 'Une erreur est survenue');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur inattendue est survenue');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!isEditMode || !existingAssignment?.id) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await deleteAssignment(existingAssignment.id);
      if (result.success) {
        onSuccess?.();
        onClose();
      } else {
        setError(result.error || 'Erreur lors de la suppression');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur inattendue est survenue');
    } finally {
      setIsSubmitting(false);
      setShowDeleteConfirm(false);
    }
  };

  const getSubmitButtonText = () => {
    if (isSubmitting) return 'En cours...';
    if (isEditMode) {
      if (formState.userId === null) return "Supprimer l'assignation";
      if (hasChanges) return 'Mettre à jour';
      return 'Aucun changement';
    }
    return 'Assigner';
  };

  const isSubmitDisabled = isSubmitting || (isEditMode && !hasChanges && formState.userId !== null);

  return (
    <Dialog.Root open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl p-6 z-50 w-[450px] max-w-[90vw]">
          <Dialog.Title className="text-lg font-semibold mb-4">
            {isEditMode ? "Modifier l'assignation" : 'Nouvelle assignation'}
          </Dialog.Title>

          {/* Tour info */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">
              <span className="font-medium">Tournée:</span> {tourDisplayName}
            </p>
          </div>

          {/* Date selection */}
          <div className="mb-4 space-y-3">
            <div className="flex items-center space-x-2">
              <CalendarDays size={18} className="text-gray-500" />
              <h3 className="text-sm font-medium">Période d'assignation</h3>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="text-xs text-gray-600 font-medium block mb-1">
                  Date de début
                </label>
                <input
                  type="date"
                  value={formatDateForInput(formState.fromDate)}
                  min={formatDateForInput(today)}
                  onChange={(e) => handleFromDateChange(new Date(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isSubmitting}
                />
              </div>
              <div>
                <label className="text-xs text-gray-600 font-medium block mb-1">Date de fin</label>
                <input
                  type="date"
                  value={formatDateForInput(formState.toDate)}
                  min={formatDateForInput(formState.fromDate)}
                  onChange={(e) => handleToDateChange(new Date(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isSubmitting || isOpenEnded}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2 mt-2">
              <input
                type="checkbox"
                id="openEnded"
                checked={isOpenEnded}
                onChange={(e) => handleOpenEndedChange(e.target.checked)}
              />
              <label htmlFor="openEnded" className="text-xs text-gray-600">
                Sans date de fin
              </label>
            </div>

            {formState.toDate && dayCount > 1 && (
              <p className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
                Période de {dayCount} jours ({format(formState.fromDate, 'dd MMM', { locale: fr })}{' '}
                - {format(formState.toDate, 'dd MMM yyyy', { locale: fr })})
              </p>
            )}
          </div>

          {/* User selection */}
          <div className="mb-6">
            <label className="text-xs text-gray-600 font-medium block mb-2">
              Utilisateur assigné
            </label>

            {isEditMode && existingAssignment?.user && (
              <p className="text-xs text-blue-600 mb-2">
                Actuellement: {getUserDisplayName(existingAssignment.user)}
              </p>
            )}

            <Select.Root
              value={formState.userId || 'none'}
              onValueChange={handleUserChange}
              disabled={isSubmitting}
            >
              <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-md flex items-center justify-between text-sm hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <Select.Value>
                  {selectedUser ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs font-bold">
                        {getUserAvatar(selectedUser)}
                      </div>
                      <span>{getUserDisplayName(selectedUser)}</span>
                    </div>
                  ) : (
                    <span className="text-gray-500">
                      {isEditMode ? "Retirer l'assignation" : 'Sélectionner un utilisateur'}
                    </span>
                  )}
                </Select.Value>
                <ChevronRight className="rotate-90 text-gray-400" size={16} />
              </Select.Trigger>

              <Select.Portal>
                <Select.Content className="bg-white border rounded-lg shadow-lg z-[60] overflow-hidden">
                  <Select.Viewport className="p-1">
                    {isEditMode && (
                      <Select.Item
                        value="none"
                        className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer flex items-center space-x-2 text-sm"
                      >
                        <X size={16} className="text-red-500" />
                        <Select.ItemText>Retirer l'assignation</Select.ItemText>
                      </Select.Item>
                    )}

                    {deliverers.map((user) => (
                      <Select.Item
                        key={user.id}
                        value={user.id}
                        className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer text-sm"
                      >
                        <Select.ItemText>
                          <div className="flex items-center space-x-2">
                            <div className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs font-bold">
                              {getUserAvatar(user)}
                            </div>
                            <span>{getUserDisplayName(user)}</span>
                          </div>
                        </Select.ItemText>
                      </Select.Item>
                    ))}
                  </Select.Viewport>
                </Select.Content>
              </Select.Portal>
            </Select.Root>
          </div>

          {/* Error display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start space-x-2">
              <AlertCircle size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Delete confirmation */}
          {showDeleteConfirm && (
            <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
              <p className="text-sm text-amber-800 mb-2">
                Êtes-vous sûr de vouloir supprimer cette assignation ?
              </p>
              <div className="flex space-x-2">
                <button
                  onClick={handleDelete}
                  disabled={isSubmitting}
                  className="px-3 py-1 text-xs font-medium text-white bg-red-600 rounded hover:bg-red-700 disabled:opacity-50"
                >
                  Confirmer la suppression
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isSubmitting}
                  className="px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
                >
                  Annuler
                </button>
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            {isEditMode && existingAssignment && formState.userId !== null && (
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={isSubmitting || showDeleteConfirm}
                className="px-4 py-2 text-sm font-medium text-red-600 bg-white border border-red-300 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              >
                Supprimer
              </button>
            )}

            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50"
            >
              Annuler
            </button>

            <button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitDisabled}
              className={`px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 flex items-center space-x-2 ${
                formState.userId === null && isEditMode
                  ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isSubmitting && <Loader2 size={16} className="animate-spin" />}
              <span>{getSubmitButtonText()}</span>
            </button>
          </div>

          <Dialog.Close className="absolute top-4 right-4 p-1 hover:bg-gray-100 rounded">
            <X size={20} />
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
