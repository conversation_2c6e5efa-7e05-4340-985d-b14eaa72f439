// planning/components/DraggableUsersList.tsx
import { GripVertical } from 'lucide-react';
import React from 'react';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { getUserAvatar, getUserColor, getUserDisplayName } from '../utils/user-utils';

interface DraggableUsersListProps {
  users: IUserEntity[];
  onDragStart: (e: React.DragEvent, userId: string) => void;
  label: string;
}

export const DraggableUsersList: React.FC<DraggableUsersListProps> = ({
  users,
  onDragStart,
  label,
}) => {
  return (
    <div className="p-4">
      <h3 className="text-sm font-semibold text-gray-700 mb-2">{label}</h3>
      <div className="flex flex-wrap gap-2">
        {users.map((user) => (
          <div
            key={user.id}
            draggable
            onDragStart={(e) => onDragStart(e, user.id)}
            className="flex items-center space-x-2 px-3 py-1 border rounded-lg cursor-move hover:shadow-md transition-shadow text-white"
            style={{ backgroundColor: getUserColor(user) || '#3B82F6' }}
          >
            <GripVertical size={14} className="opacity-50" />
            <div className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-gray-800 bg-white">
              {getUserAvatar(user)}
            </div>
            <span className="text-sm">{getUserDisplayName(user)}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
