'use client';

import { flexRender } from '@tanstack/react-table';
import {
  ChevronDownIcon,
  ChevronFirstIcon,
  ChevronLastIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  CircleAlertIcon,
  CircleXIcon,
  Columns3Icon,
  ListFilterIcon,
  TrashIcon,
} from 'lucide-react';
import { useId, useRef } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pagination, PaginationContent, PaginationItem } from '@/components/ui/pagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { ExportButton } from '../../ui/export-button';
import { useDataTableExportTrigger } from '../../../lib/hooks/use-data-table-export-trigger';
import { useDataTableExport } from '../../../lib/hooks/use-data-table-export';
import { ExcelExportService } from '../../../lib/services/excel-export.service';
import { toast } from 'sonner';
import type { PaginatedDataTableProps } from '../../../interfaces/datatable';
import { usePaginatedDataTable } from '../../../lib/hooks/use-paginated-datatable';

export function PaginatedDataTable<
  TData,
  TSearchParams extends Record<string, unknown> = Record<string, unknown>,
>({
  queryKey,
  queryFn,
  config,
  enabled = true,
  className,
}: PaginatedDataTableProps<TData, TSearchParams>) {
  const id = useId();
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    table,
    meta,
    isLoading,
    isError,
    error,
    selectedRows,
    globalFilter,
    searchParams,
    handlers,
  } = usePaginatedDataTable({
    queryKey,
    queryFn,
    config,
    enabled,
  });

  const handleDeleteAction = () => {
    const deleteAction = config.actions?.find((action) => action.variant === 'destructive');
    if (deleteAction) {
      deleteAction.onClick(selectedRows);
    }
  };

  // Export functionality using the hook
  const { exportData } = useDataTableExport({
    queryFn,
    currentParams: {
      ...searchParams,
      ...(globalFilter && config.search?.searchKey
        ? { [config.search.searchKey]: globalFilter }
        : {}),
      page: table.getState().pagination.pageIndex + 1,
      limit: table.getState().pagination.pageSize,
    },
    config: { columns: config.columns },
    maxPagesLimit: config.export?.maxPagesLimit,
  });

  const handleExportTrigger = async () => {
    if (!config.export?.enabled) return;

    try {
      const data = await exportData();

      ExcelExportService.exportToExcel({
        data,
        columns: config.columns,
        fileName: config.export.fileName,
        sheetName: config.export.sheetName,
        columnMap: config.export.columnMap,
        excludeColumns: config.export.excludeColumns,
        includeOnlyColumns: config.export.includeOnlyColumns,
        valueTransformers: config.export.valueTransformers,
      });

      toast.success('Export terminé avec succès');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Erreur lors de l'export";
      toast.error(errorMessage);
    }
  };

  useDataTableExportTrigger({
    onExportTrigger: handleExportTrigger,
    enabled: config.export?.enabled && enabled,
  });

  if (isError) {
    return (
      <div className="flex items-center justify-center h-32 text-destructive">
        <p>Error loading data: {error?.message}</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Filters */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex items-center gap-3">
          {/* Global search */}
          {config.search?.enabled && (
            <div className="relative">
              <Input
                id={`${id}-search`}
                ref={inputRef}
                className={cn('peer min-w-60 ps-9', Boolean(globalFilter) && 'pe-9')}
                value={globalFilter}
                onChange={(e) => handlers.onGlobalFilterChange(e.target.value)}
                placeholder={config.search.placeholder ?? 'Search...'}
                type="text"
                aria-label="Global search"
              />
              <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
                <ListFilterIcon size={16} aria-hidden="true" />
              </div>
              {Boolean(globalFilter) && (
                <button
                  className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                  aria-label="Clear search"
                  onClick={() => {
                    handlers.onGlobalFilterChange('');
                    if (inputRef.current) {
                      inputRef.current.focus();
                    }
                  }}
                >
                  <CircleXIcon size={16} aria-hidden="true" />
                </button>
              )}
            </div>
          )}

          {/* Column filters */}
          {config.filters?.map((filter) => (
            <div key={filter.columnKey as string} className="flex flex-col gap-1">
              {filter.type === 'select' && (
                <Select
                  value={
                    ((searchParams as Record<string, unknown>)?.[
                      filter.columnKey as string
                    ] as string) || 'all'
                  }
                  onValueChange={(value) => {
                    const newParams = {
                      ...searchParams,
                      [filter.columnKey]: value === 'all' ? undefined : value || undefined,
                    };
                    // Nettoyer les valeurs vides
                    Object.keys(newParams).forEach((key) => {
                      if (!newParams[key]) {
                        delete newParams[key];
                      }
                    });
                    handlers.onSearchParamsChange(newParams as TSearchParams);
                  }}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder={filter.placeholder || filter.label} />
                  </SelectTrigger>
                  <SelectContent>
                    {filter.options?.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          ))}

          {/* Column visibility toggle */}
          {config.enableColumnVisibility && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Columns3Icon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
                  View
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      onSelect={(event) => event.preventDefault()}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center gap-3">
          {/* Selection-based actions */}
          {selectedRows.length > 0 && config.actions && (
            <>
              {config.actions
                .filter((action) => action.requiresSelection)
                .map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant ?? 'outline'}
                    onClick={() => action.onClick(selectedRows)}
                    disabled={action.disabled?.(selectedRows)}
                  >
                    {action.icon}
                    {action.label}
                    <span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
                      {selectedRows.length}
                    </span>
                  </Button>
                ))}

              {/* Delete confirmation dialog */}
              {config.actions.some((action) => action.variant === 'destructive') && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline">
                      <TrashIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
                      Delete
                      <span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
                        {selectedRows.length}
                      </span>
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
                      <div
                        className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                        aria-hidden="true"
                      >
                        <CircleAlertIcon className="opacity-80" size={16} />
                      </div>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete{' '}
                          {selectedRows.length} selected{' '}
                          {selectedRows.length === 1 ? 'item' : 'items'}.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                    </div>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDeleteAction}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </>
          )}

          {/* Non-selection actions */}
          {config.actions
            ?.filter((action) => !action.requiresSelection)
            .map((action, index) => (
              <Button
                key={index}
                variant={action.variant ?? 'outline'}
                onClick={() => action.onClick(selectedRows)}
                disabled={action.disabled?.(selectedRows)}
              >
                {action.icon}
                {action.label}
              </Button>
            ))}

          {/* Export button */}
          {config.export?.enabled && (
            <ExportButton
              queryFn={queryFn}
              currentParams={{
                ...searchParams,
                ...(globalFilter && config.search?.searchKey
                  ? { [config.search.searchKey]: globalFilter }
                  : {}),
                page: table.getState().pagination.pageIndex + 1,
                limit: table.getState().pagination.pageSize,
              }}
              columns={config.columns}
              fileName={config.export.fileName}
              sheetName={config.export.sheetName}
              columnMap={config.export.columnMap}
              excludeColumns={config.export.excludeColumns}
              includeOnlyColumns={config.export.includeOnlyColumns}
              maxPagesLimit={config.export.maxPagesLimit}
              valueTransformers={config.export.valueTransformers}
            />
          )}
        </div>
      </div>

      {/* Table */}
      <div className="bg-background overflow-hidden rounded-md border">
        <Table className="table-fixed">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent">
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    style={{ width: header.getSize() !== 0 ? `${header.getSize()}px` : undefined }}
                    className="h-11"
                  >
                    {header.isPlaceholder ? null : header.column.getCanSort() ? (
                      <div
                        className={cn(
                          header.column.getCanSort() &&
                            'flex h-full cursor-pointer items-center justify-between gap-2 select-none',
                        )}
                        onClick={header.column.getToggleSortingHandler()}
                        onKeyDown={(e) => {
                          if (header.column.getCanSort() && (e.key === 'Enter' || e.key === ' ')) {
                            e.preventDefault();
                            header.column.getToggleSortingHandler()?.(e);
                          }
                        }}
                        tabIndex={header.column.getCanSort() ? 0 : undefined}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {{
                          asc: (
                            <ChevronUpIcon
                              className="shrink-0 opacity-60"
                              size={16}
                              aria-hidden="true"
                            />
                          ),
                          desc: (
                            <ChevronDownIcon
                              className="shrink-0 opacity-60"
                              size={16}
                              aria-hidden="true"
                            />
                          ),
                        }[header.column.getIsSorted() as string] ?? null}
                      </div>
                    ) : (
                      flexRender(header.column.columnDef.header, header.getContext())
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: config.defaultPageSize ?? 10 }).map((_, index) => (
                <TableRow key={index}>
                  {config.columns.map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <Skeleton className="h-4 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="last:py-0">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={config.columns.length} className="h-24 text-center">
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between gap-8">
        {/* Results per page */}
        <div className="flex items-center gap-3">
          <Label htmlFor={`${id}-page-size`} className="max-sm:sr-only">
            Rows per page
          </Label>
          <Select
            value={table.getState().pagination.pageSize.toString()}
            onValueChange={(value) => {
              table.setPageSize(Number(value));
            }}
          >
            <SelectTrigger id={`${id}-page-size`} className="w-fit whitespace-nowrap">
              <SelectValue placeholder="Select page size" />
            </SelectTrigger>
            <SelectContent>
              {(config.pageSizeOptions ?? [10, 25, 50, 100]).map((pageSize) => (
                <SelectItem key={pageSize} value={pageSize.toString()}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Page information */}
        <div className="text-muted-foreground flex grow justify-end text-sm whitespace-nowrap">
          <p className="text-muted-foreground text-sm whitespace-nowrap" aria-live="polite">
            {meta && (
              <>
                <span className="text-foreground">
                  {Math.min((meta.page - 1) * meta.limit + 1, meta.totalItems)}-
                  {Math.min(meta.page * meta.limit, meta.totalItems)}
                </span>{' '}
                of <span className="text-foreground">{meta.totalItems}</span>
              </>
            )}
          </p>
        </div>

        {/* Pagination controls */}
        <div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <Button
                  size="icon"
                  variant="outline"
                  className="disabled:pointer-events-none disabled:opacity-50"
                  onClick={() => table.firstPage()}
                  disabled={!table.getCanPreviousPage()}
                  aria-label="Go to first page"
                >
                  <ChevronFirstIcon size={16} aria-hidden="true" />
                </Button>
              </PaginationItem>
              <PaginationItem>
                <Button
                  size="icon"
                  variant="outline"
                  className="disabled:pointer-events-none disabled:opacity-50"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                  aria-label="Go to previous page"
                >
                  <ChevronLeftIcon size={16} aria-hidden="true" />
                </Button>
              </PaginationItem>
              <PaginationItem>
                <Button
                  size="icon"
                  variant="outline"
                  className="disabled:pointer-events-none disabled:opacity-50"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                  aria-label="Go to next page"
                >
                  <ChevronRightIcon size={16} aria-hidden="true" />
                </Button>
              </PaginationItem>
              <PaginationItem>
                <Button
                  size="icon"
                  variant="outline"
                  className="disabled:pointer-events-none disabled:opacity-50"
                  onClick={() => table.lastPage()}
                  disabled={!table.getCanNextPage()}
                  aria-label="Go to last page"
                >
                  <ChevronLastIcon size={16} aria-hidden="true" />
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>
    </div>
  );
}
