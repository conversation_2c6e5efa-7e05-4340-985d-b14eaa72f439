import { TrendingUp } from 'lucide-react';
import { <PERSON>, Pie<PERSON><PERSON> } from 'recharts';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';


const chartConfig = {
  status: {
    label: "Statut",
  },
  partial: {
    label: 'Partielles',
  },
  inProgress: {
    label: 'En cours',
  },
  completed: {
    label: 'Terminées',
  },
  cancelled: {
    label: 'Annulées',
  },
} satisfies ChartConfig;


interface ChartProps<T> {
  data: T[];
}

export function ChartPieDonut(props: ChartProps<{ name: string; value: number, fill: string }>) {
  const { data } = props;
  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Statut des livraisons</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[200px]">

          <PieChart>
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
            <Pie data={data} dataKey="value" nameKey="name" innerRadius={40} />
          </PieChart>

        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        {/*<div className="flex items-center gap-2 leading-none font-medium">*/}
        {/*  Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />*/}
        {/*</div>*/}
        {/*<div className="text-muted-foreground leading-none">*/}
        {/*  Showing total visitors for the last 6 months*/}
        {/*</div>*/}
      </CardFooter>
    </Card>
  );
}
