import React, { useState, useRef, useCallback, useMemo } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  User,
  Calendar,
  Plus,
  X,
  GripVertical,
  CalendarDays,
} from 'lucide-react';
import * as Dialog from '@radix-ui/react-dialog';
import * as Select from '@radix-ui/react-select';
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isWeekend,
  getDay,
  addMonths,
  subMonths,
  isBefore,
  startOfDay,
  isToday,
  isAfter,
  addDays,
} from 'date-fns';
import { fr } from 'date-fns/locale';

// Interfaces TypeScript
interface UserType {
  nom: string;
  avatar: string;
  id: number;
}

interface Assignment {
  userId: number | null; // null = suppression explicite
  tourneeId: number;
  date: Date;
  period: 'morning' | 'afternoon' | 'full';
  isExplicitRemoval?: boolean; // Pour marquer une suppression explicite
}

interface Tournee {
  id: number;
  nom: string;
  couleur: string;
  defaultUserId?: number; // Utilisateur par défaut pour l'année
}

// Jours fériés français 2025
const joursFeries2025 = [
  new Date(2025, 0, 1), // Nouvel An
  new Date(2025, 3, 21), // Lundi de Pâques
  new Date(2025, 4, 1), // Fête du Travail
  new Date(2025, 4, 8), // Victoire 1945
  new Date(2025, 4, 29), // Ascension
  new Date(2025, 5, 9), // Lundi de Pentecôte
  new Date(2025, 6, 14), // Fête Nationale
  new Date(2025, 7, 15), // Assomption
  new Date(2025, 10, 1), // Toussaint
  new Date(2025, 10, 11), // Armistice 1918
  new Date(2025, 11, 25), // Noël
];

// Composant principal
const TourPlanning = () => {
  // État pour le mois affiché
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedCell, setSelectedCell] = useState<{
    tourneeId: number;
    date: Date;
    period: 'morning' | 'afternoon';
  } | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [draggedUser, setDraggedUser] = useState<{
    userId: number;
    sourceCell?: { tourneeId: number; date: Date; period: 'morning' | 'afternoon' };
  } | null>(null);

  // États pour la sélection de période dans la modal
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [periodType, setPeriodType] = useState<'morning' | 'afternoon' | 'full'>('full');

  // Référence pour le conteneur scrollable
  const timelineRef = useRef<HTMLDivElement>(null);

  // Données utilisateurs
  const users: UserType[] = [
    { nom: 'Alice Martin', avatar: 'AM', id: 1 },
    { nom: 'Bob Laurent', avatar: 'BL', id: 2 },
    { nom: 'Claire Dubois', avatar: 'CD', id: 3 },
    { nom: 'David Rousseau', avatar: 'DR', id: 4 },
    { nom: 'Emma Klein', avatar: 'EK', id: 5 },
    { nom: 'Fred Simon', avatar: 'FS', id: 6 },
    { nom: 'Grace Hamon', avatar: 'GH', id: 7 },
    { nom: 'Hugo Petit', avatar: 'HP', id: 8 },
    { nom: 'Iris Vidal', avatar: 'IV', id: 9 },
    { nom: 'Jules Moreau', avatar: 'JM', id: 10 },
    { nom: 'Léa Bernard', avatar: 'LB', id: 11 },
    { nom: 'Marc Leroy', avatar: 'ML', id: 12 },
    { nom: 'Nina Fabre', avatar: 'NF', id: 13 },
    { nom: 'Oscar Roux', avatar: 'OR', id: 14 },
    { nom: 'Paul André', avatar: 'PA', id: 15 },
  ];

  // Données tournées avec utilisateur par défaut (certaines sans pour avoir des cellules vides)
  const tournees: Tournee[] = [
    { id: 1, nom: 'Tournée Nord - Paris', couleur: 'bg-blue-500', defaultUserId: 1 },
    { id: 2, nom: 'Tournée Sud - Marseille', couleur: 'bg-green-500', defaultUserId: 2 },
    { id: 3, nom: 'Tournée Est - Strasbourg', couleur: 'bg-purple-500' }, // Pas d'utilisateur par défaut
    { id: 4, nom: 'Tournée Ouest - Nantes', couleur: 'bg-orange-500', defaultUserId: 4 },
    { id: 5, nom: 'Tournée Centre - Lyon', couleur: 'bg-red-500' }, // Pas d'utilisateur par défaut
    { id: 6, nom: 'Tournée Express - IDF', couleur: 'bg-teal-500', defaultUserId: 6 },
    { id: 7, nom: 'Tournée Sud-Ouest - Toulouse', couleur: 'bg-pink-500', defaultUserId: 7 },
    { id: 8, nom: 'Tournée Spéciale - Urgences', couleur: 'bg-yellow-500' }, // Pas d'utilisateur par défaut
  ];

  // État pour les assignations (ce sera connecté à l'API plus tard)
  const [assignments, setAssignments] = useState<Assignment[]>([
    // Quelques exemples d'assignations personnalisées
    { userId: 3, tourneeId: 1, date: new Date(2025, 5, 15), period: 'morning' },
    { userId: 5, tourneeId: 2, date: new Date(2025, 5, 15), period: 'afternoon' },
    { userId: 8, tourneeId: 3, date: new Date(2025, 5, 20), period: 'full' },
    { userId: 12, tourneeId: 5, date: new Date(2025, 5, 22), period: 'morning' },
  ]);

  // Générer les jours du mois
  const daysInMonth = useMemo(() => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    return eachDayOfInterval({ start, end });
  }, [currentMonth]);

  // Vérifier si c'est un jour férié
  const isJourFerie = (date: Date): boolean => {
    return joursFeries2025.some(
      (jourFerie) =>
        jourFerie.getDate() === date.getDate() &&
        jourFerie.getMonth() === date.getMonth() &&
        jourFerie.getFullYear() === date.getFullYear(),
    );
  };

  // Vérifier si une date est dans le passé
  const isPastDate = (date: Date): boolean => {
    return isBefore(startOfDay(date), startOfDay(new Date()));
  };

  // Obtenir l'assignation pour une cellule
  const getAssignment = (
    tourneeId: number,
    date: Date,
    period: 'morning' | 'afternoon',
  ): Assignment | undefined => {
    // D'abord vérifier s'il y a une assignation explicite (y compris les suppressions)
    const explicitAssignment = assignments.find(
      (a) =>
        a.tourneeId === tourneeId &&
        a.date.getDate() === date.getDate() &&
        a.date.getMonth() === date.getMonth() &&
        a.date.getFullYear() === date.getFullYear() &&
        (a.period === period || a.period === 'full'),
    );

    // Si suppression explicite, ne rien retourner
    if (explicitAssignment?.isExplicitRemoval) {
      return undefined;
    }

    // Si assignation explicite, la retourner
    if (explicitAssignment) {
      return explicitAssignment;
    }

    // Sinon, utiliser l'utilisateur par défaut si pas weekend/férié
    if (!isWeekend(date) && !isJourFerie(date)) {
      const tournee = tournees.find((t) => t.id === tourneeId);
      if (tournee?.defaultUserId) {
        return {
          userId: tournee.defaultUserId,
          tourneeId,
          date,
          period: 'full',
        };
      }
    }

    return undefined;
  };

  // Navigation entre les mois
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(direction === 'prev' ? subMonths(currentMonth, 1) : addMonths(currentMonth, 1));
  };

  // Gérer le clic sur une cellule
  const handleCellClick = (tourneeId: number, date: Date, period: 'morning' | 'afternoon') => {
    if (isPastDate(date)) return;

    setSelectedCell({ tourneeId, date, period });
    setStartDate(date);
    setEndDate(date);
    setPeriodType(period);
    setModalOpen(true);
  };

  // Gérer la sélection d'un utilisateur
  const handleUserSelect = (userId: string) => {
    if (!selectedCell || !startDate || !endDate) return;

    const start = startDate;
    const end = endDate;
    const dates = eachDayOfInterval({ start, end });

    // Pour chaque jour de la période
    dates.forEach((date) => {
      // Si c'est une date passée, on skip
      if (isPastDate(date)) return;

      // Déterminer les périodes à affecter
      const periods: ('morning' | 'afternoon')[] =
        periodType === 'full' ? ['morning', 'afternoon'] : [periodType];

      periods.forEach((period) => {
        // Supprimer toute assignation existante pour cette cellule
        setAssignments((prev) =>
          prev.filter(
            (a) =>
              !(
                a.tourneeId === selectedCell.tourneeId &&
                a.date.getDate() === date.getDate() &&
                a.date.getMonth() === date.getMonth() &&
                a.date.getFullYear() === date.getFullYear() &&
                a.period === period
              ),
          ),
        );

        if (userId === '0') {
          // Créer une suppression explicite
          const removalAssignment: Assignment = {
            userId: null,
            tourneeId: selectedCell.tourneeId,
            date: new Date(date),
            period: period,
            isExplicitRemoval: true,
          };
          setAssignments((prev) => [...prev, removalAssignment]);
        } else {
          // Ajouter la nouvelle assignation
          const newAssignment: Assignment = {
            userId: parseInt(userId),
            tourneeId: selectedCell.tourneeId,
            date: new Date(date),
            period: period,
          };
          setAssignments((prev) => [...prev, newAssignment]);
        }
      });
    });

    setModalOpen(false);
  };

  // Drag & Drop handlers
  const handleDragStart = (
    e: React.DragEvent,
    userId: number,
    sourceCell?: { tourneeId: number; date: Date; period: 'morning' | 'afternoon' },
  ) => {
    setDraggedUser({ userId, sourceCell });
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (
    e: React.DragEvent,
    tourneeId: number,
    date: Date,
    period: 'morning' | 'afternoon',
  ) => {
    e.preventDefault();

    if (!draggedUser || isPastDate(date)) return;

    // Supprimer de la source si elle existe
    if (draggedUser.sourceCell) {
      // Supprimer l'assignation existante
      setAssignments((prev) =>
        prev.filter(
          (a) =>
            !(
              a.tourneeId === draggedUser.sourceCell!.tourneeId &&
              a.date.getDate() === draggedUser.sourceCell!.date.getDate() &&
              a.date.getMonth() === draggedUser.sourceCell!.date.getMonth() &&
              a.date.getFullYear() === draggedUser.sourceCell!.date.getFullYear() &&
              a.period === draggedUser.sourceCell!.period
            ),
        ),
      );

      // Si c'était un utilisateur par défaut, créer une suppression explicite
      const sourceTournee = tournees.find((t) => t.id === draggedUser.sourceCell!.tourneeId);
      const sourceAssignment = getAssignment(
        draggedUser.sourceCell!.tourneeId,
        draggedUser.sourceCell!.date,
        draggedUser.sourceCell!.period,
      );

      if (
        sourceTournee?.defaultUserId === draggedUser.userId &&
        sourceAssignment?.period === 'full'
      ) {
        const removalAssignment: Assignment = {
          userId: null,
          tourneeId: draggedUser.sourceCell!.tourneeId,
          date: draggedUser.sourceCell!.date,
          period: draggedUser.sourceCell!.period,
          isExplicitRemoval: true,
        };
        setAssignments((prev) => [...prev, removalAssignment]);
      }
    }

    // Supprimer toute assignation existante à la destination
    setAssignments((prev) =>
      prev.filter(
        (a) =>
          !(
            a.tourneeId === tourneeId &&
            a.date.getDate() === date.getDate() &&
            a.date.getMonth() === date.getMonth() &&
            a.date.getFullYear() === date.getFullYear() &&
            a.period === period
          ),
      ),
    );

    // Ajouter à la destination
    const newAssignment: Assignment = {
      userId: draggedUser.userId,
      tourneeId,
      date,
      period,
    };

    setAssignments((prev) => [...prev, newAssignment]);
    setDraggedUser(null);
  };

  // Rendu d'une cellule de période (matin ou après-midi)
  const renderPeriodCell = (tournee: Tournee, date: Date, period: 'morning' | 'afternoon') => {
    const assignment = getAssignment(tournee.id, date, period);
    const user = assignment ? users.find((u) => u.id === assignment.userId) : null;
    const isHoliday = isWeekend(date) || isJourFerie(date);
    const isPast = isPastDate(date);
    const isTodayDate = isToday(date);

    return (
      <div
        className={`
          w-full h-8 flex items-center justify-center relative cursor-pointer
          ${period === 'morning' ? 'border-b border-gray-200' : ''}
          ${isHoliday ? 'bg-gray-100' : ''}
          ${isPast ? 'cursor-not-allowed opacity-60' : 'hover:bg-gray-50'}
          ${isTodayDate && !isHoliday ? 'bg-blue-50' : ''}
        `}
        onClick={() => !isPast && handleCellClick(tournee.id, date, period)}
        onDragOver={!isPast ? handleDragOver : undefined}
        onDrop={!isPast ? (e) => handleDrop(e, tournee.id, date, period) : undefined}
      >
        {user && (
          <div
            draggable={!isPast}
            onDragStart={(e) =>
              handleDragStart(e, user.id, { tourneeId: tournee.id, date, period })
            }
            className={`
              flex items-center space-x-1 px-2 py-1 rounded text-white text-xs font-medium
              ${tournee.couleur} bg-opacity-90 cursor-move
            `}
          >
            <GripVertical size={12} className="opacity-50" />
            <div className="w-5 h-5 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-xs font-bold">
              {user.avatar}
            </div>
            <span className="truncate max-w-[60px]">{user.nom.split(' ')[0]}</span>
          </div>
        )}
      </div>
    );
  };

  // Fonction pour formater la date
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
    });
  };

  // Formater une date pour input date
  const formatDateForInput = (date: Date | null): string => {
    if (!date) return '';
    return format(date, 'yyyy-MM-dd');
  };

  return (
    <div className="w-full bg-white border rounded-lg shadow-lg">
      {/* En-tête avec contrôles */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center space-x-4">
          <Calendar className="text-gray-600" size={20} />
          <h2 className="text-lg font-semibold text-gray-800">
            Planning des Tournées - {format(currentMonth, 'MMMM yyyy', { locale: fr })}
          </h2>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigateMonth('prev')}
            className="p-2 rounded-lg border hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft size={16} />
          </button>
          <button
            onClick={() => navigateMonth('next')}
            className="p-2 rounded-lg border hover:bg-gray-100 transition-colors"
          >
            <ChevronRight size={16} />
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Colonne des noms de tournées (fixe) */}
        <div className="w-48 bg-gray-50 border-r">
          {/* En-tête vide pour alignement */}
          <div className="h-16 border-b bg-gray-100"></div>

          {/* Liste des tournées */}
          {tournees.map((tournee) => (
            <div key={tournee.id} className="h-16 flex items-center px-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${tournee.couleur}`}></div>
                <span className="font-medium text-gray-800 text-sm">{tournee.nom}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Zone de timeline */}
        <div ref={timelineRef} className="flex-1 overflow-x-auto" style={{ maxHeight: '600px' }}>
          <div style={{ width: `${daysInMonth.length * 120}px`, minWidth: '100%' }}>
            {/* En-tête des dates */}
            <div className="h-16 flex border-b bg-gray-100 sticky top-0 z-10">
              {daysInMonth.map((date, index) => {
                const isHoliday = isWeekend(date) || isJourFerie(date);
                const isTodayDate = isToday(date);

                return (
                  <div
                    key={index}
                    className={`w-30 flex-shrink-0 border-r border-gray-200 p-2 text-center ${
                      isTodayDate ? 'bg-blue-100 border-blue-300' : ''
                    }`}
                    style={{ width: '120px' }}
                  >
                    <div
                      className={`text-xs font-medium ${isTodayDate ? 'text-blue-600' : 'text-gray-600'}`}
                    >
                      {formatDate(date)}
                    </div>
                    {isTodayDate && (
                      <div className="text-xs text-blue-600 font-bold mt-1">Aujourd'hui</div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Grille des assignations */}
            {tournees.map((tournee) => (
              <div key={tournee.id} className="h-16 flex border-b border-gray-200">
                {daysInMonth.map((date, dateIndex) => {
                  const isHoliday = isWeekend(date) || isJourFerie(date);
                  const isTodayDate = isToday(date);

                  return (
                    <div
                      key={dateIndex}
                      className={`w-30 flex-shrink-0 border-r ${
                        isTodayDate ? 'border-blue-300' : 'border-gray-100'
                      }`}
                      style={{ width: '120px' }}
                    >
                      <div className="h-full flex flex-col">
                        {renderPeriodCell(tournee, date, 'morning')}
                        {renderPeriodCell(tournee, date, 'afternoon')}
                      </div>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Modal de sélection d'utilisateur */}
      <Dialog.Root open={modalOpen} onOpenChange={setModalOpen}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
          <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl p-6 z-50 w-96">
            <Dialog.Title className="text-lg font-semibold mb-4">
              Assigner un chauffeur
            </Dialog.Title>

            {selectedCell && (
              <div className="mb-4 space-y-4">
                <div className="text-sm text-gray-600">
                  <p>
                    Tournée:{' '}
                    <span className="font-medium">
                      {tournees.find((t) => t.id === selectedCell.tourneeId)?.nom}
                    </span>
                  </p>
                </div>

                {/* Sélection de la période */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <CalendarDays size={16} className="text-gray-500" />
                    <h3 className="text-sm font-medium">Période d'assignation</h3>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="text-xs text-gray-500 block mb-1">Date de début</label>
                      <input
                        type="date"
                        value={formatDateForInput(startDate)}
                        min={formatDateForInput(new Date())}
                        onChange={(e) => setStartDate(new Date(e.target.value))}
                        className="w-full px-3 py-2 border rounded text-sm"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-gray-500 block mb-1">Date de fin</label>
                      <input
                        type="date"
                        value={formatDateForInput(endDate)}
                        min={formatDateForInput(startDate || new Date())}
                        onChange={(e) => setEndDate(new Date(e.target.value))}
                        className="w-full px-3 py-2 border rounded text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-xs text-gray-500 block mb-1">
                      Période de la journée
                    </label>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setPeriodType('full')}
                        className={`flex-1 px-3 py-2 text-sm rounded border ${
                          periodType === 'full'
                            ? 'bg-blue-500 text-white border-blue-500'
                            : 'bg-white text-gray-700 border-gray-300'
                        }`}
                      >
                        Journée complète
                      </button>
                      <button
                        onClick={() => setPeriodType('morning')}
                        className={`flex-1 px-3 py-2 text-sm rounded border ${
                          periodType === 'morning'
                            ? 'bg-blue-500 text-white border-blue-500'
                            : 'bg-white text-gray-700 border-gray-300'
                        }`}
                      >
                        Matin
                      </button>
                      <button
                        onClick={() => setPeriodType('afternoon')}
                        className={`flex-1 px-3 py-2 text-sm rounded border ${
                          periodType === 'afternoon'
                            ? 'bg-blue-500 text-white border-blue-500'
                            : 'bg-white text-gray-700 border-gray-300'
                        }`}
                      >
                        Après-midi
                      </button>
                    </div>
                  </div>

                  {startDate && endDate && (
                    <p className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                      Assigner pour{' '}
                      {Math.floor(
                        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
                      ) + 1}{' '}
                      jour(s)
                    </p>
                  )}
                </div>

                <div className="border-t pt-4">
                  <label className="text-xs text-gray-500 block mb-2">
                    Sélectionner un chauffeur
                  </label>
                  <Select.Root onValueChange={handleUserSelect}>
                    <Select.Trigger className="w-full px-3 py-2 border rounded-lg flex items-center justify-between">
                      <Select.Value placeholder="Choisir un chauffeur" />
                      <ChevronRight className="rotate-90" size={16} />
                    </Select.Trigger>

                    <Select.Portal>
                      <Select.Content className="bg-white border rounded-lg shadow-lg z-50">
                        <Select.Viewport className="p-2">
                          <Select.Item
                            value="0"
                            className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                          >
                            <Select.ItemText>
                              <div className="flex items-center space-x-2">
                                <X size={16} className="text-red-500" />
                                <span>Supprimer l'assignation</span>
                              </div>
                            </Select.ItemText>
                          </Select.Item>

                          {users.map((user) => (
                            <Select.Item
                              key={user.id}
                              value={user.id.toString()}
                              className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                            >
                              <Select.ItemText>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs font-bold">
                                    {user.avatar}
                                  </div>
                                  <span>{user.nom}</span>
                                </div>
                              </Select.ItemText>
                            </Select.Item>
                          ))}
                        </Select.Viewport>
                      </Select.Content>
                    </Select.Portal>
                  </Select.Root>
                </div>
              </div>
            )}

            <Dialog.Close className="absolute top-4 right-4 p-1 hover:bg-gray-100 rounded">
              <X size={20} />
            </Dialog.Close>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>

      {/* Légende */}
      <div className="p-4 border-t bg-gray-50">
        <div className="flex items-center space-x-6 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <User size={16} />
            <span>Utilisateur assigné</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
            <span>Aujourd'hui</span>
          </div>
          <span className="text-xs">
            Défilez horizontalement ou utilisez les flèches pour naviguer
          </span>
        </div>
      </div>

      {/* Liste des chauffeurs (pour drag & drop) */}
      <div className="p-4 border-t bg-gray-50">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">
          Chauffeurs disponibles (glisser-déposer)
        </h3>
        <div className="flex flex-wrap gap-2">
          {users.map((user) => (
            <div
              key={user.id}
              draggable
              onDragStart={(e) => handleDragStart(e, user.id)}
              className="flex items-center space-x-2 px-3 py-1 bg-white border rounded-lg cursor-move hover:shadow-md transition-shadow"
            >
              <GripVertical size={14} className="text-gray-400" />
              <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs font-bold">
                {user.avatar}
              </div>
              <span className="text-sm">{user.nom}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TourPlanning;
