'use client';

import { useAppSelector } from '@/hooks/redux';
import { selectIsLoading, selectLoadingMessage } from '@/lib/store/loading-slice';

import { FullPageLoader } from './full-page-loader';

export function LoadingProvider() {
  const isLoading = useAppSelector(selectIsLoading);
  const message = useAppSelector(selectLoadingMessage);

  if (!isLoading) return null;

  return <FullPageLoader text={message} />;
}
