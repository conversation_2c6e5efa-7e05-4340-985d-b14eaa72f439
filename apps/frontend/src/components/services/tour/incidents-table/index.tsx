'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { ITourIncidentDto } from '@/interfaces/entity/i-tour-incident-dto';
import { DeliveryCompletionType } from '@/interfaces/enum/stop-completion.enum';
import { useTourIncidentsQuery } from '@/lib/hooks/queries/tour-detail-queries';
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  Clock,
  InfoIcon,
  UserIcon,
  XCircleIcon,
} from 'lucide-react';

interface IncidentsTableProps {
  tourId: string;
  className?: string;
}

export function IncidentsTable({ tourId, className }: IncidentsTableProps) {
  const { data: incidents, isLoading, isError } = useTourIncidentsQuery(tourId);

  const formatDateTime = (dateString: string | Date | null) => {
    if (!dateString) return 'Non défini';
    const date = new Date(dateString);
    return (
      date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) +
      ' à ' +
      date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
      })
    );
  };

  const getCompletionTypeLabel = (type: DeliveryCompletionType | null) => {
    if (!type) return 'Non défini';
    switch (type) {
      case DeliveryCompletionType.FULL:
        return 'Livraison complète';
      case DeliveryCompletionType.PARTIAL:
        return 'Livraison partielle';
      case DeliveryCompletionType.NONE:
        return 'Aucune livraison';
      default:
        return type;
    }
  };

  const getCompletionTypeColor = (type: DeliveryCompletionType | null) => {
    if (!type) return 'outline';
    switch (type) {
      case DeliveryCompletionType.FULL:
        return 'default'; // Vert
      case DeliveryCompletionType.PARTIAL:
        return 'secondary'; // Orange
      case DeliveryCompletionType.NONE:
        return 'destructive'; // Rouge
      default:
        return 'outline';
    }
  };

  const getCompletionTypeIcon = (type: DeliveryCompletionType | null) => {
    if (!type) return <InfoIcon className="h-4 w-4" />;
    switch (type) {
      case DeliveryCompletionType.FULL:
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case DeliveryCompletionType.PARTIAL:
        return <AlertTriangleIcon className="h-4 w-4 text-orange-600" />;
      case DeliveryCompletionType.NONE:
        return <XCircleIcon className="h-4 w-4 text-red-600" />;
      default:
        return <InfoIcon className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangleIcon className="h-5 w-5 text-orange-600" />
            Incidents de la tournée
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Chargement des incidents...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangleIcon className="h-5 w-5 text-orange-600" />
            Incidents de la tournée
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-destructive">Erreur lors du chargement des incidents</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!incidents || incidents.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangleIcon className="h-5 w-5 text-orange-600" />
            Incidents de la tournée
            <Badge variant="outline" className="ml-2">
              0
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">
              <CheckCircleIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
              Aucun incident signalé pour cette tournée
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangleIcon className="h-5 w-5 text-orange-600" />
          Incidents de la tournée
          <Badge variant="destructive" className="ml-2">
            {incidents.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-20">N° Stop</TableHead>
              <TableHead className="w-48">Client</TableHead>
              <TableHead className="w-40">Type d'incident</TableHead>
              <TableHead className="w-40">Impact</TableHead>
              <TableHead className="w-36">Heure</TableHead>
              <TableHead>Commentaires</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {incidents.map((incident, index) => (
              <TableRow key={`${incident.stopId}-${index}`}>
                <TableCell>
                  <div className="flex items-center justify-center">
                    <div className="bg-primary/10 text-primary font-medium rounded-full w-8 h-8 flex items-center justify-center text-sm">
                      {incident.stopSequence}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className="bg-blue-50 p-1.5 rounded">
                      <UserIcon className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium">{incident.clientName}</div>
                      {incident.clientCode && (
                        <div className="text-sm text-muted-foreground">{incident.clientCode}</div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center gap-2">
                          <AlertTriangleIcon className="h-4 w-4 text-orange-600" />
                          <span className="font-medium">{incident.incidentType.name}</span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{incident.incidentType.description || 'Aucune description'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getCompletionTypeIcon(incident.deliveryCompletionType)}
                    <Badge
                      variant={getCompletionTypeColor(incident.deliveryCompletionType)}
                      className="text-xs"
                    >
                      {getCompletionTypeLabel(incident.deliveryCompletionType)}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{formatDateTime(incident.completedAt)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-xs">
                    {incident.comments ? (
                      <p
                        className="text-sm text-muted-foreground truncate"
                        title={incident.comments}
                      >
                        {incident.comments}
                      </p>
                    ) : (
                      <span className="text-sm text-muted-foreground italic">
                        Aucun commentaire
                      </span>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
