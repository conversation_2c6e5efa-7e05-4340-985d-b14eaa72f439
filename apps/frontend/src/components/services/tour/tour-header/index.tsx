import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import type { ITourEntity } from '@/interfaces/entity/i-tour-entity';
import { TourStatus } from '@/interfaces/enum/tour.enums';
import { CalendarIcon, FileSpreadsheetIcon, PackageIcon, TruckIcon, UserIcon } from 'lucide-react';

interface TourHeaderProps {
  tour: ITourEntity | undefined;
}

export function TourHeader({ tour }: TourHeaderProps) {
  if (!tour) {
    return (
      <div className="bg-white border rounded-lg p-6 mb-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: TourStatus) => {
    switch (status) {
      case TourStatus.Completed:
        return 'default';
      case TourStatus.InProgress:
        return 'secondary';
      case TourStatus.Planned:
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getStatusLabel = (status: TourStatus) => {
    switch (status) {
      case TourStatus.Completed:
        return 'Terminée';
      case TourStatus.InProgress:
        return 'En cours';
      case TourStatus.Planned:
        return 'Planifiée';
      default:
        return status;
    }
  };

  const getTourProgress = (): number => {
    if (!tour.stops || tour.stops.length === 0) return 0;
    const completedStops = tour.stops.filter((stop) => stop.completion?.completedAt).length;
    return Math.round((completedStops / tour.stops.length) * 100);
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const progress = getTourProgress();
  const completedStops = tour.stops?.filter((stop) => stop.completion?.completedAt).length || 0;
  const totalStops = tour.stops?.length || 0;

  return (
    <div className="bg-white border rounded-lg p-6 mb-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* En-tête principal avec numéro et date */}
          <div className="flex items-center gap-4 mb-4">
            <div className="bg-primary/10 p-3 rounded-lg">
              <PackageIcon className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold">
                Tour #{tour.tourIdentifier?.originalNumber || tour.id.slice(0, 8)}
              </h1>
              <div className="flex items-center gap-2 text-muted-foreground mt-1">
                <CalendarIcon className="h-4 w-4" />
                <span>{formatDate(tour.deliveryDate)}</span>
              </div>
            </div>
          </div>

          {/* Informations détaillées */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
            {/* TODO: Réactiver quand le système d'assignation sera prêt */}
            {/* Chauffeur */}
            {/* <div className="flex items-center gap-3">
              <div className="bg-blue-50 p-2 rounded-lg">
                <UserIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Chauffeur</div>
                <div className="font-medium">Non assigné</div>
              </div>
            </div> */}

            {/* Véhicule */}
            {/* <div className="flex items-center gap-3">
              <div className="bg-green-50 p-2 rounded-lg">
                <TruckIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Véhicule</div>
                <div className="font-medium">Non assigné</div>
              </div>
            </div> */}

            {/* Statut et progression */}
            <div className="flex items-center gap-3">
              <div className="bg-orange-50 p-2 rounded-lg">
                <CalendarIcon className="h-5 w-5 text-orange-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant={getStatusColor(tour.status)} className="text-xs">
                    {getStatusLabel(tour.status)}
                  </Badge>
                  <span className="text-sm font-medium">
                    {completedStops}/{totalStops} arrêts
                  </span>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">Progression</span>
                    <span className="text-xs font-medium">{progress}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 ml-6">
          <Button variant="outline" disabled>
            <FileSpreadsheetIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
            Export PDF
          </Button>
        </div>
      </div>
    </div>
  );
}
