'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { IStopEntity } from '@/interfaces/entity/i-stop-entity';
import { DeliveryStatus } from '@/interfaces/enum/stop-completion.enum';
import { useTourStopsQuery } from '@/lib/hooks/queries/tour-detail-queries';
import { CheckCircleIcon, Clock, MapPinIcon, TruckIcon, XCircleIcon } from 'lucide-react';

interface TourTimelineProps {
  tourId: string;
  className?: string;
}

interface TimelineStop {
  stop: IStopEntity;
  plannedTime: Date | null;
  actualTime: Date | null;
  status: 'completed' | 'current' | 'planned' | 'failed';
  position: number; // Pourcentage 0-100 sur l'axe temporel
}

export function TourTimeline({ tourId, className }: TourTimelineProps) {
  const { data: stopsData, isLoading, isError } = useTourStopsQuery(tourId);

  // Parse delivery time window string (ex: "08:30-09:00" ou "14:00")
  const parseTimeWindow = (timeWindow: string | null): Date | null => {
    if (!timeWindow) return null;

    // Prendre la première heure (début de la fenêtre)
    const timeMatch = timeWindow.match(/(\d{1,2}):(\d{2})/);
    if (!timeMatch) return null;

    const today = new Date();
    const hours = parseInt(timeMatch[1], 10);
    const minutes = parseInt(timeMatch[2], 10);

    const date = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes);
    return date;
  };

  // Convertir heure en position sur timeline (8h = 0%, 20h = 100%)
  const timeToPosition = (time: Date): number => {
    const startHour = 8; // 8h00
    const endHour = 20; // 20h00
    const totalMinutes = (endHour - startHour) * 60; // 12h * 60 = 720 minutes

    const hour = time.getHours();
    const minute = time.getMinutes();
    const timeInMinutes = (hour - startHour) * 60 + minute;

    // Assurer que c'est dans les bornes
    const clampedMinutes = Math.max(0, Math.min(timeInMinutes, totalMinutes));
    return (clampedMinutes / totalMinutes) * 100;
  };

  // Préparer les données de timeline
  const prepareTimelineData = (stops: IStopEntity[]): TimelineStop[] => {
    if (!stops || stops.length === 0) return [];

    return stops
      .sort((a, b) => a.sequenceInTour - b.sequenceInTour)
      .map((stop) => {
        const plannedTime = parseTimeWindow(stop.deliveryTimeWindow);
        const actualTime = stop.completion?.completedAt
          ? new Date(stop.completion.completedAt)
          : null;

        // Déterminer le statut
        let status: TimelineStop['status'] = 'planned';
        if (stop.completion?.deliveryStatus === DeliveryStatus.COMPLETED) {
          status = 'completed';
        } else if (stop.completion?.deliveryStatus === DeliveryStatus.FAILED) {
          status = 'failed';
        } else if (stop.completion?.deliveryStatus === DeliveryStatus.PENDING) {
          status = 'current';
        }

        // Position basée sur l'heure planifiée ou réelle
        const timeForPosition = actualTime || plannedTime;
        const position = timeForPosition ? timeToPosition(timeForPosition) : 0;

        return {
          stop,
          plannedTime,
          actualTime,
          status,
          position,
        };
      });
  };

  // Calculer l'estimation de fin
  const calculateEstimatedEnd = (timelineStops: TimelineStop[]): string => {
    const completedStops = timelineStops.filter((ts) => ts.status === 'completed');
    const totalStops = timelineStops.length;

    if (completedStops.length === 0 || totalStops === 0) {
      return '18:00 (estimation)';
    }

    // Si tous les stops sont complétés
    if (completedStops.length === totalStops) {
      const lastStop = completedStops[completedStops.length - 1];
      const endTime = lastStop.actualTime;
      return endTime
        ? endTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }) + ' (terminé)'
        : '18:00 (estimation)';
    }

    // Estimation basée sur la vitesse moyenne
    const firstCompleted = completedStops[0];
    const lastCompleted = completedStops[completedStops.length - 1];

    if (firstCompleted.actualTime && lastCompleted.actualTime) {
      const timeSpent = lastCompleted.actualTime.getTime() - firstCompleted.actualTime.getTime();
      const stopsCompleted = completedStops.length;
      const remainingStops = totalStops - stopsCompleted;

      const avgTimePerStop = timeSpent / stopsCompleted;
      const estimatedRemainingTime = avgTimePerStop * remainingStops;

      const estimatedEnd = new Date(lastCompleted.actualTime.getTime() + estimatedRemainingTime);
      return (
        estimatedEnd.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }) +
        ' (estimation)'
      );
    }

    return '18:00 (estimation)';
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Timeline de progression
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Chargement de la timeline...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Timeline de progression
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-destructive">Erreur lors du chargement de la timeline</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const stops = stopsData?.items || [];
  const timelineStops = prepareTimelineData(stops);
  const estimatedEnd = calculateEstimatedEnd(timelineStops);

  if (timelineStops.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Timeline de progression
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Aucun arrêt planifié pour cette tournée</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Générer les marqueurs d'heures (8h, 10h, 12h, 14h, 16h, 18h, 20h)
  const hourMarkers = [];
  for (let hour = 8; hour <= 20; hour += 2) {
    const position = ((hour - 8) / 12) * 100;
    hourMarkers.push({
      hour,
      position,
      label: `${hour}:00`,
    });
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-blue-600" />
          Timeline de progression
          <span className="text-sm font-normal text-muted-foreground ml-auto">
            Fin estimée : {estimatedEnd}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          {/* Axe principal horizontal */}
          <div className="relative h-20 mb-8">
            {/* Ligne de base */}
            <div className="absolute top-10 left-0 right-0 h-0.5 bg-gray-300"></div>

            {/* Marqueurs d'heures */}
            {hourMarkers.map((marker) => (
              <div
                key={marker.hour}
                className="absolute transform -translate-x-1/2"
                style={{ left: `${marker.position}%`, top: '32px' }}
              >
                <div className="w-0.5 h-4 bg-gray-400"></div>
                <div className="text-xs text-muted-foreground mt-1 text-center">{marker.label}</div>
              </div>
            ))}

            {/* Points des stops */}
            <TooltipProvider>
              {timelineStops.map((timelineStop, index) => {
                const { stop, status, position, plannedTime, actualTime } = timelineStop;

                // Icône selon le statut
                const getStatusIcon = () => {
                  switch (status) {
                    case 'completed':
                      return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
                    case 'failed':
                      return <XCircleIcon className="h-4 w-4 text-red-600" />;
                    case 'current':
                      return <TruckIcon className="h-4 w-4 text-blue-600 animate-pulse" />;
                    default:
                      return <Clock className="h-4 w-4 text-gray-400" />;
                  }
                };

                // Couleur du point
                const getStatusColor = () => {
                  switch (status) {
                    case 'completed':
                      return 'bg-green-600 border-green-600';
                    case 'failed':
                      return 'bg-red-600 border-red-600';
                    case 'current':
                      return 'bg-blue-600 border-blue-600 animate-pulse';
                    default:
                      return 'bg-gray-400 border-gray-400';
                  }
                };

                return (
                  <Tooltip key={stop.id}>
                    <TooltipTrigger asChild>
                      <div
                        className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                        style={{ left: `${Math.max(2, Math.min(98, position))}%`, top: '40px' }}
                      >
                        <div
                          className={`w-6 h-6 rounded-full border-2 flex items-center justify-center bg-white ${getStatusColor()}`}
                        >
                          {getStatusIcon()}
                        </div>
                        {/* Numéro de séquence */}
                        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                          <div className="bg-primary/10 text-primary text-xs font-medium rounded px-1">
                            {stop.sequenceInTour}
                          </div>
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-sm">
                        <div className="font-medium">
                          Stop #{stop.sequenceInTour} - {stop.client?.name || 'Client inconnu'}
                        </div>
                        <div className="text-muted-foreground">
                          {plannedTime && (
                            <div>
                              Prévu :{' '}
                              {plannedTime.toLocaleTimeString('fr-FR', {
                                hour: '2-digit',
                                minute: '2-digit',
                              })}
                            </div>
                          )}
                          {actualTime && (
                            <div>
                              Réalisé :{' '}
                              {actualTime.toLocaleTimeString('fr-FR', {
                                hour: '2-digit',
                                minute: '2-digit',
                              })}
                            </div>
                          )}
                          <div>
                            Statut :{' '}
                            {status === 'completed'
                              ? 'Terminé'
                              : status === 'current'
                                ? 'En cours'
                                : status === 'failed'
                                  ? 'Annulé'
                                  : 'Planifié'}
                          </div>
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                );
              })}
            </TooltipProvider>
          </div>

          {/* Légende */}
          <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
              <span>Terminé</span>
            </div>
            <div className="flex items-center gap-2">
              <TruckIcon className="h-4 w-4 text-blue-600" />
              <span>En cours</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-400" />
              <span>Planifié</span>
            </div>
            <div className="flex items-center gap-2">
              <XCircleIcon className="h-4 w-4 text-red-600" />
              <span>Annulé</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
