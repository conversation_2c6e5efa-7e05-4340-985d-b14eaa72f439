import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../ui/dialog';
import type { IUserEntity } from '../../../../interfaces/entity/i-user-entity';
import { ResetPasswordForm } from '../reset-password-form';

interface ResetPasswordDialogProps {
  user?: IUserEntity | null;
  isCurrentUser?: boolean;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ResetPasswordDialog({
  user,
  isCurrentUser,
  open,
  onOpenChange,
}: ResetPasswordDialogProps) {
  const handleSuccess = () => {
    onOpenChange(false);
  };

  const title = isCurrentUser
    ? 'Changer mon mot de passe'
    : `Réinitialiser le mot de passe de ${user?.firstName} ${user?.lastName}`;

  const description = isCurrentUser
    ? 'Saisissez votre nouveau mot de passe'
    : `Définissez un nouveau mot de passe pour ${user?.username}`;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <ResetPasswordForm
          userId={user?.id}
          isCurrentUser={isCurrentUser}
          onSuccess={handleSuccess}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
