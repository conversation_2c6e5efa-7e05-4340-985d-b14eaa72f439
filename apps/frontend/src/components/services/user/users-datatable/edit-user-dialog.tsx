import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { toast } from 'sonner';
import { EditUserForm } from '../user-form/edit-user-form';

interface EditUserDialogProps {
  user: IUserEntity | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditUserDialog({ user, open, onOpenChange }: EditUserDialogProps) {
  const handleSuccess = () => {
    toast.success('Utilisateur mis à jour avec succès');
    onOpenChange(false);
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Modifier l'utilisateur</DialogTitle>
          <DialogDescription>
            Modifiez les informations de {user.firstName} {user.lastName} ({user.username}).
          </DialogDescription>
        </DialogHeader>

        <EditUserForm user={user} onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  );
}
