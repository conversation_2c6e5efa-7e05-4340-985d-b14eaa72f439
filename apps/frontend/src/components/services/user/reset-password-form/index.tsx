'use client';

import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Button } from '../../../ui/button';
import { Input } from '../../../ui/input';
import { Checkbox } from '../../../ui/checkbox';
import { FormField } from '../../../ui/form-field';
import { userApiService } from '../../../../lib/api-service/user-api-service';
import type { ResetPasswordRequestDto } from '../../../../lib/dto/reset-password.dto';

interface ResetPasswordFormProps {
  userId?: string;
  isCurrentUser?: boolean;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ResetPasswordForm({
  userId,
  isCurrentUser,
  onSuccess,
  onCancel,
}: ResetPasswordFormProps) {
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
    temporary: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const resetPasswordMutation = useMutation({
    mutationFn: (data: ResetPasswordRequestDto) =>
      isCurrentUser
        ? userApiService.resetMyPassword(data.password, data.temporary)
        : userApiService.resetUserPassword(userId!, data.password, data.temporary),
    onSuccess: () => {
      toast.success('Mot de passe réinitialisé avec succès');
      setFormData({ password: '', confirmPassword: '', temporary: false });
      onSuccess?.();
    },
    onError: (error: Error & { response?: { status?: number; data?: { message?: string } } }) => {
      const message =
        error?.response?.status === 403
          ? "Vous n'avez pas les droits pour effectuer cette action"
          : error?.response?.status === 404
            ? 'Utilisateur non trouvé'
            : 'Erreur lors de la réinitialisation du mot de passe';
      toast.error(message);
    },
  });

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    }

    if (formData.confirmPassword && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      resetPasswordMutation.mutate({
        password: formData.password,
        temporary: formData.temporary,
      });
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <FormField label="Nouveau mot de passe" error={errors.password}>
        <Input
          type="password"
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          placeholder="Saisissez le nouveau mot de passe"
          disabled={resetPasswordMutation.isPending}
        />
      </FormField>

      <FormField label="Confirmer le mot de passe" error={errors.confirmPassword}>
        <Input
          type="password"
          value={formData.confirmPassword}
          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
          placeholder="Confirmez le nouveau mot de passe"
          disabled={resetPasswordMutation.isPending}
        />
      </FormField>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="temporary"
          checked={formData.temporary}
          onCheckedChange={(checked) => handleInputChange('temporary', checked)}
          disabled={resetPasswordMutation.isPending}
        />
        <label htmlFor="temporary" className="text-sm">
          Mot de passe temporaire
          <span className="text-muted-foreground block text-xs">
            L'utilisateur devra changer son mot de passe à la prochaine connexion
          </span>
        </label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={resetPasswordMutation.isPending}
        >
          Annuler
        </Button>
        <Button type="submit" disabled={resetPasswordMutation.isPending}>
          {resetPasswordMutation.isPending ? 'Réinitialisation...' : 'Réinitialiser'}
        </Button>
      </div>
    </form>
  );
}
