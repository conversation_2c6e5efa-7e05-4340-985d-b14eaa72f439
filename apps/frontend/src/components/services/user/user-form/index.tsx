'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { ColorPicker } from '@/components/ui/color-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { UserRole } from '@/interfaces/enum/user-role.enum';
import { userApiService } from '@/lib/api-service/user-api-service';
import type { CreateUserRequestDto } from '@/lib/dto/user-creation.dto';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { InfoIcon } from 'lucide-react';
import { useState } from 'react';

interface UserFormProps {
  onSuccess?: () => void;
  isSSO?: boolean;
}

interface FormData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  locale: string;
  role: UserRole | '';
  color: string;
  createInKeycloak: boolean;
  password: string;
}

export function UserForm({ onSuccess, isSSO = false }: UserFormProps) {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    locale: 'fr',
    role: '',
    color: '#3B82F6', // Couleur par défaut
    createInKeycloak: true,
    password: '',
  });
  const [error, setError] = useState('');

  const createUserMutation = useMutation({
    mutationFn: (userData: CreateUserRequestDto) => userApiService.createUser(userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      onSuccess?.();
    },
    onError: (error: Error & { response?: { data?: { message?: string } } }) => {
      console.error('Erreur création utilisateur:', error);
      const message =
        error.response?.data?.message ||
        error.message ||
        "Erreur lors de la création de l'utilisateur";
      setError(message);
    },
  });

  const cleanFormData = (data: FormData): CreateUserRequestDto => {
    const cleanedData: CreateUserRequestDto = {
      username: data.username.trim(),
      email: data.email.trim(),
      role: data.role as UserRole,
      createInKeycloak: data.createInKeycloak,
    };

    // Ajouter les champs optionnels seulement s'ils ont une valeur
    if (data.firstName.trim()) {
      cleanedData.firstName = data.firstName.trim();
    }
    if (data.lastName.trim()) {
      cleanedData.lastName = data.lastName.trim();
    }
    if (data.locale.trim()) {
      cleanedData.locale = data.locale.trim();
    }
    if (data.color.trim()) {
      cleanedData.color = data.color.trim();
    }
    if (data.createInKeycloak && data.password.trim()) {
      cleanedData.password = data.password.trim();
    }

    return cleanedData;
  };

  const validateForm = (data: FormData): string | null => {
    if (!data.username.trim()) {
      return "Le nom d'utilisateur est requis";
    }
    if (!data.email.trim()) {
      return "L'email est requis";
    }
    if (!data.role) {
      return 'Le rôle est requis';
    }
    if (data.createInKeycloak && !data.password.trim()) {
      return 'Le mot de passe est requis pour les utilisateurs Keycloak';
    }
    if (data.createInKeycloak && data.password.trim().length < 8) {
      return 'Le mot de passe doit contenir au moins 8 caractères';
    }

    // Validation email basique
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email.trim())) {
      return "Format d'email invalide";
    }

    return null;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    console.log('Données du formulaire:', formData);

    // Validation
    const validationError = validateForm(formData);
    if (validationError) {
      setError(validationError);
      return;
    }

    // Nettoyer et préparer les données
    const cleanedData = cleanFormData(formData);
    console.log('Données nettoyées à envoyer:', cleanedData);

    // Envoyer la requête
    createUserMutation.mutate(cleanedData);
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user starts typing
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4" noValidate>
      {isSSO && (
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            Cet utilisateur sera créé via SSO (Single Sign-On). Certaines informations seront gérées
            par le système d'authentification externe.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="username">Nom d'utilisateur *</Label>
          <Input
            id="username"
            value={formData.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            placeholder="john.doe"
            disabled={isSSO}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email *</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="<EMAIL>"
            disabled={isSSO}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">Prénom</Label>
          <Input
            id="firstName"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            placeholder="John"
            disabled={isSSO}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastName">Nom</Label>
          <Input
            id="lastName"
            value={formData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            placeholder="Doe"
            disabled={isSSO}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="locale">Langue</Label>
          <select
            id="locale"
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={formData.locale}
            onChange={(e) => handleInputChange('locale', e.target.value)}
            disabled={isSSO}
          >
            <option value="fr">Français</option>
            <option value="en">Anglais</option>
          </select>
        </div>

        <div>
          <ColorPicker
            value={formData.color}
            onChange={(color) => handleInputChange('color', color)}
            disabled={isSSO}
            label="Couleur"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="role">Rôle *</Label>
        <select
          id="role"
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          value={formData.role}
          onChange={(e) => handleInputChange('role', e.target.value as UserRole)}
          disabled={isSSO}
        >
          <option value="" disabled>
            -- Sélectionner un rôle --
          </option>
          <option value={UserRole.Manager}>Manageur</option>
          <option value={UserRole.Receptionist}>Réceptionniste</option>
          <option value={UserRole.Deliverer}>Livreur</option>
        </select>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="createInKeycloak"
          checked={formData.createInKeycloak}
          onCheckedChange={(checked) => handleInputChange('createInKeycloak', checked)}
          disabled={isSSO}
        />
        <Label htmlFor="createInKeycloak">
          Créer dans Keycloak (décochez pour les utilisateurs SSO externes)
        </Label>
      </div>

      {formData.createInKeycloak && !isSSO && (
        <div className="space-y-2">
          <Label htmlFor="password">Mot de passe *</Label>
          <Input
            id="password"
            type="password"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            placeholder="Mot de passe sécurisé"
            minLength={8}
          />
          <p className="text-sm text-muted-foreground">Minimum 8 caractères</p>
        </div>
      )}

      {error && <p className="text-sm text-destructive">{error}</p>}

      <Button type="submit" disabled={createUserMutation.isPending || isSSO} className="w-full">
        {createUserMutation.isPending ? 'Création...' : "Créer l'utilisateur"}
      </Button>
    </form>
  );
}
