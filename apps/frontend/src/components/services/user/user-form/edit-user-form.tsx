'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { ColorPicker } from '@/components/ui/color-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { UserRole } from '@/interfaces/enum/user-role.enum';
import { userApiService } from '@/lib/api-service/user-api-service';
import type { UpdateUserRequestDto } from '@/lib/dto/user-update.dto';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { InfoIcon } from 'lucide-react';
import { useState } from 'react';

interface EditUserFormProps {
  user: IUserEntity;
  onSuccess?: () => void;
}

interface FormData {
  firstName: string;
  lastName: string;
  locale: string;
  role: UserRole | '';
  color: string;
}

export function EditUserForm({ user, onSuccess }: EditUserFormProps) {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState<FormData>({
    firstName: user.firstName || '',
    lastName: user.lastName || '',
    locale: user.locale || 'fr',
    role: user.roles?.[0] || '',
    color: user.color || '#000000',
  });
  const [error, setError] = useState('');

  const isSSO = user.isSSO === true;

  const updateUserMutation = useMutation({
    mutationFn: (userData: UpdateUserRequestDto) => userApiService.updateUser(user.id, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', user.id] });
      onSuccess?.();
    },
    onError: (error: Error & { response?: { data?: { message?: string } } }) => {
      console.error('Erreur mise à jour utilisateur:', error);
      const message =
        error.response?.data?.message ||
        error.message ||
        "Erreur lors de la mise à jour de l'utilisateur";
      setError(message);
    },
  });

  const cleanFormData = (data: FormData): UpdateUserRequestDto => {
    const cleanedData: UpdateUserRequestDto = {};

    // Pour les utilisateurs SSO, on ne peut modifier que la couleur
    if (isSSO) {
      if (data.color.trim() !== (user.color || '')) {
        cleanedData.color = data.color.trim();
      }
      return cleanedData;
    }

    // Pour les utilisateurs non-SSO, on peut modifier tous les champs
    if (data.firstName.trim() !== (user.firstName || '')) {
      cleanedData.firstName = data.firstName.trim();
    }
    if (data.lastName.trim() !== (user.lastName || '')) {
      cleanedData.lastName = data.lastName.trim();
    }
    if (data.locale.trim() !== (user.locale || 'fr')) {
      cleanedData.locale = data.locale.trim();
    }
    const currentRole = user.roles?.[0] || '';
    if (data.role !== currentRole && data.role !== '') {
      cleanedData.role = data.role as UserRole;
    }
    if (data.color.trim() !== (user.color || '')) {
      cleanedData.color = data.color.trim();
    }

    return cleanedData;
  };

  const validateForm = (data: FormData): string | null => {
    // Validation minimale - au moins un champ doit être modifié
    const cleanedData = cleanFormData(data);
    if (Object.keys(cleanedData).length === 0) {
      return 'Aucune modification détectée';
    }

    return null;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    console.log('Données du formulaire:', formData);

    // Validation
    const validationError = validateForm(formData);
    if (validationError) {
      setError(validationError);
      return;
    }

    // Nettoyer et préparer les données
    const cleanedData = cleanFormData(formData);
    console.log('Données nettoyées à envoyer:', cleanedData);

    // Envoyer la requête
    updateUserMutation.mutate(cleanedData);
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user starts typing
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4" noValidate>
      {isSSO && (
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            Cet utilisateur est connecté via SSO (Single Sign-On). Les informations personnelles
            (nom, prénom, email, langue, rôle) sont gérées par le système d'authentification externe
            et ne peuvent pas être modifiées ici. Seule la couleur peut être personnalisée.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">Prénom</Label>
          <Input
            id="firstName"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            placeholder="John"
            disabled={isSSO}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastName">Nom</Label>
          <Input
            id="lastName"
            value={formData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            placeholder="Doe"
            disabled={isSSO}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="locale">Langue</Label>
        <select
          id="locale"
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          value={formData.locale}
          onChange={(e) => handleInputChange('locale', e.target.value)}
          disabled={isSSO}
        >
          <option value="fr">Français</option>
          <option value="en">English</option>
        </select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="role">Rôle</Label>
        <select
          id="role"
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          value={formData.role}
          onChange={(e) => handleInputChange('role', e.target.value as UserRole)}
          disabled={isSSO}
        >
          <option value="">-- Aucun rôle --</option>
          <option value={UserRole.Manager}>Manageur</option>
          <option value={UserRole.Receptionist}>Réceptionniste</option>
          <option value={UserRole.Deliverer}>Livreur</option>
        </select>
      </div>

      <div>
        <ColorPicker
          value={formData.color}
          onChange={(color) => handleInputChange('color', color)}
          disabled={false}
          label="Couleur"
        />
      </div>

      {error && <p className="text-sm text-destructive">{error}</p>}

      <Button type="submit" disabled={updateUserMutation.isPending} className="w-full">
        {updateUserMutation.isPending ? 'Mise à jour...' : "Mettre à jour l'utilisateur"}
      </Button>
    </form>
  );
}
