'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { importManagerApiService } from '@/lib/api-service/import-manager-api-service';
import type { IImportEntity } from '@/interfaces/entity/i-import-entity';
import { ImportStatus } from '@/interfaces/enum/import-status.enum';
import { useQuery } from '@tanstack/react-query';
import {
  AlertCircleIcon,
  CheckCircleIcon,
  FileTextIcon,
  InfoIcon,
  LoaderIcon,
  MinusCircleIcon,
  XCircleIcon,
} from 'lucide-react';

interface ImportDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  importId?: string;
}

export function ImportDetailsModal({ open, onOpenChange, importId }: ImportDetailsModalProps) {
  const {
    data: importDetails,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['import-details', importId],
    queryFn: () => importManagerApiService.getImportDetails(importId!),
    enabled: open && !!importId,
  });

  const getStatusColor = (status: ImportStatus) => {
    switch (status) {
      case ImportStatus.Completed:
        return 'bg-green-100 text-green-800 border-green-200';
      case ImportStatus.Failed:
        return 'bg-red-100 text-red-800 border-red-200';
      case ImportStatus.PartiallyCompleted:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case ImportStatus.Skipped:
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusLabel = (status: ImportStatus) => {
    switch (status) {
      case ImportStatus.Completed:
        return 'Réussi';
      case ImportStatus.Failed:
        return 'Échec';
      case ImportStatus.PartiallyCompleted:
        return 'Partiellement réussi';
      case ImportStatus.Skipped:
        return 'Ignoré';
      default:
        return 'Inconnu';
    }
  };

  const getStatusIcon = (status: ImportStatus) => {
    switch (status) {
      case ImportStatus.Completed:
        return <CheckCircleIcon className="h-4 w-4" />;
      case ImportStatus.Failed:
        return <XCircleIcon className="h-4 w-4" />;
      case ImportStatus.PartiallyCompleted:
        return <AlertCircleIcon className="h-4 w-4" />;
      case ImportStatus.Skipped:
        return <MinusCircleIcon className="h-4 w-4" />;
      default:
        return <InfoIcon className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  if (!importId) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileTextIcon className="h-5 w-5 text-primary" />
            Détails de l'import
          </DialogTitle>
          <DialogDescription>
            Informations détaillées sur l'import et les tours traités
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <LoaderIcon className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Chargement des détails...</span>
          </div>
        ) : error ? (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <div className="flex items-center gap-2">
              <XCircleIcon className="h-5 w-5 text-red-600" />
              <span className="font-medium text-red-800">Erreur de chargement</span>
            </div>
            <p className="mt-1 text-sm text-red-700">
              Impossible de charger les détails de l'import.
            </p>
          </div>
        ) : importDetails ? (
          <div className="max-h-[60vh] overflow-y-auto">
            <div className="space-y-6">
              {/* Import Information */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Informations générales</h3>
                  <Badge
                    className={`${getStatusColor(importDetails.status)} flex items-center gap-1`}
                  >
                    {getStatusIcon(importDetails.status)}
                    {getStatusLabel(importDetails.status)}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">ID :</span>
                    <span className="ml-2 font-mono">{importDetails.id}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Date d'import :</span>
                    <span className="ml-2">{formatDate(importDetails.importDate)}</span>
                  </div>
                </div>

                {/* Statistics */}
                <div className="grid grid-cols-4 gap-4">
                  <div className="rounded-lg border bg-green-50 p-3 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {importDetails.successfulRecords}
                    </div>
                    <div className="text-sm text-green-700">Réussites</div>
                  </div>
                  <div className="rounded-lg border bg-red-50 p-3 text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {importDetails.failedRecords}
                    </div>
                    <div className="text-sm text-red-700">Échecs</div>
                  </div>
                  <div className="rounded-lg border bg-gray-50 p-3 text-center">
                    <div className="text-2xl font-bold text-gray-600">
                      {importDetails.skippedRecords}
                    </div>
                    <div className="text-sm text-gray-700">Ignorés</div>
                  </div>
                  <div className="rounded-lg border bg-blue-50 p-3 text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {importDetails.tours?.length || 0}
                    </div>
                    <div className="text-sm text-blue-700">Tours traités</div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Tours Section */}
              {importDetails.tours && importDetails.tours.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <CheckCircleIcon className="h-5 w-5 text-green-600" />
                    Tours importés avec succès ({importDetails.tours.length})
                  </h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {importDetails.tours.map((tour, index) => (
                      <div key={tour.id || index} className="rounded border bg-green-50 p-3">
                        <div className="flex items-center justify-between">
                          <div className="font-medium">
                            {tour.tourIdentifier?.originalNumber || `Tour ${index + 1}`}
                          </div>
                          <Badge variant="outline" className="text-green-700 border-green-300">
                            {tour.status || 'Importé'}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Errors Section */}
              {importDetails.errors && importDetails.errors.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <XCircleIcon className="h-5 w-5 text-red-600" />
                      Erreurs détectées ({importDetails.errors.length})
                    </h3>
                    <div className="space-y-3 max-h-40 overflow-y-auto">
                      {importDetails.errors.map((error, index) => (
                        <div key={index} className="rounded border border-red-200 bg-red-50 p-3">
                          <div className="flex items-start gap-2">
                            <AlertCircleIcon className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                            <div className="flex-1">
                              <div className="font-medium text-red-800">{error.fileName}</div>
                              <div className="text-sm text-red-700 mt-1">{error.message}</div>
                              {error.details && (
                                <div className="text-xs text-red-600 mt-2 font-mono bg-red-100 p-2 rounded">
                                  {JSON.stringify(error.details, null, 2)}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        ) : null}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Fermer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default ImportDetailsModal;
