'use client';

import { Ava<PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { UserRole } from '@/interfaces/enum/user-role.enum';
import { userApiService } from '@/lib/api-service/user-api-service';
import { ColumnDef } from '@tanstack/react-table';
import { EditIcon, EyeIcon, TruckIcon } from 'lucide-react';
import { useState } from 'react';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';

interface DeliverersTableSearchParams extends Record<string, unknown> {
  search?: string;
  roles?: string;
}

interface DeliverersDataTableProps {
  className?: string;
}

export function DeliverersDataTable({ className }: DeliverersDataTableProps) {
  const handleViewUser = (user: IUserEntity) => {
    console.log('View deliverer:', user);
  };

  const handleViewUsers = (selectedUsers: IUserEntity[]) => {
    console.log('View deliverers:', selectedUsers);
  };

  const handleEditUsers = (selectedUsers: IUserEntity[]) => {
    console.log('Edit deliverers:', selectedUsers);
  };

  const columns: ColumnDef<IUserEntity>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Tout sélectionner"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Sélectionner la ligne"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: 'Livreur',
      accessorKey: 'username',
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback
              className="text-xs font-medium"
              style={{ backgroundColor: row.original.color ?? undefined }}
            >
              {row.original.firstName?.[0] || row.original.username[0].toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium">{row.original.username}</span>
              {row.original.isSSO && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  SSO
                </Badge>
              )}
              {row.original.roles?.includes(UserRole.Deliverer) && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  <TruckIcon className="w-3 h-3 mr-1" />
                  Livreur
                </Badge>
              )}
            </div>
            {(row.original.firstName || row.original.lastName) && (
              <div className="text-sm text-muted-foreground">
                {[row.original.firstName, row.original.lastName].filter(Boolean).join(' ')}
              </div>
            )}
          </div>
        </div>
      ),
      size: 250,
      enableHiding: false,
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: ({ row }) => (
        <div className="max-w-48 truncate">
          {row.original.email || <span className="text-muted-foreground italic">Aucun email</span>}
        </div>
      ),
      size: 200,
    },
    {
      header: 'Langue',
      accessorKey: 'locale',
      cell: ({ row }) => (
        <Badge variant="outline" className="uppercase">
          {row.original.locale || 'FR'}
        </Badge>
      ),
      size: 80,
      enableSorting: false,
    },
    {
      header: 'Couleur',
      accessorKey: 'color',
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-full border border-gray-200"
              style={{ backgroundColor: row.original.color || '#cccccc' }}
              aria-label={`Couleur: ${row.original.color || 'Non définie'}`}
            />
            <span className="text-xs text-muted-foreground font-mono">
              {row.original.color || 'Non définie'}
            </span>
          </div>
        );
      },
      size: 120,
      enableSorting: true,
      enableHiding: true,
    },
    {
      header: 'Rôles',
      accessorKey: 'roles',
      cell: ({ row }) => {
        const roles = row.original.roles || [];
        const roleLabels = {
          'lrg-bl-manager': 'Manageur',
          'lrg-bl-receptionist': 'Réceptionniste',
          'lrg-bl-deliverer': 'Livreur',
          'lrg-bl-admin': 'Admin',
        };

        return (
          <div className="flex flex-wrap gap-1">
            {roles.length > 0 ? (
              roles.map((role, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {roleLabels[role as keyof typeof roleLabels] || role}
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground italic text-xs">Aucun rôle</span>
            )}
          </div>
        );
      },
      size: 140,
      enableSorting: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewUser(row.original)}
          className="h-8 w-8 p-0"
        >
          <EyeIcon className="h-4 w-4" />
          <span className="sr-only">Voir les détails de {row.original.username}</span>
        </Button>
      ),
      size: 80,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<IUserEntity, DeliverersTableSearchParams> = {
    export: {
      enabled: true,
      fileName: 'livreurs',
      sheetName: 'Livreurs',
      excludeColumns: ['select', 'actions'],
      columnMap: {
        username: "Nom d'utilisateur",
        email: 'Email',
        locale: 'Langue',
        color: 'Couleur',
        roles: 'Rôles',
      },
      valueTransformers: {
        roles: (value) => {
          const roles = Array.isArray(value) ? value : [];
          const roleLabels = {
            'lrg-bl-manager': 'Manageur',
            'lrg-bl-receptionist': 'Réceptionniste',
            'lrg-bl-deliverer': 'Livreur',
            'lrg-bl-admin': 'Admin',
          };
          return roles.length > 0
            ? roles.map((role) => roleLabels[role as keyof typeof roleLabels] || role).join(', ')
            : 'Aucun rôle';
        },
        locale: (value) => (value as string)?.toUpperCase() || 'FR',
        color: (value) => (value as string) || 'Non définie',
      },
    },
    columns,
    search: {
      enabled: true,
      placeholder: "Rechercher des livreurs par nom d'utilisateur ou email...",
      searchKey: 'search',
    },
    searchParams: {
      roles: UserRole.Deliverer,
    },
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Voir détails',
        icon: <EyeIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleViewUsers,
        variant: 'outline',
        requiresSelection: true,
      },
      {
        label: 'Modifier',
        icon: <EditIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleEditUsers,
        variant: 'outline',
        requiresSelection: true,
      },
    ],
  };

  return (
    <PaginatedDataTable
      queryKey={['deliverers', 'paginated']}
      queryFn={(params) => userApiService.getUsers(params)}
      config={config}
      className={className}
    />
  );
}

export default DeliverersDataTable;
