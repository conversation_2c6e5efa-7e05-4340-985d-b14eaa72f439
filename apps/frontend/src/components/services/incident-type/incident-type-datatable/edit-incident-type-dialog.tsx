import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { IIncidentTypeEntity } from '@/interfaces/entity/i-incident-type-entity';
import { EditIncidentTypeForm } from '../incident-type-form/edit-incident-type-form';
import { incidentTypeManagerApiService } from '@/lib/api-service/incident-type-manager-api-service';
import type { IUpdateIncidentTypeDto } from '@/lib/dto/incident-type.dto';

interface EditIncidentTypeDialogProps {
  incidentType: IIncidentTypeEntity | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditIncidentTypeDialog({
  incidentType,
  open,
  onOpenChange,
}: EditIncidentTypeDialogProps) {
  const queryClient = useQueryClient();

  const updateMutation = useMutation({
    mutationFn: (data: IUpdateIncidentTypeDto) => {
      if (!incidentType) throw new Error('Incident type is required');
      return incidentTypeManagerApiService.update(incidentType.id, data);
    },
    onSuccess: () => {
      toast.success("Type d'incident modifié avec succès");
      queryClient.invalidateQueries({ queryKey: ['incident-types'] });
      onOpenChange(false);
    },
    onError: (error: unknown) => {
      const message =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data
          ? String(error.response.data.message)
          : 'Une erreur est survenue lors de la modification';
      toast.error(message);
    },
  });

  if (!incidentType) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Modifier le type d'incident</DialogTitle>
          <DialogDescription>
            Modifiez les informations du type d'incident "{incidentType.name}".
          </DialogDescription>
        </DialogHeader>

        <EditIncidentTypeForm
          incidentType={incidentType}
          onSubmit={(data) => updateMutation.mutate(data)}
          isLoading={updateMutation.isPending}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
