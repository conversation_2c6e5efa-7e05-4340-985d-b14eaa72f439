'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { IncidentTypeForm } from '../incident-type-form';
import { incidentTypeManagerApiService } from '@/lib/api-service/incident-type-manager-api-service';
import type { ICreateIncidentTypeDto } from '@/lib/dto/incident-type.dto';

interface CreateIncidentTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateIncidentTypeDialog({
  open,
  onOpenChange,
}: CreateIncidentTypeDialogProps) {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: (data: ICreateIncidentTypeDto) => incidentTypeManagerApiService.create(data),
    onSuccess: () => {
      toast.success("Type d'incident créé avec succès");
      queryClient.invalidateQueries({ queryKey: ['incident-types'] });
      onOpenChange(false);
    },
    onError: (error: unknown) => {
      const message =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data
          ? String(error.response.data.message)
          : 'Une erreur est survenue lors de la création';
      toast.error(message);
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Créer un type d'incident</DialogTitle>
          <DialogDescription>
            Remplissez les informations pour créer un nouveau type d'incident.
          </DialogDescription>
        </DialogHeader>

        <IncidentTypeForm
          onSubmit={(data) => createMutation.mutate(data)}
          isLoading={createMutation.isPending}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
