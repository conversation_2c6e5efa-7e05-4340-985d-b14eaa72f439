'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { IIncidentTypeEntity } from '@/interfaces/entity/i-incident-type-entity';
import { incidentTypeManagerApiService } from '@/lib/api-service/incident-type-manager-api-service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { CheckIcon, EditIcon, PlusIcon, TrashIcon, XIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';
import { CreateIncidentTypeDialog } from './create-incident-type-dialog';
import { EditIncidentTypeDialog } from './edit-incident-type-dialog';

interface IncidentTypeTableSearchParams extends Record<string, unknown> {
  search?: string;
  isActive?: string;
}

export function IncidentTypeDataTable({ className }: IncidentTypeDataTableProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingIncidentType, setEditingIncidentType] = useState<IIncidentTypeEntity | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: (id: string) => incidentTypeManagerApiService.delete(id),
    onSuccess: () => {
      toast.success("Type d'incident supprimé avec succès");
      queryClient.invalidateQueries({ queryKey: ['incident-types'] });
    },
    onError: (error: unknown) => {
      const message =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data
          ? String(error.response.data.message)
          : 'Une erreur est survenue lors de la suppression';
      toast.error(message);
    },
  });

  const handleEditIncidentType = (incidentType: IIncidentTypeEntity) => {
    setEditingIncidentType(incidentType);
    setIsEditDialogOpen(true);
  };

  const handleEditIncidentTypes = (selectedIncidentTypes: IIncidentTypeEntity[]) => {
    if (selectedIncidentTypes.length === 1) {
      handleEditIncidentType(selectedIncidentTypes[0]);
    }
  };

  const handleDeleteIncidentTypes = async (selectedIncidentTypes: IIncidentTypeEntity[]) => {
    if (
      confirm(
        `Êtes-vous sûr de vouloir supprimer ${selectedIncidentTypes.length} type(s) d'incident ?`,
      )
    ) {
      for (const incidentType of selectedIncidentTypes) {
        await deleteMutation.mutateAsync(incidentType.id);
      }
    }
  };

  const handleAddIncidentType = () => {
    setIsCreateDialogOpen(true);
  };

  const columns: ColumnDef<IIncidentTypeEntity>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Tout sélectionner"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Sélectionner la ligne"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: 'Nom',
      accessorKey: 'name',
      cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
      size: 200,
      enableHiding: false,
    },
    {
      header: 'Annule la livraison',
      accessorKey: 'doesCancelDelivery',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {row.original.doesCancelDelivery ? (
            <div className="flex items-center gap-1 text-red-600">
              <CheckIcon className="h-4 w-4" />
              <span className="text-sm font-medium">Oui</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 text-muted-foreground">
              <XIcon className="h-4 w-4" />
              <span className="text-sm">Non</span>
            </div>
          )}
        </div>
      ),
      size: 150,
    },
    {
      header: 'Statut',
      accessorKey: 'isActive',
      cell: ({ row }) => (
        <Badge variant={row.original.isActive ? 'default' : 'secondary'}>
          {row.original.isActive ? 'Actif' : 'Inactif'}
        </Badge>
      ),
      size: 100,
    },
    {
      header: 'Date de création',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const date = new Date(row.original.createdAt);
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString('fr-FR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })}
          </div>
        );
      },
      size: 120,
    },
    {
      header: 'Date de modification',
      accessorKey: 'updatedAt',
      cell: ({ row }) => {
        const date = new Date(row.original.updatedAt);
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString('fr-FR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })}
          </div>
        );
      },
      size: 120,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditIncidentType(row.original)}
            className="h-8 w-8 p-0"
          >
            <EditIcon className="h-4 w-4" />
            <span className="sr-only">Modifier {row.original.name}</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => deleteMutation.mutate(row.original.id)}
            className="h-8 w-8 p-0"
          >
            <TrashIcon className="h-4 w-4" />
            <span className="sr-only">Supprimer {row.original.name}</span>
          </Button>
        </div>
      ),
      size: 120,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<IIncidentTypeEntity, IncidentTypeTableSearchParams> = {
    export: {
      enabled: true,
      fileName: 'types-incident',
      sheetName: "Types d'incident",
      excludeColumns: ['select', 'actions'],
      columnMap: {
        name: 'Nom',
        doesCancelDelivery: 'Annule la livraison',
        isActive: 'Statut',
        createdAt: 'Date de création',
        updatedAt: 'Date de modification',
      },
      valueTransformers: {
        doesCancelDelivery: (value) => (value ? 'Oui' : 'Non'),
        isActive: (value) => (value ? 'Actif' : 'Inactif'),
        createdAt: (value) => {
          return new Date(value as string).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          });
        },
        updatedAt: (value) => {
          return new Date(value as string).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          });
        },
      },
    },
    columns,
    search: {
      enabled: true,
      placeholder: "Rechercher des types d'incident par nom...",
      searchKey: 'search',
    },
    filters: [
      {
        columnKey: 'isActive',
        label: 'Statut',
        type: 'select',
        options: [
          { label: 'Tous les statuts', value: 'all' },
          { label: 'Actif', value: 'true' },
          { label: 'Inactif', value: 'false' },
        ],
        placeholder: 'Filtrer par statut',
      },
      {
        columnKey: 'doesCancelDelivery',
        label: 'Annule la livraison',
        type: 'select',
        options: [
          { label: 'Tous', value: 'all' },
          { label: 'Oui', value: 'true' },
          { label: 'Non', value: 'false' },
        ],
        placeholder: 'Filtrer par impact',
      },
    ],
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Modifier',
        icon: <EditIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleEditIncidentTypes,
        variant: 'outline',
        requiresSelection: true,
      },
      {
        label: 'Supprimer',
        icon: <TrashIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleDeleteIncidentTypes,
        variant: 'destructive',
        requiresSelection: true,
      },
      {
        label: 'Ajouter un type',
        icon: <PlusIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleAddIncidentType,
        variant: 'outline',
        requiresSelection: false,
      },
    ],
  };

  return (
    <>
      <PaginatedDataTable
        queryKey={['incident-types', 'paginated']}
        queryFn={(params) => incidentTypeManagerApiService.findAll(params)}
        config={config}
        className={className}
      />

      <CreateIncidentTypeDialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} />
      <EditIncidentTypeDialog
        incidentType={editingIncidentType}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      />
    </>
  );
}

interface IncidentTypeDataTableProps {
  className?: string;
}

export default IncidentTypeDataTable;
