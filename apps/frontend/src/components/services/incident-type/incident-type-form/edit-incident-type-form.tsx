import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import type { IIncidentTypeEntity } from '@/interfaces/entity/i-incident-type-entity';
import type { IUpdateIncidentTypeDto } from '@/lib/dto/incident-type.dto';

interface EditIncidentTypeFormProps {
  incidentType: IIncidentTypeEntity;
  onSubmit: (data: IUpdateIncidentTypeDto) => void;
  isLoading?: boolean;
  onCancel?: () => void;
}

export function EditIncidentTypeForm({
  incidentType,
  onSubmit,
  isLoading = false,
  onCancel,
}: EditIncidentTypeFormProps) {
  const [name, setName] = useState('');
  const [doesCancelDelivery, setDoesCancelDelivery] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [errors, setErrors] = useState<{ name?: string }>({});

  useEffect(() => {
    if (incidentType) {
      setName(incidentType.name);
      setDoesCancelDelivery(incidentType.doesCancelDelivery);
      setIsActive(incidentType.isActive);
    }
  }, [incidentType]);

  const validateForm = () => {
    const newErrors: { name?: string } = {};

    if (!name.trim()) {
      newErrors.name = 'Le nom est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        name: name.trim(),
        doesCancelDelivery,
        isActive,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nom *</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Nom du type d'incident"
          disabled={isLoading}
        />
        {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="doesCancelDelivery"
            checked={doesCancelDelivery}
            onCheckedChange={setDoesCancelDelivery}
            disabled={isLoading}
          />
          <Label htmlFor="doesCancelDelivery" className="text-sm font-medium">
            Annule la livraison
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">
          Si activé, ce type d'incident annulera automatiquement la livraison
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="isActive"
            checked={isActive}
            onCheckedChange={setIsActive}
            disabled={isLoading}
          />
          <Label htmlFor="isActive" className="text-sm font-medium">
            Actif
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">
          Si désactivé, ce type d'incident ne sera plus disponible pour les nouvelles sélections
        </p>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
            Annuler
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Modification...' : 'Modifier'}
        </Button>
      </div>
    </form>
  );
}
