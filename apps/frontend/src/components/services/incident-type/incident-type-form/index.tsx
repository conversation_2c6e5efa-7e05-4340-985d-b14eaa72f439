import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import type { ICreateIncidentTypeDto } from '@/lib/dto/incident-type.dto';

interface IncidentTypeFormProps {
  onSubmit: (data: ICreateIncidentTypeDto) => void;
  isLoading?: boolean;
  onCancel?: () => void;
}

export function IncidentTypeForm({
  onSubmit,
  isLoading = false,
  onCancel,
}: IncidentTypeFormProps) {
  const [name, setName] = useState('');
  const [doesCancelDelivery, setDoesCancelDelivery] = useState(false);
  const [errors, setErrors] = useState<{ name?: string }>({});

  const validateForm = () => {
    const newErrors: { name?: string } = {};

    if (!name.trim()) {
      newErrors.name = 'Le nom est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        name: name.trim(),
        doesCancelDelivery,
        isActive: true, // Default to active when creating
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nom *</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Nom du type d'incident"
          disabled={isLoading}
        />
        {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="doesCancelDelivery"
            checked={doesCancelDelivery}
            onCheckedChange={setDoesCancelDelivery}
            disabled={isLoading}
          />
          <Label htmlFor="doesCancelDelivery" className="text-sm font-medium">
            Annule la livraison
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">
          Si activé, ce type d'incident annulera automatiquement la livraison
        </p>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
            Annuler
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Création...' : 'Créer'}
        </Button>
      </div>
    </form>
  );
}
