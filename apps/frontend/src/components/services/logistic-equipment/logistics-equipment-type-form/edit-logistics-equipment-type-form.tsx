import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { ILogisticsEquipmentTypeEntity } from '@/interfaces/entity/i-logistics-equipment-type-entity';
import { LogisticsEquipmentKind } from '@/interfaces/enum/logistics-equipment-kind.enum';

interface EditLogisticsEquipmentTypeFormProps {
  logisticsEquipmentType: ILogisticsEquipmentTypeEntity;
  onSubmit: (data: { name: string; kind: LogisticsEquipmentKind }) => void;
  isLoading?: boolean;
  onCancel?: () => void;
}

export function EditLogisticsEquipmentTypeForm({
  logisticsEquipmentType,
  onSubmit,
  isLoading = false,
  onCancel,
}: EditLogisticsEquipmentTypeFormProps) {
  const [name, setName] = useState(logisticsEquipmentType.name);
  const [kind, setKind] = useState<LogisticsEquipmentKind>(logisticsEquipmentType.kind);
  const [errors, setErrors] = useState<{ name?: string; kind?: string }>({});

  useEffect(() => {
    setName(logisticsEquipmentType.name);
    setKind(logisticsEquipmentType.kind);
  }, [logisticsEquipmentType]);

  const validateForm = () => {
    const newErrors: { name?: string; kind?: string } = {};

    if (!name.trim()) {
      newErrors.name = 'Le nom est requis';
    }

    if (!kind) {
      newErrors.kind = 'Le type est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        name: name.trim(),
        kind,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Nom *</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Nom du type d'équipement"
          disabled={isLoading}
        />
        {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="kind">Type *</Label>
        <select
          id="kind"
          value={kind}
          onChange={(e) => setKind(e.target.value as LogisticsEquipmentKind)}
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          disabled={isLoading}
        >
          <option value={LogisticsEquipmentKind.PALLET}>Palette</option>
          <option value={LogisticsEquipmentKind.ROLL}>Roll</option>
          <option value={LogisticsEquipmentKind.PACKAGE}>Colis</option>
        </select>
        {errors.kind && <p className="text-sm text-destructive">{errors.kind}</p>}
      </div>

      <div className="flex justify-end gap-3 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
            Annuler
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Modification...' : 'Modifier'}
        </Button>
      </div>
    </form>
  );
}
