'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { IStopEntity } from '@/interfaces/entity/i-stop-entity';
import type { IPaginationParams } from '@/interfaces/pagination';
import { stopManagerApiService } from '@/lib/api-service/stop-manager-api-service';
import { ColumnDef } from '@tanstack/react-table';
import {
  CalendarIcon,
  Clock,
  EyeIcon,
  MapPinIcon,
  PackageIcon,
  Truck,
  UserIcon,
} from 'lucide-react';
import { useState } from 'react';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';

interface StopTableSearchParams extends Record<string, unknown> {
  date?: string;
}

interface StopsDataTableProps {
  className?: string;
  fixedDate?: string; // Si fourni, utilise cette date au lieu du state interne
  hideInternalDateFilter?: boolean; // Masque le sélecteur de date interne
}

export function StopsDataTable({
  className,
  fixedDate,
  hideInternalDateFilter = false,
}: StopsDataTableProps) {
  const [selectedDate, setSelectedDate] = useState<string>(
    fixedDate || new Date().toISOString().split('T')[0],
  );

  // Utilise la date fixe si fournie, sinon la date sélectionnée
  const currentDate = fixedDate || selectedDate;

  const handleViewStop = (stop: IStopEntity) => {
    console.log('View stop:', stop);
    // TODO: Navigate to stop detail view
  };

  const handleChangeDriver = (stop: IStopEntity) => {
    console.log('Change Driver:', stop);
    // TODO: Change driver
  };

  const handleViewStops = (selectedStops: IStopEntity[]) => {
    console.log('View stops:', selectedStops);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getDeliveryStatusLabel = (completion: IStopEntity['completion']) => {
    if (!completion || !completion.deliveryStatus) {
      return 'En attente';
    }
    switch (completion.deliveryStatus) {
      case 'COMPLETED':
        return 'Terminé';
      case 'FAILED':
        return 'Annulé';
      case 'PENDING':
        return 'En cours';
      default:
        return completion.deliveryStatus;
    }
  };

  const getDeliveryStatusColor = (completion: IStopEntity['completion']) => {
    if (!completion || !completion.deliveryStatus) {
      return 'outline';
    }
    switch (completion.deliveryStatus) {
      case 'COMPLETED':
        return 'default';
      case 'FAILED':
        return 'destructive';
      case 'PENDING':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const columns: ColumnDef<IStopEntity>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Tout sélectionner"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Sélectionner la ligne"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: 'N° Tournée',
      accessorKey: 'tour.tourIdentifier.originalNumber',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <div className="bg-primary/10 p-1.5 rounded">
            <PackageIcon className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="font-medium">
              {row.original.tour?.tourIdentifier?.originalNumber || 'N/A'}
            </div>
            <div className="text-sm text-muted-foreground">
              Séquence: {row.original.sequenceInTour}
            </div>
          </div>
        </div>
      ),
      size: 120,
      enableHiding: false,
    },
    {
      header: 'Client',
      accessorKey: 'client.name',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <div>
            <div className="font-medium max-w-48 truncate">
              {row.original.client?.name || row.original.originalClientInfo?.name || 'N/A'}
            </div>
            {row.original.client?.code && (
              <div className="text-sm text-muted-foreground">{row.original.client.code}</div>
            )}
          </div>
        </div>
      ),
      size: 200,
    },
    {
      header: 'Adresse',
      accessorKey: 'originalClientInfo.address.line1',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <MapPinIcon className="h-4 w-4 text-muted-foreground" />
          <div className="max-w-48 truncate">
            {row.original.originalClientInfo?.address?.line1 || 'N/A'}
          </div>
        </div>
      ),
      size: 200,
      enableSorting: false,
    },
    {
      header: 'Type',
      accessorKey: 'tour.tourIdentifier.type',
      cell: ({ row }) => {
        const tourType = row.original.tour?.tourIdentifier?.type;
        const displayType = tourType === 'FROZEN' ? 'LIVRAISON SURGELÉ' : 'LIVRAISON';
        return (
          <div className="flex items-center gap-2">
            <div className="max-w-48 truncate">{displayType}</div>
          </div>
        );
      },
      size: 120,
      enableSorting: true,
    },
    /*    {
      header: 'Date de tournée',
      accessorKey: 'tour.deliveryDate',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          <span>{formatDate(row.original.tour?.deliveryDate || '')}</span>
        </div>
      ),
      size: 150,
    },*/
    {
      header: 'Horaire prévu',
      accessorKey: 'deliveryTimeWindow',
      cell: ({ row }) => (
        <div className="text-sm">{row.original.deliveryTimeWindow || 'Non défini'}</div>
      ),
      size: 120,
      enableSorting: false,
    },
    {
      header: 'Livré le',
      accessorKey: 'completion.completedAt',
      cell: ({ row }) => {
        const completedAt = row.original.completion?.completedAt;
        return (
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span>
              {completedAt
                ? new Date(completedAt).toLocaleDateString('fr-FR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                  }) +
                  ' à ' +
                  new Date(completedAt).toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                : 'Non livré'}
            </span>
          </div>
        );
      },
      size: 150,
      enableSorting: true,
    },
    {
      header: 'Chauffeur',
      accessorKey: 'driver', // TODO: Add driver relationship
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <UserIcon className="h-4 w-4 text-muted-foreground" />
          <span className="text-muted-foreground">TBD</span>
        </div>
      ),
      size: 120,
      enableSorting: false,
    },
    {
      header: 'Véhicule',
      accessorKey: 'vehicle', // TODO: Add vehicle relationship
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Truck className="h-4 w-4 text-muted-foreground" />
          <span className="max-w-48 truncate text-muted-foreground">TBD</span>
        </div>
      ),
      size: 100,
      enableSorting: false,
    },
    {
      header: 'Statut',
      accessorKey: 'completion.deliveryStatus',
      cell: ({ row }) => (
        <Badge variant={getDeliveryStatusColor(row.original.completion)}>
          {getDeliveryStatusLabel(row.original.completion)}
        </Badge>
      ),
      size: 120,
      enableSorting: true,
    },
    {
      header: 'Comptant',
      accessorKey: 'paymentInfo', // TODO: Add payment info to data model
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs text-muted-foreground">
            TBD
          </Badge>
        </div>
      ),
      size: 120,
      enableSorting: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewStop(row.original)}
            className="h-8 w-8 p-0"
          >
            <EyeIcon className="h-4 w-4" />
            <span className="sr-only">Voir les détails de la livraison</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleChangeDriver(row.original)}
            className="h-8 w-8 p-0"
          >
            <UserIcon className="h-4 w-4 " />
            <span className="sr-only">Changer de chauffeur pour la livraison</span>
          </Button>
        </div>
      ),
      size: 80,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<IStopEntity, StopTableSearchParams> = {
    export: {
      enabled: true,
      fileName: 'arrets',
      sheetName: 'Arrêts',
      excludeColumns: ['select', 'actions'],
      columnMap: {
        'tour.tourIdentifier.originalNumber': 'N° Tournée',
        'client.name': 'Client',
        'originalClientInfo.address.line1': 'Adresse',
        'tour.tourIdentifier.type': 'Type',
        deliveryTimeWindow: 'Horaire prévu',
        'completion.completedAt': 'Livré le',
        driver: 'Chauffeur',
        vehicle: 'Véhicule',
        'completion.deliveryStatus': 'Statut',
        paymentInfo: 'Comptant',
      },
      valueTransformers: {
        'tour.tourIdentifier.originalNumber': (value, row) => {
          const tour = (row as IStopEntity).tour;
          return tour?.tourIdentifier?.originalNumber || 'N/A';
        },
        'client.name': (value, row) => {
          const stop = row as IStopEntity;
          return stop.client?.name || stop.originalClientInfo?.name || 'N/A';
        },
        'originalClientInfo.address.line1': (value, row) => {
          const stop = row as IStopEntity;
          return stop.originalClientInfo?.address?.line1 || 'N/A';
        },
        'tour.tourIdentifier.type': (value, row) => {
          const tour = (row as IStopEntity).tour;
          const tourType = tour?.tourIdentifier?.type;
          return tourType === 'FROZEN' ? 'LIVRAISON SURGELÉ' : 'LIVRAISON';
        },
        deliveryTimeWindow: (value) => (value as string) || 'Non défini',
        'completion.completedAt': (value, row) => {
          const completedAt = (row as IStopEntity).completion?.completedAt;
          if (!completedAt) return 'Non livré';
          const date = new Date(completedAt);
          return (
            date.toLocaleDateString('fr-FR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            }) +
            ' à ' +
            date.toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit',
            })
          );
        },
        driver: () => 'TBD',
        vehicle: () => 'TBD',
        'completion.deliveryStatus': (value, row) => {
          const completion = (row as IStopEntity).completion;
          return getDeliveryStatusLabel(completion);
        },
        paymentInfo: () => 'TBD',
      },
    },
    columns,
    filters: [
      {
        columnKey: 'completion.deliveryStatus',
        label: 'Statut de livraison',
        type: 'select',
        options: [
          { label: 'Tous les statuts', value: 'all' },
          { label: 'En cours', value: 'PENDING' },
          { label: 'Terminée', value: 'COMPLETED' },
          { label: 'Annulée', value: 'FAILED' },
        ],
        placeholder: 'Filtrer par statut',
      },
    ],
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Voir détails',
        icon: <EyeIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleViewStops,
        variant: 'outline',
        requiresSelection: true,
      },
    ],
  };

  const customQueryFn = (params: IPaginationParams & StopTableSearchParams) => {
    return stopManagerApiService.getStopsByDeliveryDate(currentDate, params);
  };

  return (
    <div className="space-y-4">
      {/* Date Filter - Affiché seulement si pas masqué et pas de date fixe */}
      {!hideInternalDateFilter && !fixedDate && (
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="date-filter" className="text-sm font-medium">
              Date de livraison :
            </Label>
            <DatePicker
              value={selectedDate}
              onChange={setSelectedDate}
              placeholder="Sélectionner une date de livraison"
              className="min-w-[200px]"
            />
          </div>
        </div>
      )}

      <PaginatedDataTable
        queryKey={['stops', 'paginated', currentDate]}
        queryFn={customQueryFn}
        config={config}
        className={className}
      />
    </div>
  );
}

export default StopsDataTable;
