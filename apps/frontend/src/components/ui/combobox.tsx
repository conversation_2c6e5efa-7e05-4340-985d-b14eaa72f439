'use client';

import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useDebounce } from '@/hooks/use-debounce';
import { cn, getContainer } from '@/lib/utils';

export interface ComboboxProps<T> {
  options: {
    value: string;
    label: string;
    valueMetadata?: T;
  }[];
  selectedValue: string | null;
  setSelectedValue: (value: { value: string; label: string; valueMetadata?: T }) => void;
  isLoading?: boolean;
  placeholder?: string;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void | Promise<void>;
  noResultsMessage?: string;
  className?: string;
  disabled?: boolean;
  debounceTime?: number;
  container?: HTMLElement;
}

export function Combobox<T>({
  options,
  selectedValue,
  setSelectedValue,
  isLoading = false,
  placeholder = 'Sélectionner...',
  searchPlaceholder = 'Rechercher...',
  onSearch,
  noResultsMessage = 'Aucun résultat trouvé.',
  className = 'w-full',
  disabled = false,
  debounceTime = 300,
  container = getContainer(),
}: ComboboxProps<T>) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, debounceTime);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [selectedOption, setSelectedOption] = React.useState<{
    value: string;
    label: string;
    valueMetadata?: T;
  } | null>(null);

  const selectedLabel = React.useMemo(() => {
    const fromCurrentOptions = options.find((option) => option.value === selectedValue);
    if (fromCurrentOptions) {
      return fromCurrentOptions.label;
    }
    return selectedOption?.label;
  }, [options, selectedValue, selectedOption]);

  React.useEffect(() => {
    if (onSearch && debouncedSearchQuery !== undefined) {
      onSearch(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, onSearch]);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 10);
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild disabled={disabled}>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(className, 'justify-between')}
          disabled={disabled}
        >
          {selectedLabel || placeholder}
          {isLoading && open === false ? (
            <Loader2 className="h-4 w-4 animate-spin opacity-50" />
          ) : (
            <ChevronsUpDown className="h-4 w-4 opacity-50" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className={cn(className, 'p-0')} align="start" container={container}>
        <Command shouldFilter={false}>
          <CommandInput
            ref={inputRef}
            placeholder={searchPlaceholder}
            className="h-9"
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            {isLoading ? (
              <CommandEmpty className="py-6 text-center">
                <Loader2 className="mx-auto h-6 w-6 animate-spin text-primary" />
                <p className="mt-2 text-sm text-muted-foreground">Chargement...</p>
              </CommandEmpty>
            ) : options.length === 0 ? (
              <CommandEmpty>{noResultsMessage}</CommandEmpty>
            ) : (
              <CommandGroup>
                {options.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={() => {
                      setSelectedOption(option);
                      setSelectedValue(option);
                      setSearchQuery('');
                      setOpen(false);
                    }}
                  >
                    {option.label}
                    <Check
                      className={cn(
                        'ml-auto',
                        selectedValue === option.value ? 'opacity-100' : 'opacity-0',
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
