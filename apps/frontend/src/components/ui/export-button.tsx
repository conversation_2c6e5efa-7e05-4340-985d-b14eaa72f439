'use client';

import { useState } from 'react';
import { FileSpreadsheetIcon, DownloadIcon } from 'lucide-react';
import { Button } from './button';
import { Progress } from './progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from './dialog';
import { toast } from 'sonner';
import type { ColumnDef } from '@tanstack/react-table';
import type { IPaginatedResponse, IPaginationParams } from '../../interfaces/pagination';
import type { DataTableConfig } from '../../interfaces/datatable';
import { useDataTableExport } from '../../lib/hooks/use-data-table-export';
import { ExcelExportService } from '../../lib/services/excel-export.service';

export interface ExportButtonProps<
  TData,
  TSearchParams extends Record<string, unknown> = Record<string, unknown>,
> {
  queryFn: (params: IPaginationParams & TSearchParams) => Promise<IPaginatedResponse<TData>>;
  currentParams: IPaginationParams & TSearchParams;
  columns: ColumnDef<TData>[];
  fileName?: string;
  sheetName?: string;
  columnMap?: Record<string, string>;
  excludeColumns?: string[];
  includeOnlyColumns?: string[];
  maxPagesLimit?: number;
  valueTransformers?: Record<
    string,
    (
      value: unknown,
      row: Record<string, unknown>,
      columnKey: string,
    ) => string | number | boolean | Date
  >;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export function ExportButton<
  TData,
  TSearchParams extends Record<string, unknown> = Record<string, unknown>,
>({
  queryFn,
  currentParams,
  columns,
  fileName = 'export',
  sheetName = 'Data',
  columnMap,
  excludeColumns,
  includeOnlyColumns,
  maxPagesLimit = 100,
  valueTransformers,
  variant = 'outline',
  size = 'default',
  className,
}: ExportButtonProps<TData, TSearchParams>) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { exportData, isExporting, exportProgress, error } = useDataTableExport({
    queryFn,
    currentParams,
    config: { columns } as DataTableConfig<TData, TSearchParams>,
    maxPagesLimit,
  });

  const handleExport = async () => {
    setIsDialogOpen(true);

    try {
      const data = await exportData();

      ExcelExportService.exportToExcel({
        data: data as Record<string, unknown>[],
        columns: columns as ColumnDef<Record<string, unknown>>[],
        fileName,
        sheetName,
        columnMap,
        excludeColumns,
        includeOnlyColumns,
        valueTransformers,
      });

      toast.success('Export terminé avec succès');
      setIsDialogOpen(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur lors de l'export";
      toast.error(errorMessage);
    }
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={handleExport}
        disabled={isExporting}
        className={className}
      >
        {isExporting ? (
          <>
            <DownloadIcon className="-ms-1 opacity-60 animate-pulse" size={16} aria-hidden="true" />
            Export en cours...
          </>
        ) : (
          <>
            <FileSpreadsheetIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />
            Exporter en Excel
          </>
        )}
      </Button>

      <Dialog open={isDialogOpen && isExporting} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Export en cours</DialogTitle>
            <DialogDescription>
              Récupération des données en cours. Veuillez patienter...
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Progress value={exportProgress * 100} className="w-full" />
            <p className="text-sm text-muted-foreground text-center">
              {Math.round(exportProgress * 100)}% complété
            </p>
            {error && (
              <p className="text-sm text-destructive text-center">Erreur: {error.message}</p>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
