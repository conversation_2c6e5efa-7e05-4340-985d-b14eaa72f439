import { cn } from '../../lib/utils';
import { Label } from './label';

interface FormFieldProps {
  children: React.ReactNode;
  label: string;
  error?: string;
  variant?: 'default' | 'compact';
}

export function FormField({ children, label, error, variant = 'default' }: FormFieldProps) {
  return (
    <div className={cn('space-y-2', variant === 'compact' && 'space-y-1')}>
      <Label className={cn(variant === 'compact' && 'text-xs')} htmlFor={label}>
        {label}
      </Label>
      {children}
      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
}
