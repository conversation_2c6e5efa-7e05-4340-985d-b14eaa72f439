'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CheckIcon, PaletteIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ColorPickerProps {
  value?: string;
  onChange: (color: string) => void;
  disabled?: boolean;
  label?: string;
  defaultColors?: string[];
}

const DEFAULT_COLORS = [
  '#EF4444', // red-500
  '#F97316', // orange-500
  '#EAB308', // yellow-500
  '#22C55E', // green-500
  '#3B82F6', // blue-500
  '#8B5CF6', // violet-500
  '#EC4899', // pink-500
  '#64748B', // slate-500
];

export function ColorPicker({
  value = '#000000',
  onChange,
  disabled = false,
  label = 'Couleur',
  defaultColors = DEFAULT_COLORS,
}: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [customColor, setCustomColor] = useState(value);

  useEffect(() => {
    setCustomColor(value);
  }, [value]);

  const handleColorSelect = (color: string) => {
    onChange(color);
    setCustomColor(color);
    setIsOpen(false);
  };

  const handleCustomColorChange = (color: string) => {
    setCustomColor(color);
    onChange(color);
  };

  const handleCustomColorInput = (inputValue: string) => {
    setCustomColor(inputValue);
    if (/^#[0-9A-Fa-f]{6}$/.test(inputValue)) {
      onChange(inputValue);
    }
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            disabled={disabled}
            className={cn(
              'w-full justify-start text-left font-normal',
              !value && 'text-muted-foreground',
            )}
          >
            <div className="flex items-center gap-2">
              <div
                className="h-4 w-4 rounded border border-gray-200"
                style={{ backgroundColor: value }}
              />
              <span className="font-mono text-sm">{value}</span>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-72 p-4" container={null}>
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium mb-2 block">Couleurs prédéfinies</Label>
              <div className="flex gap-2 justify-center">
                {defaultColors.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={cn(
                      'w-6 h-6 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors relative',
                      value === color && 'border-gray-600 ring-2 ring-gray-300',
                    )}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorSelect(color)}
                    title={color}
                  >
                    {value === color && (
                      <CheckIcon className="w-4 h-4 text-white absolute inset-0 m-auto drop-shadow-sm" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            <div className="border-t pt-4">
              <Label className="text-sm font-medium mb-3 block">Couleur libre</Label>

              <div className="flex items-center gap-3 mb-3">
                <div className="relative">
                  <input
                    type="color"
                    value={customColor}
                    onChange={(e) => handleCustomColorChange(e.target.value)}
                    className="w-12 h-12 rounded-md border border-gray-200 cursor-pointer bg-transparent"
                    style={{ padding: '2px' }}
                  />
                  <PaletteIcon className="w-4 h-4 text-gray-400 absolute top-1 right-1 pointer-events-none" />
                </div>
                <div className="flex-1">
                  <Input
                    type="text"
                    value={customColor}
                    onChange={(e) => handleCustomColorInput(e.target.value)}
                    placeholder="#000000"
                    className="font-mono text-sm"
                    maxLength={7}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Format: #RRGGBB</p>
                </div>
              </div>

              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleColorSelect(customColor)}
                className="w-full"
                disabled={!/^#[0-9A-Fa-f]{6}$/.test(customColor)}
              >
                Appliquer cette couleur
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
