import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from './redux';
import {
  hideLoader,
  selectIsLoading,
  selectLoadingMessage,
  showLoader,
} from '@/lib/store/loading-slice';

export function useLoading() {
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectIsLoading);
  const message = useAppSelector(selectLoadingMessage);

  const startLoading = useCallback(
    (loadingMessage?: string) => {
      dispatch(showLoader(loadingMessage));
    },
    [dispatch],
  );

  const stopLoading = useCallback(() => {
    dispatch(hideLoader());
  }, [dispatch]);

  const withLoading = useCallback(
    async <T>(promise: Promise<T>, loadingMessage?: string): Promise<T> => {
      startLoading(loadingMessage);
      try {
        return await promise;
      } finally {
        stopLoading();
      }
    },
    [startLoading, stopLoading],
  );

  return {
    isLoading,
    message,
    startLoading,
    stopLoading,
    withLoading,
  };
}
