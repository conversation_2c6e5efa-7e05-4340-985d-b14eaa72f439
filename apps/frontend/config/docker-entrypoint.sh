#!/bin/sh
set -e

INDEX_HTML="/usr/share/nginx/html/index.html"

# Collect all VITE_ env vars and build a JSON object
VITE_ENV_JSON=$(printenv | grep '^VITE_' | awk -F= '{printf "\"%s\":\"%s\",", $1, gensub(/"/, "\\\"", "g", $2)}' | sed 's/,$//')
VITE_ENV_JSON="{$VITE_ENV_JSON}"

# Escape for sed (" -> \")
ESCAPED_JSON=$(printf '%s' "$VITE_ENV_JSON" | sed 's/"/\\"/g')

# Replace the meta tag content in index.html
sed -i "s|<meta name=\"env\" content=\".*\"|<meta name=\"env\" content=\"$ESCAPED_JSON\"|" "$INDEX_HTML"

exec "$@" 