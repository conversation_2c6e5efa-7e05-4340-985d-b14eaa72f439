import * as fs from 'fs';
import * as path from 'path';

// Function to convert from camelCase/PascalCase to kebab-case
function toKebabCase(str: string): string {
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
    .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
    .toLowerCase();
}

// Function to check if a string is in camelCase or PascalCase
function isCamelOrPascalCase(str: string): boolean {
  return /^[a-zA-Z0-9]+$/.test(str) && /[A-Z]/.test(str) && str !== 'index';
}

// Function to check if a file should be excluded from processing
function shouldExcludeFile(filePath: string): boolean {
  // Exclude type definition files, test files if needed
  return (
    filePath.includes('.d.ts') || filePath.includes('node_modules') || filePath.includes('dist')
  );
}

// Function to get all TypeScript and TSX files in a directory recursively
function getTsFiles(dirPath: string, fileList: string[] = []): string[] {
  if (!fs.existsSync(dirPath)) {
    console.warn(`Directory does not exist: ${dirPath}`);
    return fileList;
  }

  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const filePath = path.join(dirPath, file);

    if (fs.statSync(filePath).isDirectory()) {
      fileList = getTsFiles(filePath, fileList);
    } else if (/\.(ts|tsx)$/.test(file) && !shouldExcludeFile(filePath)) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Main function to execute the analysis
function main() {
  try {
    const srcDir = path.resolve(process.cwd(), 'src');
    console.log(`Scanning directory: ${srcDir}`);

    // Get all TS and TSX files
    const tsFiles = getTsFiles(srcDir);
    console.log(`Found ${tsFiles.length} TypeScript files`);

    // Files to be renamed
    const filesToRename = [];

    // Analyze the files
    for (const filePath of tsFiles) {
      const dirName = path.dirname(filePath);
      const baseName = path.basename(filePath);
      const extName = path.extname(filePath);
      const fileName = baseName.replace(extName, '');

      if (isCamelOrPascalCase(fileName)) {
        const kebabFileName = toKebabCase(fileName) + extName;
        const newPath = path.join(dirName, kebabFileName);

        filesToRename.push({
          oldPath: filePath,
          newPath,
          oldName: baseName,
          newName: kebabFileName,
        });
      }
    }

    // Display results
    console.log('\n===== Files to be renamed =====');
    console.log(`Total files that would be renamed: ${filesToRename.length}`);

    if (filesToRename.length > 0) {
      console.log('\nFiles:');
      filesToRename.forEach((file) => {
        console.log(`- ${file.oldName} → ${file.newName}`);
      });
    } else {
      console.log('No files need to be renamed.');
    }

    console.log(
      '\nThis is just a preview. To perform the actual renaming, run the migrate-to-kebab-case.ts script.',
    );
  } catch (error) {
    console.error('Error during analysis:', error);
    process.exit(1);
  }
}

main();
