# File Naming Migration Script

This script converts all TypeScript and TSX files in the frontend project from camelCase/PascalCase to kebab-case, and updates all imports accordingly.

## How It Works

1. Scans all `.ts` and `.tsx` files in the `src` directory
2. Identifies files with camelCase or PascalCase names
3. Creates a mapping of old filenames to new kebab-case filenames
4. Updates all import declarations in files to use the new kebab-case filenames
5. Renames the files to their kebab-case equivalents

## Usage

### List Files (Preview Mode)

To see which files would be renamed without actually making any changes:

```bash
# From the frontend directory
pnpm list-files-to-rename

# Or directly from the scripts directory
cd scripts
pnpm install
pnpm list
```

### Perform Migration

To run the full migration script (will rename files and update imports):

```bash
# From the frontend directory
pnpm migrate-to-kebab

# Or directly from the scripts directory
cd scripts
pnpm install
pnpm migrate
```

## Backup Your Code

It's recommended to commit your code before running this script, as it makes significant changes to file names and imports.

## Additional Notes

- The script only affects files with camelCase or PascalCase names
- Files that are already in kebab-case will remain unchanged
- The script preserves file extensions
- Type definition files (`.d.ts`) are excluded from renaming
- The migration script includes a confirmation prompt before making changes 