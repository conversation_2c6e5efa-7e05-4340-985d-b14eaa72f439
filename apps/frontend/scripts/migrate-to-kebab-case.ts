import * as fs from 'fs';
import * as path from 'path';
import { Project, ScriptTarget } from 'ts-morph';

// Function to convert from camelCase/PascalCase to kebab-case
function toKebabCase(str: string): string {
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
    .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2')
    .toLowerCase();
}

// Function to check if a string is in camelCase or PascalCase
function isCamelOrPascalCase(str: string): boolean {
  return /^[a-zA-Z0-9]+$/.test(str) && /[A-Z]/.test(str) && str !== 'index';
}

// Function to check if a file should be excluded from processing
function shouldExcludeFile(filePath: string): boolean {
  // Exclude type definition files, test files if needed
  return (
    filePath.includes('.d.ts') || filePath.includes('node_modules') || filePath.includes('dist')
  );
}

// Function to get all TypeScript and TSX files in a directory recursively
function getTsFiles(dirPath: string, fileList: string[] = []): string[] {
  if (!fs.existsSync(dirPath)) {
    console.warn(`Directory does not exist: ${dirPath}`);
    return fileList;
  }

  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const filePath = path.join(dirPath, file);

    if (fs.statSync(filePath).isDirectory()) {
      fileList = getTsFiles(filePath, fileList);
    } else if (/\.(ts|tsx)$/.test(file) && !shouldExcludeFile(filePath)) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Map to store original filenames and their kebab-case versions
const fileNameMap = new Map<string, string>();

// Function to display a summary of changes
function displaySummary() {
  console.log('\n===== Migration Summary =====');
  console.log(`Total files to be renamed: ${fileNameMap.size}`);

  if (fileNameMap.size > 0) {
    console.log('\nFiles to be renamed:');
    for (const [oldPath, newPath] of fileNameMap.entries()) {
      const oldName = path.basename(oldPath);
      const newName = path.basename(newPath);
      console.log(`- ${oldName} → ${newName}`);
    }
  }
}

// Function to ask for confirmation before proceeding
function askForConfirmation(): Promise<boolean> {
  if (fileNameMap.size === 0) {
    console.log('No files need to be renamed. Exiting...');
    return Promise.resolve(false);
  }

  displaySummary();

  return new Promise((resolve) => {
    console.log('\nWARNING: This operation will rename files and update imports.');
    console.log('It is recommended to commit your changes before proceeding.');
    process.stdout.write('Do you want to continue? (y/N): ');

    process.stdin.once('data', (data) => {
      const input = data.toString().trim().toLowerCase();
      resolve(input === 'y' || input === 'yes');
    });
  });
}

// Main function to execute the migration
async function main() {
  try {
    // Initialize ts-morph project
    const project = new Project({
      compilerOptions: {
        target: ScriptTarget.ESNext,
      },
    });

    const srcDir = path.resolve(process.cwd(), 'src');
    console.log(`Scanning directory: ${srcDir}`);

    // Get all TS and TSX files
    const tsFiles = getTsFiles(srcDir);
    console.log(`Found ${tsFiles.length} TypeScript files`);

    // First pass: Add all files to the project and map filenames
    tsFiles.forEach((filePath) => {
      project.addSourceFileAtPath(filePath);

      const dirName = path.dirname(filePath);
      const baseName = path.basename(filePath);
      const extName = path.extname(filePath);
      const fileName = baseName.replace(extName, '');

      if (isCamelOrPascalCase(fileName)) {
        const kebabFileName = toKebabCase(fileName) + extName;
        const newPath = path.join(dirName, kebabFileName);

        fileNameMap.set(filePath, newPath);
      }
    });

    // Ask for confirmation before proceeding
    const shouldProceed = await askForConfirmation();
    if (!shouldProceed) {
      console.log('Operation canceled.');
      process.exit(0);
    }

    console.log('\nUpdating imports...');

    // Second pass: Update all import declarations
    let updatedFiles = 0;

    project.getSourceFiles().forEach((sourceFile) => {
      let hasChanged = false;

      // Update imports
      sourceFile.getImportDeclarations().forEach((importDecl) => {
        const moduleSpecifier = importDecl.getModuleSpecifierValue();

        // Skip node_modules imports
        if (moduleSpecifier.startsWith('.') || moduleSpecifier.startsWith('/')) {
          const importPath =
            moduleSpecifier.endsWith('.tsx') || moduleSpecifier.endsWith('.ts')
              ? moduleSpecifier
              : `${moduleSpecifier}`;

          const currentFilePath = sourceFile.getFilePath();
          const currentDir = path.dirname(currentFilePath);

          // Resolve the absolute path of the import
          const resolvedPath = path.resolve(currentDir, importPath);

          // Try different extensions if the import doesn't specify one
          const possiblePaths = [
            resolvedPath,
            `${resolvedPath}.ts`,
            `${resolvedPath}.tsx`,
            `${resolvedPath}/index.ts`,
            `${resolvedPath}/index.tsx`,
          ];

          for (const possiblePath of possiblePaths) {
            // If we've mapped this file, update the import
            if (fileNameMap.has(possiblePath)) {
              const newPath = fileNameMap.get(possiblePath)!;
              const relativePath = path.relative(currentDir, newPath).replace(/\\/g, '/');

              // Make sure the path starts with ./ or ../
              const newModuleSpecifier = relativePath.startsWith('.')
                ? relativePath
                : `./${relativePath}`;

              // Remove file extension from import
              const newSpecifier = newModuleSpecifier.replace(/\.(ts|tsx)$/, '');

              importDecl.setModuleSpecifier(newSpecifier);
              hasChanged = true;
              break;
            }
          }
        }
      });

      if (hasChanged) {
        sourceFile.saveSync();
        updatedFiles++;
      }
    });

    console.log(`Updated imports in ${updatedFiles} files`);
    console.log('\nRenaming files...');

    // Third pass: Rename the files
    let renamedFiles = 0;
    for (const [oldPath, newPath] of fileNameMap.entries()) {
      const oldBaseName = path.basename(oldPath);
      const newBaseName = path.basename(newPath);

      console.log(`Renaming ${oldBaseName} to ${newBaseName}`);

      try {
        fs.renameSync(oldPath, newPath);
        renamedFiles++;
      } catch (error) {
        console.error(`Error renaming ${oldPath}: ${error}`);
      }
    }

    console.log(`\nMigration complete!`);
    console.log(`- Renamed ${renamedFiles} files to kebab-case`);
    console.log(`- Updated imports in ${updatedFiles} files`);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

main().catch(console.error);
