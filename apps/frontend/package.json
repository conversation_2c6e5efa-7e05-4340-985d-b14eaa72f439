{"name": "@lrg/frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"./src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "format:check": "prettier --check \"./src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "tidy": "pnpm format && pnpm lint:fix", "preview": "vite preview", "typecheck": "tsc --noEmit", "migrate-to-kebab": "cd scripts && pnpm install && pnpm migrate", "list-files-to-rename": "cd scripts && pnpm install && pnpm list"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.8.1", "@tailwindcss/vite": "^4.1.6", "@tanstack/query-sync-storage-persister": "^5.75.7", "@tanstack/react-query": "^5.75.7", "@tanstack/react-query-persist-client": "^5.75.7", "@tanstack/react-table": "^8.21.3", "@turf/turf": "^7.2.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "hls.js": "^1.6.2", "immer": "^10.1.1", "input-otp": "^1.4.2", "keycloak-js": "^26.2.0", "lucide-react": "^0.509.0", "maplibre-gl": "^5.5.0", "meilisearch": "^0.50.0", "mitt": "^3.0.1", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.3", "react-map-gl": "^8.0.4", "react-redux": "^9.2.0", "react-router": "^7.6.0", "react-use": "^17.6.0", "recharts": "^2.15.4", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.6", "video.js": "^8.22.0", "vite-plugin-svgr": "^4.3.0", "xlsx": "^0.18.5", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/geojson": "^7946.0.16", "@types/node": "22.15.17", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "sass-embedded": "^1.87.0", "tw-animate-css": "^1.2.9", "typescript": "5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}