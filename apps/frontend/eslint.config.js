import js from '@eslint/js'
import query from '@tanstack/eslint-plugin-query'
import prettier from 'eslint-plugin-prettier'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import globals from 'globals'
import tseslint from 'typescript-eslint'


export default tseslint.config(
  { ignores: ['dist', 'node_modules'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'prettier': prettier,
      'query': query,
    },
    rules: {
      '@typescript-eslint/ban-ts-comment': ['error', { 'ts-ignore': true, 'ts-expect-error': false, 'ts-nocheck': false, 'ts-check': false }],
      ...reactHooks.configs.recommended.rules,
      'prettier/prettier': [
        'warn',
        {
          "singleQuote": true,
          "trailingComma": "all",
          "semi": true,
          "tabWidth": 2,
          "printWidth": 100,
          "jsxSingleQuote": false,
          "bracketSameLine": false
        }
      ],
      "no-empty-pattern": "off",
      '@typescript-eslint/no-unused-vars': [
        "off", {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
        }
      ],
      'react-refresh/only-export-components': 'off',
      // Prevent cross-workspace imports
      'no-restricted-imports': [
        'error',
        {
          patterns: [
            {
              group: ['@lrg/backend*', '@lrg/mobile*'],
              message:
                'Cross-workspace imports are not allowed. Frontend should not import from backend or mobile packages.',
            },
            {
              group: ['**/backend/src*', '**/mobile/src*'],
              message:
                'Cross-workspace imports via relative paths are not allowed. Frontend should not import from backend or mobile.',
            },
          ],
        },
      ],
    },
  },
)
