# ---- Build Stage ----
FROM node:20-alpine AS builder
WORKDIR /app

# Installer pnpm globalement
RUN npm install -g pnpm

# Copier uniquement les fichiers de dépendances pour optimiser le cache
COPY package.json pnpm-lock.yaml ./

# Installer les dépendances (production + dev pour build)
RUN pnpm install --frozen-lockfile

# Copier le reste du code source
COPY . .

# Build Vite (output dans dist/)
RUN pnpm build

# ---- Production Stage ----
FROM nginx:1.25-alpine AS production

# Copier le script d'entrypoint custom pour l'injection des variables d'env
COPY ./config/docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh


# Copier le build Vite dans le dossier nginx
COPY --from=builder /app/dist /usr/share/nginx/html


# (Optionnel) Copier une config nginx custom si besoin
# COPY nginx.conf /etc/nginx/nginx.conf

# Utiliser l'utilisateur nginx pour plus de sécurité
USER nginx

EXPOSE 80

ENTRYPOINT ["/docker-entrypoint.sh"]

CMD ["nginx", "-g", "daemon off;"] 