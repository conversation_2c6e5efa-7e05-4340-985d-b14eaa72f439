import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { IStopEntity } from './i-stop-entity';
import type { IFileEntity } from './i-file-entity';

/**
 * Delivery note entity interface
 * Corresponds to DeliveryNoteEntity in backend
 */
export interface IDeliveryNoteEntity extends IBaseDomainEntity {
  stop: IStopEntity;
  stopId: string;
  filename: string;
  file?: IFileEntity;
  fileId?: string;
  sequenceInStop: number;
}
