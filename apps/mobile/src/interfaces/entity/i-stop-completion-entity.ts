import { DeliveryCompletionType, DeliveryStatus } from '../enum/stop-completion.enum';
import { IFileEntity } from './i-file-entity';
import { IIncidentTypeEntity } from './i-incident-type-entity';
import { ILogisticsEquipmentCount } from './i-logistics-equipment-count';
import { ILogisticsEquipmentDetailsEntity } from './i-logistics-equipment-details-entity';

export interface IStopCompletion {
  deliveryStatus: DeliveryStatus;
  completedAt: Date | null;

  // Signature file
  signatureFileId: string | null;
  signatureFile: IFileEntity | null;
  signatureFirstName?: string;
  signatureLastName?: string;
  signatureEmail?: string;

  // Proof photos (multiple photos supported)
  proofPhotos: {
    id: string;
    stopId: string;
    file: IFileEntity;
    fileId: string;
    sequenceOrder: number;
    description?: string;
  }[];

  // Equipment return (ramassage d'agrès)
  returnedEquipmentCount?: ILogisticsEquipmentCount;

  // Unloaded equipment (déchargement d'agrès)
  unloadedEquipmentCount?: ILogisticsEquipmentCount;

  // Loaded equipment (chargement d'agrès)
  loadedEquipmentCount?: ILogisticsEquipmentCount;

  // Preloaded equipment (préchargement d'agrès)
  preloadedEquipmentCount?: ILogisticsEquipmentCount;

  // Equipment details
  equipmentDetails: ILogisticsEquipmentDetailsEntity[];

  // Incident handling
  incidentTypeId: string | null;
  incidentType: IIncidentTypeEntity | null;
  deliveryCompletionType: DeliveryCompletionType | null;

  // Comments
  comments: string | null;

  // Location data
  latitude: number | null;
  longitude: number | null;
  precision: number | null;
}
