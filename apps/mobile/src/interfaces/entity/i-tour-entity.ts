import { TourStatus } from '../enum/tour.enums';
import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { IImportEntity } from './i-import-entity';
import type { ILogisticsEquipmentCount } from './i-logistics-equipment-count';
import type { ILogisticsEquipmentDetailsEntity } from './i-logistics-equipment-details-entity';
import type { IStopEntity } from './i-stop-entity';
import type { ITourIdentifier } from './i-tour-identifier';

/**
 * Tour entity interface
 * Corresponds to TourEntity in backend
 */
export interface ITourEntity extends IBaseDomainEntity {
  tourIdentifier: ITourIdentifier;
  deliveryDate: string;
  status: TourStatus;
  providerFileName: string;
  stops: IStopEntity[];
  controlledEquipment: ILogisticsEquipmentDetailsEntity[];
  importBatch?: IImportEntity;
  importBatchId?: string;
  totalLoadedEquipmentCount?: ILogisticsEquipmentCount;
  totalPreloadedEquipmentCount?: ILogisticsEquipmentCount;
  totalReturnedEquipmentCount?: ILogisticsEquipmentCount;
  totalUnloadedEquipmentCount?: ILogisticsEquipmentCount;
  controlledEquipmentCount?: ILogisticsEquipmentCount;
}
