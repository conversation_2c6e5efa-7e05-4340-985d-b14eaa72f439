import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { IUserEntity } from './i-user-entity';

/**
 * File entity interface
 * Corresponds to FileEntity in backend
 */
export interface IFileEntity extends IBaseDomainEntity {
  s3fileKey: string;
  originalFilename: string;
  fileSize: string; // bigint serialized as string
  contentType?: string;
  uploadedBy?: IUserEntity;
  metadata?: Record<string, unknown>;
  isPublic: boolean;
  downloadUrl: string;
}
