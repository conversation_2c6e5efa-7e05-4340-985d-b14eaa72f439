import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { ILogisticsEquipmentTypeEntity } from './i-logistics-equipment-type-entity';
import type { IStopEntity } from './i-stop-entity';
import type { ITourEntity } from './i-tour-entity';

export enum LogisticsEquipmentOperation {
  RETURNED = 'RETURNED',
  UNLOADED = 'UNLOADED',
  LOADED = 'LOADED',
  PRELOADED = 'PRELOADED',
  CONTROLLED = 'CONTROLLED',
}

/**
 * Logistics equipment details entity interface
 * Corresponds to LogisticsEquipmentDetailsEntity in backend
 * Can be associated with either a stop or a tour
 */
export interface ILogisticsEquipmentDetailsEntity extends IBaseDomainEntity {
  stop?: IStopEntity;
  stopId?: string;
  tour?: ITourEntity;
  tourId?: string;
  logisticsEquipmentType?: ILogisticsEquipmentTypeEntity;
  logisticsEquipmentTypeId: string;
  quantity: number;
  operation: LogisticsEquipmentOperation;
}
