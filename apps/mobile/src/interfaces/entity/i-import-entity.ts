import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { ImportStatus } from '../enum/import-status.enum';
import type { ITourEntity } from './i-tour-entity';

/**
 * Import entity interface
 * Corresponds to ImportEntity in backend
 */
export interface IImportEntity extends IBaseDomainEntity {
  status: ImportStatus;
  successfulRecords: number;
  failedRecords: number;
  skippedRecords: number;
  errors?: Array<{
    fileName: string;
    fileContent: string;
    message: string;
    details?: Record<string, unknown>;
  }>;
  metadata?: Record<string, unknown>;
  tours?: ITourEntity[];
  importDate: string;
  importedFiles: Array<{ fileName: string; fileContent: string }>;
}
