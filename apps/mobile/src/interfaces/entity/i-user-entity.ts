import { UserRole } from '../enum/user-role.enum';
import type { IBaseDomainEntity } from './i-base-domain-entity';
import type { IFileEntity } from './i-file-entity';
import type { ITourAssignmentEntity } from './i-tour-assignment-entity';

/**
 * User entity interface
 * Corresponds to UserEntity in backend
 */
export interface IUserEntity extends IBaseDomainEntity {
  username: string;
  locale: string | null;
  email: string | null;
  firstName: string | null;
  lastName: string | null;
  color: string | null;
  roles?: UserRole[];
  files: IFileEntity[];
  tourAssignments: ITourAssignmentEntity[];
}
