export enum DeliveryCompletionType {
  FULL = 'FULL',
  PARTIAL = 'PARTIAL',
  NONE = 'NONE',
}

export interface IEquipmentCountDto {
  palletCount?: number;
  rollCount?: number;
  packageCount?: number;
}

export interface ILogisticsEquipmentDetailDto {
  logisticsEquipmentTypeId: string;
  quantity: number;
}

export interface ICompleteStopDeliveryDto {
  signatureFile?: {
    base64: string;
    filename: string;
    mimeType: string;
    /** A temporary file path to the file on the device, because   of shared_prefs storage limit*/
    systemFilePath?: string;
  };
  signatureFirstName?: string;
  signatureLastName?: string;
  signatureEmail?: string;
  photoFiles?: {
    base64: string;
    filename: string;
    mimeType: string;
    /** A temporary file path to the file on the device, because   of shared_prefs storage limit*/
    systemFilePath?: string;
  }[];
  incidentTypeId?: string;
  comments?: string;
  deliveryCompletionType?: DeliveryCompletionType;
  isSecureLocation?: boolean;
  returnedEquipment?: IEquipmentCountDto;
  unloadedEquipment?: IEquipmentCountDto;
  returnedEquipmentDetails?: ILogisticsEquipmentDetailDto[];
  unloadedEquipmentDetails?: ILogisticsEquipmentDetailDto[];
  // Location data
  latitude?: number;
  longitude?: number;
  precision?: number;
}
