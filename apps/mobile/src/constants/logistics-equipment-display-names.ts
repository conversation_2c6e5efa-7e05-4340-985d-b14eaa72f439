import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';

/**
 * Map des types d'équipements logistiques vers leurs noms d'affichage en français
 */
export const LogisticsEquipmentKindToDisplayName: Record<LogisticsEquipmentKind, string> = {
  [LogisticsEquipmentKind.PALLET]: 'Palettes',
  [LogisticsEquipmentKind.ROLL]: 'Rolls',
  [LogisticsEquipmentKind.PACKAGE]: 'Caisses',
} as const;

/**
 * Obtient le nom d'affichage pour un type d'équipement logistique
 * @param kind - Le type d'équipement
 * @returns Le nom d'affichage en français
 */
export const getLogisticsEquipmentDisplayName = (kind: LogisticsEquipmentKind): string => {
  return LogisticsEquipmentKindToDisplayName[kind] || kind;
};
