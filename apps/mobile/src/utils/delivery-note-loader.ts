import { FileOpener } from '@capacitor-community/file-opener';
import { Directory, Filesystem } from '@capacitor/filesystem';
import { IDeliveryNoteEntity } from '../interfaces/entity/i-delivery-note-entity';
import { tourDeliverService } from '../services/TourDeliverService';

interface DeliveryNoteLoaderOptions {
  openAfterDownload?: boolean;
  forceRefresh?: boolean;
}

interface DeliveryNoteLoaderResult {
  success: boolean;
  localPath?: string;
  error?: string;
}

/**
 * Convert base64 string to ArrayBuffer
 * @param base64 - Base64 encoded string
 * @returns ArrayBuffer
 */
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  return bytes.buffer;
}

/**
 * Smart utility to load a delivery note from local storage or download it if needed
 * @param tourId - The tour ID
 * @param deliveryNote - The delivery note entity
 * @param options - Optional configuration
 * @returns Promise with the result
 */
export async function loadDeliveryNote(
  tourId: string,
  deliveryNote: IDeliveryNoteEntity,
  options: DeliveryNoteLoaderOptions = {},
): Promise<DeliveryNoteLoaderResult> {
  const { openAfterDownload = false, forceRefresh = false } = options;

  try {
    // Generate the expected filename
    const filename = `${deliveryNote.filename}`;
    const localPath = `deliveryNotes/${new Date().toISOString().split('T')[0]}/${filename}`;

    // Check if file exists locally (unless force refresh)
    if (!forceRefresh) {
      try {
        await Filesystem.stat({
          path: localPath,
          directory: Directory.Cache,
        });

        console.log('[DeliveryNoteLoader] File found locally:', localPath);

        // Open the file if requested
        if (openAfterDownload) {
          await openDeliveryNoteFile(localPath);
        }

        return { success: true, localPath };
      } catch {
        // File doesn't exist locally, continue to download
        console.log('[DeliveryNoteLoader] File not found locally, will download:', filename);
      }
    }

    // Download the file
    console.log('[DeliveryNoteLoader] Downloading file:', filename);
    const blob = await tourDeliverService.downloadDeliveryNote(tourId, deliveryNote.id);

    // Convert blob to base64
    const reader = new FileReader();
    const base64Data = await new Promise<string>((resolve, reject) => {
      reader.onloadend = () => {
        const base64 = reader.result as string;
        resolve(base64.split(',')[1]); // Remove data:application/pdf;base64, prefix
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });

    // Ensure directory exists
    const directory = localPath.substring(0, localPath.lastIndexOf('/'));
    try {
      await Filesystem.mkdir({
        path: directory,
        directory: Directory.Cache,
        recursive: true,
      });
    } catch {
      // Directory might already exist, ignore error
    }

    // Save the file
    await Filesystem.writeFile({
      path: localPath,
      data: base64Data,
      directory: Directory.Cache,
    });

    console.log('[DeliveryNoteLoader] File saved successfully:', localPath);

    // Open the file if requested
    if (openAfterDownload) {
      await openDeliveryNoteFile(localPath);
    }

    return { success: true, localPath };
  } catch (error) {
    console.error('[DeliveryNoteLoader] Error loading delivery note:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Check if it's a 404 error
    if (errorMessage.includes('404') || errorMessage.toLowerCase().includes('not found')) {
      return {
        success: false,
        error: 'Delivery note not yet available. It may still be processing.',
      };
    }

    return {
      success: false,
      error: `Failed to load delivery note: ${errorMessage}`,
    };
  }
}

/**
 * Open a delivery note file using the native file opener
 * @param localPath - The local path to the file
 */
async function openDeliveryNoteFile(localPath: string): Promise<void> {
  try {
    const fileUri = await Filesystem.getUri({
      path: localPath,
      directory: Directory.Cache,
    });

    await FileOpener.open({
      filePath: fileUri.uri,
      contentType: 'application/pdf',
    }).catch(async (fileOpenerError) => {
      // If fileOpener fails use web browser download
      console.warn(
        '[DeliveryNoteLoader] FileOpener failed, falling back to browser download:',
        fileOpenerError,
      );

      // Read the file content
      const fileContent = await Filesystem.readFile({
        path: localPath,
        directory: Directory.Cache,
      });

      // Create a blob and download through browser
      const blob = new Blob([base64ToArrayBuffer(fileContent.data as string)], {
        type: 'application/pdf',
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = localPath.split('/').pop() || 'delivery-note.pdf';
      link.click();

      // Cleanup
      setTimeout(() => URL.revokeObjectURL(url), 100);
    });
  } catch (error) {
    console.error('[DeliveryNoteLoader] Error opening file:', error);
    throw new Error('Failed to open PDF file');
  }
}

/**
 * Check if a delivery note is available locally
 * @param deliveryNote - The delivery note entity
 * @returns Promise<boolean> - true if file exists locally
 */
export async function isDeliveryNoteAvailableLocally(
  deliveryNote: IDeliveryNoteEntity,
): Promise<boolean> {
  try {
    const filename = `${deliveryNote.filename}`;
    const localPath = `deliveryNotes/${new Date().toISOString().split('T')[0]}/${filename}`;

    await Filesystem.stat({
      path: localPath,
      directory: Directory.Cache,
    });

    return true;
  } catch {
    return false;
  }
}

/**
 * Get the local path for a delivery note
 * @param deliveryNote - The delivery note entity
 * @returns The expected local path
 */
export function getDeliveryNoteLocalPath(deliveryNote: IDeliveryNoteEntity): string {
  const filename = `${deliveryNote.filename}`;

  return `deliveryNotes/${new Date().toISOString().split('T')[0]}/${filename}`;
}

/**
 * Simple function to get delivery note content (base64) from local storage or download it
 * @param tourId - The tour ID
 * @param deliveryNote - The delivery note entity
 * @returns Promise<string> - Base64 encoded PDF content
 */
export async function getDeliveryNoteContent(
  tourId: string,
  deliveryNote: IDeliveryNoteEntity,
): Promise<string> {
  const result = await loadDeliveryNote(tourId, deliveryNote, {
    openAfterDownload: false,
    forceRefresh: false,
  });

  if (!result.success || !result.localPath) {
    throw new Error(result.error || 'Failed to load delivery note');
  }

  // Read the file content
  const file = await Filesystem.readFile({
    path: result.localPath,
    directory: Directory.Cache,
  });

  return file.data as string;
}
