import { EquipmentValidationRecord } from '../components/services/deliverer/EquipmentValidator';
import { IStopEntity } from '../interfaces/entity/i-stop-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';

/**
 * Extrait les données d'équipement depuis les shipmentLines d'un stop
 */
export const extractEquipmentFromStop = (stop: IStopEntity): EquipmentValidationRecord => {
  // Initialiser toutes les catégories de base à 0
  const equipmentRecord: EquipmentValidationRecord = {
    [LogisticsEquipmentKind.PALLET]: {
      initialQuantity: 0,
      adjustedQuantity: 0,
      isValidated: false,
    },
    [LogisticsEquipmentKind.ROLL]: {
      initialQuantity: 0,
      adjustedQuantity: 0,
      isValidated: false,
    },
    [LogisticsEquipmentKind.PACKAGE]: {
      initialQuantity: 0,
      adjustedQuantity: 0,
      isValidated: false,
    },
  };

  // Agréger les quantités par type d'équipement depuis les shipmentLines filtrées
  stop.shipmentLines?.forEach((shipmentLine) => {
    if (shipmentLine.palletCount) {
      equipmentRecord[LogisticsEquipmentKind.PALLET].initialQuantity += shipmentLine.palletCount;
      equipmentRecord[LogisticsEquipmentKind.PALLET].adjustedQuantity += shipmentLine.palletCount;
    }

    if (shipmentLine.rollCount) {
      equipmentRecord[LogisticsEquipmentKind.ROLL].initialQuantity += shipmentLine.rollCount;
      equipmentRecord[LogisticsEquipmentKind.ROLL].adjustedQuantity += shipmentLine.rollCount;
    }

    if (shipmentLine.packageCount) {
      equipmentRecord[LogisticsEquipmentKind.PACKAGE].initialQuantity += shipmentLine.packageCount;
      equipmentRecord[LogisticsEquipmentKind.PACKAGE].adjustedQuantity += shipmentLine.packageCount;
    }
  });

  return equipmentRecord;
};

export const extractEquipmentFromCompletedStop = (stop: IStopEntity): EquipmentValidationRecord => {
  // Initialiser toutes les catégories de base à 0
  const equipmentRecord: EquipmentValidationRecord = {
    [LogisticsEquipmentKind.PALLET]: {
      initialQuantity: 0,
      adjustedQuantity: 0,
      isValidated: false,
    },
    [LogisticsEquipmentKind.ROLL]: {
      initialQuantity: 0,
      adjustedQuantity: 0,
      isValidated: false,
    },
    [LogisticsEquipmentKind.PACKAGE]: {
      initialQuantity: 0,
      adjustedQuantity: 0,
      isValidated: false,
    },
  };

  // Agréger les quantités par type d'équipement depuis les shipmentLines filtrées
  if (stop?.completion?.returnedEquipmentCount && stop.completion.returnedEquipmentCount) {
    if (stop.completion.returnedEquipmentCount.palletCount) {
      equipmentRecord[LogisticsEquipmentKind.PALLET].initialQuantity +=
        stop.completion.returnedEquipmentCount.palletCount;
      equipmentRecord[LogisticsEquipmentKind.PALLET].adjustedQuantity +=
        stop.completion.returnedEquipmentCount.palletCount;
    }

    if (stop.completion.returnedEquipmentCount.rollCount) {
      equipmentRecord[LogisticsEquipmentKind.ROLL].initialQuantity +=
        stop.completion.returnedEquipmentCount.rollCount;
      equipmentRecord[LogisticsEquipmentKind.ROLL].adjustedQuantity +=
        stop.completion.returnedEquipmentCount.rollCount;
    }

    if (stop.completion.returnedEquipmentCount.packageCount) {
      equipmentRecord[LogisticsEquipmentKind.PACKAGE].initialQuantity +=
        stop.completion.returnedEquipmentCount.packageCount;
      equipmentRecord[LogisticsEquipmentKind.PACKAGE].adjustedQuantity +=
        stop.completion.returnedEquipmentCount.packageCount;
    }
  }

  return equipmentRecord;
};
