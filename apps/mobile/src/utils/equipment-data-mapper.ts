import {
  EquipmentQuantityData,
  ReturnedEquipmentData,
  UnloadedEquipmentData,
} from '../components/services/deliverer/delivery-process/types';
import { ILogisticsEquipmentTypeEntity } from '../interfaces/entity/i-logistics-equipment-type-entity';

/**
 * Convertit les données d'équipement déchargé (UnloadedEquipmentData)
 * en format EquipmentQuantityData pour EquipmentQuantitySelector
 */
export const convertUnloadedToEquipmentQuantities = (
  unloadedData: UnloadedEquipmentData,
  equipmentTypes: ILogisticsEquipmentTypeEntity[],
): Record<string, EquipmentQuantityData> => {
  const quantities: Record<string, EquipmentQuantityData> = {};

  // Créer un map des détails par type ID
  const detailsMap = new Map();
  unloadedData.unloadedEquipmentDetails.forEach((detail) => {
    detailsMap.set(detail.logisticsEquipmentTypeId, detail.quantity);
  });

  // Mapper chaque type d'équipement
  equipmentTypes.forEach((type) => {
    const quantity = detailsMap.get(type.id) || 0;

    quantities[type.id] = {
      equipmentType: type,
      defaultQuantity: 0,
      currentQuantity: quantity,
    };
  });

  return quantities;
};

/**
 * Convertit les données d'équipement retourné (ReturnedEquipmentData)
 * en format EquipmentQuantityData pour EquipmentQuantitySelector
 */
export const convertReturnedToEquipmentQuantities = (
  returnedData: ReturnedEquipmentData,
  equipmentTypes: ILogisticsEquipmentTypeEntity[],
): Record<string, EquipmentQuantityData> => {
  const quantities: Record<string, EquipmentQuantityData> = {};

  // Créer un map des détails par type ID
  const detailsMap = new Map();
  returnedData.returnedEquipmentDetails.forEach((detail) => {
    detailsMap.set(detail.logisticsEquipmentTypeId, detail.quantity);
  });

  // Mapper chaque type d'équipement
  equipmentTypes.forEach((type) => {
    const quantity = detailsMap.get(type.id) || 0;

    quantities[type.id] = {
      equipmentType: type,
      defaultQuantity: 0,
      currentQuantity: quantity,
    };
  });

  return quantities;
};

/**
 * Créer un état de validation initiale basé sur les quantités existantes
 * Tous les équipements avec quantité > 0 sont considérés comme validés
 */
export const createInitialValidationState = (
  quantities: Record<string, EquipmentQuantityData>,
): Record<string, boolean> => {
  const validatedState: Record<string, boolean> = {};

  Object.entries(quantities).forEach(([typeId, quantityData]) => {
    validatedState[typeId] = quantityData.currentQuantity > 0;
  });

  return validatedState;
};
