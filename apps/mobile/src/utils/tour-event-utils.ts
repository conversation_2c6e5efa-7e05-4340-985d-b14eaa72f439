import { cloneDeep } from 'lodash';
import { ILoadStopDto } from '../interfaces/dto/load-stop.dto';
import { ICompleteStopDeliveryDto } from '../interfaces/dto/stop-delivery.dto';
import { IStopCompletion } from '../interfaces/entity/i-stop-completion-entity';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { DeliveryCompletionType, DeliveryStatus } from '../interfaces/enum/stop-completion.enum';
import { TourStatus } from '../interfaces/enum/tour.enums';
import { QueuedEventInterface } from '../stores/eventQueueSlice';

/**
 * Applique un événement à un stop d'une tournée
 * @param tour - La tournée à modifier (sera clonée)
 * @param event - L'événement à appliquer
 * @param dataResolvedAt - La date de résolution des données pour filtrer les événements
 * @returns Une nouvelle instance de la tournée avec l'événement appliqué
 */
export function applyEventToTour(
  tour: ITourEntity,
  event: QueuedEventInterface,
  dataResolvedAt: string | null,
): ITourEntity {
  // Clone profond pour éviter les mutations
  const updatedTour = cloneDeep(tour);

  // Si pas de date de résolution ou si l'événement est antérieur, on ignore
  if (!dataResolvedAt || event.createdAt < dataResolvedAt) {
    return updatedTour;
  }

  // Trouver le stop concerné par l'événement
  const stopId = (event.payload as any)?.stopId;

  if (!stopId || !updatedTour.stops) {
    return updatedTour;
  }

  const stopIndex = updatedTour.stops.findIndex((stop) => stop.id === stopId);

  if (stopIndex === -1) {
    return updatedTour;
  }

  const stop = updatedTour.stops[stopIndex];

  // Appliquer l'événement selon son type
  switch (event.eventType) {
    case 'stop-completion': {
      const payload = event.payload as ICompleteStopDeliveryDto;

      if (!stop.completion) {
        stop.completion = {} as IStopCompletion;
      }

      stop.completion.deliveryStatus =
        payload.deliveryCompletionType !== DeliveryCompletionType.NONE
          ? DeliveryStatus.COMPLETED
          : DeliveryStatus.FAILED;

      break;
    }

    case 'stop-load':
    case 'stop-preload': {
      const payload = event.payload as ILoadStopDto;

      if (!stop.completion) {
        stop.completion = {
          deliveryStatus: DeliveryStatus.PENDING,
        } as IStopCompletion;
      }

      if (event.eventType === 'stop-load') {
        stop.completion.loadedEquipmentCount = payload.equipmentCount;
        updatedTour.status = TourStatus.InProgress;
      } else {
        stop.completion.preloadedEquipmentCount = payload.equipmentCount;
      }
      break;
    }
  }

  return updatedTour;
}

/**
 * Applique plusieurs événements à une tournée
 * @param tour - La tournée à modifier
 * @param events - Les événements à appliquer
 * @param dataResolvedAt - La date de résolution des données
 * @returns Une nouvelle instance de la tournée avec tous les événements appliqués
 */
export function applyEventsToTour(
  tour: ITourEntity,
  events: QueuedEventInterface[],
  dataResolvedAt: string | null,
): ITourEntity {
  return events.reduce((accTour, event) => applyEventToTour(accTour, event, dataResolvedAt), tour);
}
