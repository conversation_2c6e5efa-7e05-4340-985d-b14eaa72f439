export class Logger {
  private serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  log(...args: any[]) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}][${this.serviceName}]`, ...args);
  }

  info(...args: any[]) {
    this.log(...args);
  }

  warn(...args: any[]) {
    const timestamp = new Date().toISOString();
    console.warn(`[${timestamp}][${this.serviceName}]`, ...args);
  }

  error(...args: any[]) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}][${this.serviceName}]`, ...args);
  }
}
