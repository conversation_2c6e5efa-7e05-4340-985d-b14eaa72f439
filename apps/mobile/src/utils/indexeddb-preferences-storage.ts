export const indexedDBPreferencesStorage = {
  _db: null as IDBDatabase | null,
  _dbName: 'appStorage',
  _storeName: 'storage',

  _initDB: async function (): Promise<IDBDatabase> {
    if (this._db) {
      return this._db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this._dbName, 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this._db = request.result;
        resolve(this._db);
      };

      request.onupgradeneeded = () => {
        const db = request.result;

        if (!db.objectStoreNames.contains(this._storeName)) {
          db.createObjectStore(this._storeName, { keyPath: 'key' });
        }
      };
    });
  },

  getItem: async function (name: string): Promise<string | null> {
    try {
      const db = await this._initDB();

      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this._storeName], 'readonly');
        const store = transaction.objectStore(this._storeName);
        const request = store.get(name);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          const result = request.result;
          resolve(result ? result.value : null);
        };
      });
    } catch (error) {
      console.error('Error getting item from indexedDB:', error);

      return null;
    }
  },

  setItem: async function (name: string, value: string): Promise<void> {
    const db = await this._initDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this._storeName], 'readwrite');
      const store = transaction.objectStore(this._storeName);
      const request = store.put({ key: name, value });

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  },

  removeItem: async function (name: string): Promise<void> {
    try {
      const db = await this._initDB();

      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this._storeName], 'readwrite');
        const store = transaction.objectStore(this._storeName);
        const request = store.delete(name);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('Error removing item from indexedDB:', error);
    }
  },
};
