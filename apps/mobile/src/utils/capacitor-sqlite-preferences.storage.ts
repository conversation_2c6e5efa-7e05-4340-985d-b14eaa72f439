import { CapacitorSQLite, SQLiteConnection, SQLiteDBConnection } from '@capacitor-community/sqlite';

export const capacitorSQLitePreferencesStorage = {
  _db: null as SQLiteDBConnection | null,
  _dbName: 'appStorage',

  _initDB: async function (): Promise<SQLiteDBConnection> {
    if (this._db) {
      return this._db;
    }

    const sqlite = new SQLiteConnection(CapacitorSQLite);

    // <PERSON><PERSON>er ou ouvrir la base
    this._db = await sqlite.createConnection(this._dbName, false, 'no-encryption', 1, false);

    await this._db.open();

    // Créer la table si elle n'existe pas
    await this._db.execute(`
      CREATE TABLE IF NOT EXISTS storage (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    `);

    return this._db;
  },

  getItem: async function (name: string): Promise<string | null> {
    try {
      const db = await this._initDB();
      const result = await db.query('SELECT value FROM storage WHERE key = ?', [name]);

      return result.values && result.values.length > 0 ? result.values[0].value : null;
    } catch (error) {
      console.error('Error getting item from SQLite:', error);

      return null;
    }
  },

  setItem: async function (name: string, value: string): Promise<void> {
    const db = await this._initDB();
    await db.run('INSERT OR REPLACE INTO storage (key, value) VALUES (?, ?)', [name, value]);
  },

  removeItem: async function (name: string): Promise<void> {
    try {
      const db = await this._initDB();
      await db.run('DELETE FROM storage WHERE key = ?', [name]);
    } catch (error) {
      console.error('Error removing item from SQLite:', error);
    }
  },
};
