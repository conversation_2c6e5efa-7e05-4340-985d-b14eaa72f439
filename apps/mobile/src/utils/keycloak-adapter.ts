import { App, URLOpenListenerEvent } from '@capacitor/app';
import { <PERSON>rows<PERSON> } from '@capacitor/browser';
import Keycloak, {
  KeycloakAdapter,
  KeycloakLoginOptions,
  KeycloakLogoutOptions,
  KeycloakPromiseWrapper,
  KeycloakRegisterOptions,
} from 'keycloak-js';

// Placeholder for platform utilities - replace with your actual implementation
const isAndroid = (): boolean => {
  // Example implementation, adjust as needed
  return (window as any)?.Capacitor?.getPlatform() === 'android';
};

export class CapacitorAdapter implements KeycloakAdapter {
  constructor(private keycloak: Keycloak) {}

  async login(options?: KeycloakLoginOptions): Promise<void> {
    console.log('login', options);
    const loginUrl = await this.keycloak.createLoginUrl(options);

    return this.openAndWaitForCallback(loginUrl, ({ url }, promise) => {
      const oauth = this.keycloak.parseCallback(url);
      this.keycloak.processCallback(oauth, promise);
    });
  }

  async logout(options?: KeycloakLogoutOptions): Promise<void> {
    const logoutUrl = this.keycloak.createLogoutUrl(options);

    return this.openAndWaitForCallback(logoutUrl, (_, promise) => {
      this.keycloak.clearToken();
      // wait for in-app browser to be completely closed before continuing
      // Consider if a more robust browser close detection is needed
      setTimeout(() => promise.setSuccess(), 500);
    });
  }

  async register(options?: KeycloakRegisterOptions): Promise<void> {
    const registerUrl = await this.keycloak.createRegisterUrl(options);

    return this.openAndWaitForCallback(registerUrl, ({ url }, promise) => {
      const oauth = this.keycloak.parseCallback(url);
      this.keycloak.processCallback(oauth, promise);
    });
  }

  accountManagement(): Promise<void> {
    const promise = this.keycloak.createPromise<void, void>();
    const accountUrl = this.keycloak.createAccountUrl();

    if (typeof accountUrl === 'undefined') {
      // Check if undefined, not !undefined
      // Keycloak core library throws error here, maybe re-throw or handle differently
      console.error('Account management not supported by the OIDC server.');
      promise.setError(new Error('Account management not supported by the OIDC server.') as any);

      return promise.promise;
    }

    Browser.open({ url: accountUrl })
      .then(() => {
        // Opening the browser doesn't guarantee success, it just opens the URL.
        // The Keycloak core library doesn't resolve the promise here.
        // Maybe leave unresolved or find a way to detect success?
        // For now, aligning with core library behavior (no explicit success call).
      })
      .catch((err) => {
        promise.setError(err);
      });

    // The core library returns an unresolved promise here.
    return promise.promise;
  }

  redirectUri(options: { redirectUri: string }, _encodeHash = true): string {
    // Encode hash parameter is not used in Keycloak's core logic for this function
    if (options && options.redirectUri) {
      return options.redirectUri;
    } else if (this.keycloak.redirectUri) {
      return this.keycloak.redirectUri;
    } else {
      // Default matches the core library
      return 'http://localhost';
    }
  }

  private openAndWaitForCallback(
    url: string,
    callback: (event: URLOpenListenerEvent, promise: KeycloakPromiseWrapper<void, void>) => void,
  ): Promise<void> {
    console.log('openAndWaitForCallback', url);
    const promise = this.keycloak.createPromise<void, void>();

    // No need for the async IIFE wrapper if the outer function is async
    // But since the outer functions *return* this promise, we keep the IIFE
    (async () => {
      try {
        const appListener = await App.addListener('appUrlOpen', async (event) => {
          try {
            if (!isAndroid()) {
              // Ensure Browser.close exists and handle potential errors
              if (Browser && Browser.close) {
                await Browser.close();
              } else {
                console.warn('Browser.close() not available on this platform.');
              }
            }
            // Ensure listener is removed even if callback throws
            await appListener.remove();
            callback(event, promise);
          } catch (listenerError) {
            console.error('Error in appUrlOpen listener:', listenerError);
            promise.setError(listenerError as any);
            // Attempt to remove listener again in case of error during callback
            try {
              await appListener.remove();
            } catch {
              /* ignore */
            }
          }
        });

        // Adding a delay might be brittle; consider if Browser.open can be awaited directly
        // or if a different mechanism is needed to ensure the listener is ready.
        setTimeout(async () => {
          try {
            await Browser.open({ url });
          } catch (openError) {
            console.error('Error opening browser:', openError);
            promise.setError(openError as any);
            // Clean up listener if browser fails to open
            await appListener.remove();
          }
        }, 300);
      } catch (addListenerError) {
        console.error('Error adding App listener:', addListenerError);
        promise.setError(addListenerError as any);
      }
    })();

    return promise.promise;
  }
}
