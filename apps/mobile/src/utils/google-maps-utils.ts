import { Browser } from '@capacitor/browser';
import { Capacitor } from '@capacitor/core';
import { Geolocation } from '@capacitor/geolocation';

export interface DirectionsOptions {
  destination: string;
  useCurrentLocation?: boolean;
  travelMode?: 'driving' | 'walking' | 'bicycling' | 'transit';
}

/**
 * Ouvre Google Maps avec un itinéraire vers la destination spécifiée
 * @param options Options pour l'itinéraire
 */
export const openGoogleMapsDirections = async (options: DirectionsOptions): Promise<void> => {
  const { destination, useCurrentLocation = true, travelMode = 'driving' } = options;

  try {
    let url: string;

    if (useCurrentLocation) {
      try {
        // Essayer d'obtenir la position actuelle
        const position = await Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 60000, // Cache pendant 1 minute
        });

        const { latitude, longitude } = position.coords;
        url = `https://www.google.com/maps/dir/${latitude},${longitude}/${encodeURIComponent(destination)}?travelmode=${travelMode}`;
      } catch (geolocationError) {
        console.warn(
          "Impossible d'obtenir la géolocalisation, utilisation du fallback:",
          geolocationError,
        );
        // Fallback sans position actuelle
        url = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(destination)}&travelmode=${travelMode}`;
      }
    } else {
      // Sans géolocalisation
      url = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(destination)}&travelmode=${travelMode}`;
    }

    // Ouvrir selon la plateforme
    if (Capacitor.isNativePlatform()) {
      // Sur mobile, ouvre l'app Google Maps ou le navigateur
      await Browser.open({
        url,
        presentationStyle: 'popover',
        toolbarColor: '#1976d2',
      });
    } else {
      // Sur web, ouvre dans un nouvel onglet
      window.open(url, '_blank');
    }
  } catch (error) {
    console.error("Erreur lors de l'ouverture de Google Maps:", error);
    throw new Error("Impossible d'ouvrir l'itinéraire Google Maps");
  }
};

/**
 * Formate une adresse depuis un objet adresse
 * @param address Objet adresse
 * @returns Adresse formatée
 */
export const formatAddressForMaps = (address: {
  street?: string;
  city?: string;
  postalCode?: string;
  country?: string;
}): string => {
  const parts = [address.street, address.postalCode, address.city, address.country].filter(Boolean);

  return parts.join(', ');
};

/**
 * Ouvre Google Maps avec une adresse formatée
 * @param address Objet adresse
 * @param options Options supplémentaires
 */
export const openDirectionsToAddress = async (
  address: {
    street?: string;
    city?: string;
    postalCode?: string;
    country?: string;
  },
  options: Omit<DirectionsOptions, 'destination'> = {},
): Promise<void> => {
  const formattedAddress = formatAddressForMaps(address);

  console.log('formattedAddress', formattedAddress);

  if (!formattedAddress.trim()) {
    throw new Error('Adresse invalide ou vide');
  }

  await openGoogleMapsDirections({
    destination: formattedAddress,
    ...options,
  });
};
