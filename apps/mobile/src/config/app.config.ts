/**
 * Configuration
 */

// Variables d'environnement
const ENV = {
  API_URL: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  APP_NAME: import.meta.env.VITE_APP_NAME || 'In2Delivery',
  APP_FRONTEND_IP: import.meta.env.VITE_FRONTEND_IP || '************',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  NODE_ENV: import.meta.env.MODE || 'development',
  DEBUG: import.meta.env.VITE_DEBUG === 'true' || false,
  KEYCLOAK_URL: import.meta.env.VITE_KEYCLOAK_URL as string,
  KEYCLOAK_REALM: import.meta.env.VITE_KEYCLOAK_REALM as string,
  KEYCLOAK_CLIENT_ID: import.meta.env.VITE_KEYCLOAK_CLIENT_ID as string,
  APP_IDENTIFIER: 'fr.in2delivery.app',
};

// Clés de stockage local
const STORAGE_KEYS = {
  USER_INFO: 'user_info',
  THEME: 'app_theme',
  LANGUAGE: 'app_language',
};

// Configuration des thèmes
const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
};

// Configuration des langues
const LANGUAGES = {
  FR: 'fr',
  EN: 'en',
  DEFAULT: 'fr',
};

// Durée en millisecondes
const DURATIONS = {
  TOAST_SHORT: 2000,
  TOAST_MEDIUM: 3500,
  TOAST_LONG: 5000,
  TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24 heures
};

// Paramètres de pagination
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
};

// Paramètres de tri
const SORT = {
  ORDER: {
    ASC: 'asc',
    DESC: 'desc',
  },
  FIELDS: {
    CREATED_AT: 'createdAt',
    UPDATED_AT: 'updatedAt',
    TITLE: 'title',
    NAME: 'name',
    SIZE: 'size',
    STATUS: 'status',
  },
  DEFAULT_FIELD: 'createdAt',
  DEFAULT_ORDER: 'desc',
};

export const APP_CONFIG = {
  ENV,
  STORAGE_KEYS,
  THEMES,
  LANGUAGES,
  DURATIONS,
  PAGINATION,
  SORT,
};

export default APP_CONFIG;
