import { Directory, Filesystem } from '@capacitor/filesystem';
import { DateTime } from 'luxon';

export class OfflineStorageService {
  directory = Directory.Cache;
  private isBaseDirectoryCreated = false;

  constructor(private readonly basePath: string) {
    this.basePath = `${this.basePath}/${DateTime.now().toISODate()}`;
  }

  private async ensureDirectoryExists() {
    if (this.isBaseDirectoryCreated) {
      return;
    }

    try {
      await Filesystem.mkdir({
        path: this.basePath,
        directory: this.directory,
        recursive: true,
      });
    } catch (error) {
      console.error('Error creating directory:', error);
    } finally {
      this.isBaseDirectoryCreated = true;
    }
  }

  async writeFile(fileName: string, data: string) {
    await this.ensureDirectoryExists();

    const filePath = `${this.basePath}/${fileName}`;
    await Filesystem.writeFile({
      path: filePath,
      directory: this.directory,
      data,
    });
  }

  async doesFileExist(fileName: string) {
    try {
      await this.ensureDirectoryExists();
      const filePath = `${this.basePath}/${fileName}`;

      const file = await Filesystem.stat({
        path: filePath,
        directory: this.directory,
      });

      return file.size > 0;
    } catch {
      return false;
    }
  }

  async getFileContent(fileName: string): Promise<string> {
    const filePath = `${this.basePath}/${fileName}`;

    const file = await Filesystem.readFile({
      path: filePath,
      directory: this.directory,
    });

    if (typeof file.data === 'string') {
      return file.data;
    }

    return file.data.toString();
  }
}

export const deliveryNotesStorageService = new OfflineStorageService('deliveryNotes');
