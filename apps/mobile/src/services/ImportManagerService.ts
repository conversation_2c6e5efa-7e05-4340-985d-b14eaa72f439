import { IImportEntity } from '../interfaces/entity/i-import-entity';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { apiService } from './ApiService';

export class ImportManagerService {
  private readonly endpoint = '/api/manager/imports';

  /**
   * Récupère le statut d'un import par son ID
   */
  async getImportStatus(id: string): Promise<IImportEntity> {
    return apiService.get<IImportEntity>(`${this.endpoint}/${id}`);
  }

  /**
   * Liste paginée des imports
   */
  async getImportList(params?: IPaginationParams): Promise<IPaginatedResponse<IImportEntity>> {
    return apiService.getPaginated<IImportEntity>(this.endpoint, params);
  }

  /**
   * Déclenche un import SFTP pour une date donnée
   */
  async triggerSftpImport(date: string): Promise<IImportEntity> {
    return apiService.post<IImportEntity>(`${this.endpoint}/sftp`, { date });
  }

  /**
   * Récupère les détails complets d'un import incluant les fichiers importés
   */
  async getImportDetails(id: string): Promise<IImportEntity> {
    return apiService.get<IImportEntity>(`${this.endpoint}/${id}/details`);
  }
}

export const importManagerService = new ImportManagerService();
