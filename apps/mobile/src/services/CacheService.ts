import Dexie, { Table } from 'dexie';

interface CacheEntry {
  key: string;
  value: string;
  createdAt: Date;
  ttl?: number;
  expiresAt?: Date;
}

export class CacheDB extends Dexie {
  cache!: Table<CacheEntry, string>;

  constructor() {
    super('CacheDB');
    this.version(1).stores({
      cache: 'key, expiresAt',
    });
  }
}

export class CacheService {
  private db: CacheDB;

  constructor() {
    this.db = new CacheDB();
    this.cleanupExpiredEntries();
  }

  private async cleanupExpiredEntries() {
    await new Promise((resolve) => setTimeout(resolve, 5000));
    console.log('[CacheService] cleanupExpiredEntries');

    const entries = await this.db.cache.toArray();
    const now = new Date();
    const expiredEntries = entries.filter((entry) => entry.expiresAt && entry.expiresAt < now);

    console.log('[CacheService] expiredEntries', expiredEntries.length);

    for (const entry of expiredEntries) {
      try {
        await this.db.cache.delete(entry.key);
        console.log('[CacheService] entry deleted', entry.key);
      } catch (error) {
        console.error('[CacheService] error deleting entry', error);
      }
    }
  }

  private calculateExpiresAt(ttl?: number): Date | undefined {
    if (!ttl || ttl <= 0) {
      return undefined;
    }

    return new Date(Date.now() + ttl);
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    const entry: CacheEntry = {
      key,
      value,
      createdAt: new Date(),
      ttl,
      expiresAt: this.calculateExpiresAt(ttl),
    };

    await this.db.cache.put(entry);
  }

  async get(key: string): Promise<string | null> {
    const entry = await this.db.cache.get(key);

    if (!entry) {
      return null;
    }

    if (entry.expiresAt && entry.expiresAt < new Date()) {
      await this.db.cache.delete(key);

      return null;
    }

    return entry.value;
  }

  async delete(key: string): Promise<boolean> {
    await this.db.cache.delete(key);

    return true;
  }

  async has(key: string): Promise<boolean> {
    const entry = await this.db.cache.get(key);

    if (!entry) {
      return false;
    }

    if (entry.expiresAt && entry.expiresAt < new Date()) {
      await this.db.cache.delete(key);

      return false;
    }

    return true;
  }

  async doesKeyExist(key: string): Promise<boolean> {
    const entry = await this.db.cache.get(key);

    return entry !== undefined;
  }

  async clear(): Promise<void> {
    await this.db.cache.clear();
  }

  async getKeys(): Promise<string[]> {
    const entries = await this.db.cache
      .filter((entry) => !entry.expiresAt || entry.expiresAt > new Date())
      .toArray();

    return entries.map((entry) => entry.key);
  }

  async size(): Promise<number> {
    return await this.db.cache
      .filter((entry) => !entry.expiresAt || entry.expiresAt > new Date())
      .count();
  }

  async getInfo(): Promise<{
    totalEntries: number;
    expiredEntries: number;
    activeEntries: number;
  }> {
    const entries = await this.db.cache.toArray();
    const now = new Date();
    const expiredCount = entries.filter((entry) => entry.expiresAt && entry.expiresAt < now).length;

    return {
      totalEntries: entries.length,
      expiredEntries: expiredCount,
      activeEntries: entries.length - expiredCount,
    };
  }
}

export const cacheService = new CacheService();
