import { Directory, Filesystem } from '@capacitor/filesystem';
import { DateTime } from 'luxon';
import { capacitorPreferencesStorage } from '../utils/capacitor-preferences-storage';

const CLEANUP_LAST_RUN_KEY = 'deliveryNotes_lastCleanup';

export class DeliveryNoteCleanupService {
  private static instance: DeliveryNoteCleanupService;
  private readonly basePath = 'deliveryNotes';
  private readonly directory = Directory.Cache; // Using Cache directory like OfflineStorageService

  static getInstance(): DeliveryNoteCleanupService {
    if (!DeliveryNoteCleanupService.instance) {
      DeliveryNoteCleanupService.instance = new DeliveryNoteCleanupService();
    }

    return DeliveryNoteCleanupService.instance;
  }

  /**
   * Clean up old delivery notes folders (keep only today's folder)
   * @returns Promise with cleanup statistics
   */
  async cleanupOldDeliveryNotes(): Promise<{
    deletedFolders: string[];
    deletedFiles: number;
    error?: string;
  }> {
    const stats = {
      deletedFolders: [] as string[],
      deletedFiles: 0,
    };

    try {
      console.log('[DeliveryNoteCleanup] Starting cleanup of old delivery notes...');

      // Get today's date in ISO format
      const todayFolder = DateTime.now().toISODate();

      // List all folders in the delivery notes directory
      let folders: string[] = [];
      try {
        const result = await Filesystem.readdir({
          path: this.basePath,
          directory: this.directory,
        });

        folders = result.files.filter((file) => file.type === 'directory').map((file) => file.name);
      } catch (error: any) {
        if (error.message?.includes('does not exist')) {
          console.log('[DeliveryNoteCleanup] No delivery notes directory found, nothing to clean');

          return stats;
        }
        throw error;
      }

      console.log('[DeliveryNoteCleanup] Found folders:', folders);

      // Delete all folders except today's
      for (const folder of folders) {
        if (folder !== todayFolder) {
          try {
            // Count files before deletion (for stats)
            const filesResult = await Filesystem.readdir({
              path: `${this.basePath}/${folder}`,
              directory: this.directory,
            });
            stats.deletedFiles += filesResult.files.length;

            // Delete the folder and all its contents
            await Filesystem.rmdir({
              path: `${this.basePath}/${folder}`,
              directory: this.directory,
              recursive: true,
            });

            stats.deletedFolders.push(folder);
            console.log(`[DeliveryNoteCleanup] Deleted folder: ${folder}`);
          } catch (error) {
            console.error(`[DeliveryNoteCleanup] Error deleting folder ${folder}:`, error);
          }
        }
      }

      // Update last cleanup timestamp
      await capacitorPreferencesStorage.setItem(CLEANUP_LAST_RUN_KEY, DateTime.now().toISO());

      console.log('[DeliveryNoteCleanup] Cleanup completed:', stats);

      return stats;
    } catch (error) {
      console.error('[DeliveryNoteCleanup] Error during cleanup:', error);

      return {
        ...stats,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check if cleanup should run (once per day)
   * @returns Promise<boolean>
   */
  async shouldRunCleanup(): Promise<boolean> {
    try {
      const lastRun = await capacitorPreferencesStorage.getItem(CLEANUP_LAST_RUN_KEY);

      if (!lastRun) {
        return true;
      }

      const lastRunDate = DateTime.fromISO(lastRun);
      const today = DateTime.now().startOf('day');

      // Run cleanup if last run was before today
      return lastRunDate < today;
    } catch (error) {
      console.error('[DeliveryNoteCleanup] Error checking last cleanup run:', error);

      return true; // Run cleanup if we can't determine last run
    }
  }

  /**
   * Get storage statistics for delivery notes
   * @returns Promise with storage stats
   */
  async getStorageStats(): Promise<{
    totalFolders: number;
    totalFiles: number;
    totalSizeBytes: number;
    folders: Array<{
      name: string;
      fileCount: number;
      sizeBytes: number;
    }>;
  }> {
    const stats = {
      totalFolders: 0,
      totalFiles: 0,
      totalSizeBytes: 0,
      folders: [] as Array<{
        name: string;
        fileCount: number;
        sizeBytes: number;
      }>,
    };

    try {
      const result = await Filesystem.readdir({
        path: this.basePath,
        directory: this.directory,
      });

      const folders = result.files.filter((file) => file.type === 'directory');
      stats.totalFolders = folders.length;

      for (const folder of folders) {
        const folderPath = `${this.basePath}/${folder.name}`;
        const filesResult = await Filesystem.readdir({
          path: folderPath,
          directory: this.directory,
        });

        let folderSize = 0;

        for (const file of filesResult.files) {
          if (file.type === 'file') {
            try {
              const fileStat = await Filesystem.stat({
                path: `${folderPath}/${file.name}`,
                directory: this.directory,
              });
              folderSize += fileStat.size || 0;
            } catch (error) {
              console.error('Error getting file size:', error);
            }
          }
        }

        stats.folders.push({
          name: folder.name,
          fileCount: filesResult.files.filter((f) => f.type === 'file').length,
          sizeBytes: folderSize,
        });

        stats.totalFiles += filesResult.files.filter((f) => f.type === 'file').length;
        stats.totalSizeBytes += folderSize;
      }

      return stats;
    } catch (error: any) {
      if (error.message?.includes('does not exist')) {
        return stats; // Return empty stats if directory doesn't exist
      }
      throw error;
    }
  }

  /**
   * Clean up the Redux state to remove references to deleted files
   * This should be called after cleaning up the file system
   */
  async cleanupReduxState(_dispatch: any): Promise<void> {
    try {
      const todayFolder = DateTime.now().toISODate();

      // Get current offline notes from Redux
      const state = await capacitorPreferencesStorage.getItem('deliveryNotes_offline');

      if (!state) {
        return;
      }

      const offlineNotes = JSON.parse(state);
      const updatedNotes: Record<string, string> = {};

      // Keep only notes from today's folder
      for (const [noteId, path] of Object.entries(offlineNotes)) {
        if (typeof path === 'string' && path.includes(todayFolder)) {
          updatedNotes[noteId] = path;
        }
      }

      // Update the persisted state
      await capacitorPreferencesStorage.setItem(
        'deliveryNotes_offline',
        JSON.stringify(updatedNotes),
      );

      console.log('[DeliveryNoteCleanup] Updated Redux state, removed old references');
    } catch (error) {
      console.error('[DeliveryNoteCleanup] Error cleaning Redux state:', error);
    }
  }
}

export const deliveryNoteCleanupService = DeliveryNoteCleanupService.getInstance();
