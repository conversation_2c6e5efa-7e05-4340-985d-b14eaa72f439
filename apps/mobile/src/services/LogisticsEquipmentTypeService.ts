import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { ILogisticsEquipmentTypeEntity } from '../interfaces/entity/i-logistics-equipment-type-entity';
import {
  ICreateLogisticsEquipmentTypeDto,
  IUpdateLogisticsEquipmentTypeDto,
} from '../interfaces/dto/logistics-equipment-type.dto';
import { apiService } from './ApiService';

export class LogisticsEquipmentTypeService {
  private readonly managerEndpoint = '/api/manager/logistics-equipment-types';
  private readonly operatorEndpoint = '/api/deliver/logistics-equipment-types';

  /**
   * Manager: Créer un type d'équipement logistique
   */
  async create(data: ICreateLogisticsEquipmentTypeDto): Promise<ILogisticsEquipmentTypeEntity> {
    return apiService.post<ILogisticsEquipmentTypeEntity>(this.managerEndpoint, data);
  }

  /**
   * Manager: Récupérer tous les types d'équipements logistiques (paginés)
   */
  async findAll(
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ILogisticsEquipmentTypeEntity>> {
    return apiService.getPaginated<ILogisticsEquipmentTypeEntity>(this.managerEndpoint, params);
  }

  /**
   * Manager: Récupérer un type d'équipement logistique par ID
   */
  async findOne(id: string): Promise<ILogisticsEquipmentTypeEntity> {
    return apiService.get<ILogisticsEquipmentTypeEntity>(`${this.managerEndpoint}/${id}`);
  }

  /**
   * Manager: Mettre à jour un type d'équipement logistique
   */
  async update(
    id: string,
    data: IUpdateLogisticsEquipmentTypeDto,
  ): Promise<ILogisticsEquipmentTypeEntity> {
    return apiService.patch<ILogisticsEquipmentTypeEntity>(`${this.managerEndpoint}/${id}`, data);
  }

  /**
   * Manager: Supprimer un type d'équipement logistique
   */
  async delete(id: string): Promise<void> {
    return apiService.delete<void>(`${this.managerEndpoint}/${id}`);
  }

  /**
   * Deliver: Récupérer tous les types d'équipements logistiques
   */
  async findAllForDeliver(): Promise<ILogisticsEquipmentTypeEntity[]> {
    const response = await apiService.getPaginated<ILogisticsEquipmentTypeEntity>(
      this.operatorEndpoint,
      { page: 1, limit: 100 },
    );

    return response.items;
  }

  /**
   * Deliver: Récupérer un type d'équipement logistique par ID
   */
  async findOneForDeliver(id: string): Promise<ILogisticsEquipmentTypeEntity> {
    return apiService.get<ILogisticsEquipmentTypeEntity>(`${this.operatorEndpoint}/${id}`);
  }
}

export const logisticsEquipmentTypeService = new LogisticsEquipmentTypeService();
