import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { IControlTourEquipmentDto } from '../interfaces/dto/control-tour-equipment.dto';
import { apiService } from './ApiService';

export class TourReceptionistService {
  private readonly endpoint = '/api/receptionist/tours';

  /**
   * Récupère tous les tours d'aujourd'hui pour le réceptionniste
   */
  async getTodayTours(): Promise<ITourEntity[]> {
    return apiService.get<ITourEntity[]>(`${this.endpoint}/today`);
  }

  /**
   * Contrôle l'équipement d'une tournée
   */
  async controlTourEquipment(tourId: string, dto: IControlTourEquipmentDto): Promise<ITourEntity> {
    return apiService.post<ITourEntity>(`${this.endpoint}/${tourId}/control-equipment`, dto);
  }
}

export const tourReceptionistService = new TourReceptionistService();
