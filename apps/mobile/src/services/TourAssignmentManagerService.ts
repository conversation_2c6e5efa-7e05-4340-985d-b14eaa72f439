import { ITourAssignmentEntity } from '../interfaces/entity/i-tour-assignment-entity';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { apiService } from './ApiService';

export class TourAssignmentManagerService {
  private readonly endpoint = '/api/manager/tour-assignments';

  /**
   * Liste paginée des affectations de tours
   */
  async getTourAssignments(
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ITourAssignmentEntity>> {
    return apiService.getPaginated<ITourAssignmentEntity>(this.endpoint, params);
  }

  /**
   * Récupère une affectation de tour par son ID
   */
  async getTourAssignmentById(id: string): Promise<ITourAssignmentEntity> {
    return apiService.get<ITourAssignmentEntity>(`${this.endpoint}/${id}`);
  }

  /**
   * Crée une nouvelle affectation de tour
   */
  async createTourAssignment(data: {
    userId: string;
    tourId: string;
    startDate: string;
    endDate?: string;
  }): Promise<ITourAssignmentEntity> {
    return apiService.post<ITourAssignmentEntity>(this.endpoint, data);
  }

  /**
   * Met à jour une affectation de tour
   */
  async updateTourAssignment(
    id: string,
    data: {
      userId?: string;
      tourId?: string;
      startDate?: string;
      endDate?: string;
    },
  ): Promise<ITourAssignmentEntity> {
    return apiService.patch<ITourAssignmentEntity>(`${this.endpoint}/${id}`, data);
  }

  /**
   * Supprime une affectation de tour
   */
  async deleteTourAssignment(id: string): Promise<void> {
    return apiService.delete<void>(`${this.endpoint}/${id}`);
  }
}

export const tourAssignmentManagerService = new TourAssignmentManagerService();
