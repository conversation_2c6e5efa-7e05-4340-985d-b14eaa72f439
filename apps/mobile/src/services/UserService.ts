import type { IUpdateUserRequestDto } from '../interfaces/dto/user-update-request.dto';
import { IUserEntity } from '../interfaces/entity/i-user-entity';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { apiService } from './ApiService';

export class UserService {
  private readonly endpoint = '/api/user';

  /**
   * Liste paginée des utilisateurs
   */
  async getAllUsers(params?: IPaginationParams): Promise<IPaginatedResponse<IUserEntity>> {
    return apiService.getPaginated<IUserEntity>(this.endpoint, params);
  }

  /**
   * Récupère un utilisateur par ID
   */
  async getUserById(id: string): Promise<IUserEntity> {
    return apiService.get<IUserEntity>(`${this.endpoint}/${id}`);
  }

  /**
   * Récupère un utilisateur par username
   */
  async getUserByUsername(username: string): Promise<IUserEntity> {
    return apiService.get<IUserEntity>(`${this.endpoint}/username/${username}`);
  }

  /**
   * Récupère le profil utilisateur courant
   */
  async getCurrentUser(): Promise<IUserEntity> {
    return apiService.get<IUserEntity>(`${this.endpoint}/me`);
  }

  /**
   * Met à jour le profil utilisateur courant
   */
  async updateProfile(userData: IUpdateUserRequestDto): Promise<IUserEntity> {
    return apiService.patch<IUserEntity>(`${this.endpoint}/me`, userData);
  }

  /**
   * Récupère la liste des couleurs par défaut disponibles
   */
  async getDefaultColors(): Promise<string[]> {
    return apiService.get<string[]>(`${this.endpoint}/default-colors`);
  }

  /**
   * Reset password for current user
   */
  async resetMyPassword(password: string, temporary?: boolean): Promise<void> {
    return apiService.post<void>(`${this.endpoint}/me/reset-password`, {
      password,
      temporary,
    });
  }

  /**
   * Reset password for another user (manager only)
   */
  async resetUserPassword(userId: string, password: string, temporary?: boolean): Promise<void> {
    return apiService.post<void>(`/api/manager/users/${userId}/reset-password`, {
      password,
      temporary,
    });
  }
}

export const userService = new UserService();
