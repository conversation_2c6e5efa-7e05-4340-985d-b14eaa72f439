import { IAddress } from '../interfaces/entity/i-address';
import { IClientEntity } from '../interfaces/entity/i-client-entity';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { apiService } from './ApiService';

export class ClientManagerService {
  private readonly endpoint = '/api/manager/clients';

  /**
   * Liste paginée des clients
   */
  async getClients(params?: IPaginationParams): Promise<IPaginatedResponse<IClientEntity>> {
    return apiService.getPaginated<IClientEntity>(this.endpoint, params);
  }

  /**
   * Récupère un client par son ID
   */
  async getClientById(id: string): Promise<IClientEntity> {
    return apiService.get<IClientEntity>(`${this.endpoint}/${id}`);
  }

  /**
   * Met à jour un client
   */
  async updateClient(
    id: string,
    data: { email?: string; address?: IAddress },
  ): Promise<IClientEntity> {
    return apiService.patch<IClientEntity>(`${this.endpoint}/${id}`, data);
  }
}

export const clientManagerService = new ClientManagerService();
