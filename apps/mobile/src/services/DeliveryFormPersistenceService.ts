import {
  IncidentData,
  PaymentData,
  ReturnedEquipmentData,
  SignatureData,
  UnloadedEquipmentData,
} from '../components/services/deliverer/delivery-process';
import { DeliveryStep } from '../pages/admin/DeliveryProcess';
import { capacitorPreferencesStorage } from '../utils/capacitor-preferences-storage';

export interface PersistedDeliveryFormData {
  stopId: string;
  currentStep?: DeliveryStep;
  lastUpdated: number;
  paymentData?: PaymentData;
  equipmentDepositData?: UnloadedEquipmentData;
  equipmentPickupData?: ReturnedEquipmentData;
  documentationData?: {
    photos: string[];
    comment: string;
  };
  signatureData?: SignatureData;
  incidentData?: IncidentData;
  gpsData?: {
    latitude: number;
    longitude: number;
    timestamp: number;
    accuracy?: number;
  };
}

class DeliveryFormPersistenceService {
  private readonly STORAGE_KEY_PREFIX = 'delivery_form_';
  private readonly PERSISTENCE_VERSION = '1.0.0';

  /**
   * Obtenir la clé de stockage pour un arrêt spécifique
   */
  private getStorageKey(stopId: string): string {
    return `${this.STORAGE_KEY_PREFIX}${stopId}`;
  }

  /**
   * Sauvegarder les données du formulaire pour un arrêt
   */
  async saveFormData(stopId: string, formData: Partial<PersistedDeliveryFormData>): Promise<void> {
    try {
      const storageKey = this.getStorageKey(stopId);
      const existingData = await this.getFormData(stopId);

      const dataToSave: PersistedDeliveryFormData = {
        ...existingData,
        ...formData,
        stopId,
        lastUpdated: Date.now(),
      };

      const serializedData = JSON.stringify({
        version: this.PERSISTENCE_VERSION,
        data: dataToSave,
      });

      await capacitorPreferencesStorage.setItem(storageKey, serializedData);

      console.log(`Formulaire sauvegardé pour l'arrêt ${stopId}`, dataToSave);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du formulaire:', error);
      throw new Error('Impossible de sauvegarder le formulaire');
    }
  }

  /**
   * Récupérer les données du formulaire pour un arrêt
   */
  async getFormData(stopId: string): Promise<PersistedDeliveryFormData | null> {
    try {
      const storageKey = this.getStorageKey(stopId);
      const rawData = await capacitorPreferencesStorage.getItem(storageKey);

      if (!rawData) {
        return null;
      }

      const parsedData = JSON.parse(rawData);

      // Vérifier la version pour la compatibilité future
      if (parsedData.version !== this.PERSISTENCE_VERSION) {
        console.warn(`Version du formulaire obsolète: ${parsedData.version}, suppression...`);
        await this.clearFormData(stopId);

        return null;
      }

      return parsedData.data as PersistedDeliveryFormData;
    } catch (error) {
      console.error('Erreur lors de la récupération du formulaire:', error);

      return null;
    }
  }

  /**
   * Vérifier si des données de formulaire existent pour un arrêt
   */
  async hasFormData(stopId: string): Promise<boolean> {
    const formData = await this.getFormData(stopId);

    return formData != null;
  }

  /**
   * Vérifier si le formulaire est partiellement rempli
   */
  async isFormPartiallyFilled(stopId: string): Promise<boolean> {
    const formData = await this.getFormData(stopId);

    if (!formData) {
      return false;
    }

    // Un formulaire est considéré partiellement rempli s'il a au moins une donnée
    return !!(
      formData.paymentData ||
      formData.equipmentDepositData ||
      formData.equipmentPickupData ||
      formData.documentationData ||
      formData.signatureData ||
      formData.incidentData
    );
  }

  /**
   * Supprimer les données du formulaire pour un arrêt
   */
  async clearFormData(stopId: string): Promise<void> {
    try {
      const storageKey = this.getStorageKey(stopId);
      await capacitorPreferencesStorage.removeItem(storageKey);

      console.log(`Formulaire supprimé pour l'arrêt ${stopId}`);
    } catch (error) {
      console.error('Erreur lors de la suppression du formulaire:', error);
      throw new Error('Impossible de supprimer le formulaire');
    }
  }

  /**
   * Mettre à jour l'étape actuelle du formulaire
   */
  async updateCurrentStep(stopId: string, currentStep: DeliveryStep): Promise<void> {
    await this.saveFormData(stopId, { currentStep });
  }

  /**
   * Nettoyer les formulaires anciens (plus de 7 jours)
   */
  async cleanupOldForms(): Promise<void> {
    try {
      const SEVEN_DAYS_MS = 7 * 24 * 60 * 60 * 1000;
      // const cutoffTime = Date.now() - SEVEN_DAYS_MS;

      // Note: Capacitor Preferences ne permet pas d'énumérer les clés facilement
      // Cette fonctionnalité pourrait être améliorée avec IndexedDB si nécessaire
      console.log('Nettoyage des anciens formulaires...'); // Placeholder
    } catch (error) {
      console.error('Erreur lors du nettoyage des formulaires:', error);
    }
  }

  /**
   * Obtenir un résumé des données du formulaire pour l'affichage
   */
  async getFormSummary(stopId: string): Promise<{
    hasPayment: boolean;
    hasEquipmentDeposit: boolean;
    hasEquipmentPickup: boolean;
    hasDocumentation: boolean;
    hasSignature: boolean;
    hasIncident: boolean;
    lastUpdated?: Date;
  } | null> {
    const formData = await this.getFormData(stopId);

    if (!formData) {
      return null;
    }

    return {
      hasPayment: !!formData.paymentData,
      hasEquipmentDeposit: !!formData.equipmentDepositData,
      hasEquipmentPickup: !!formData.equipmentPickupData,
      hasDocumentation: !!formData.documentationData,
      hasSignature: !!formData.signatureData,
      hasIncident: !!formData.incidentData,
      lastUpdated: formData.lastUpdated ? new Date(formData.lastUpdated) : undefined,
    };
  }
}

export const deliveryFormPersistenceService = new DeliveryFormPersistenceService();
