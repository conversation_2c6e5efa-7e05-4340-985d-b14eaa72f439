export { ApiService, apiService } from './ApiService';
export { ClientManagerService, clientManagerService } from './ClientManagerService';
export { FileService, fileService } from './FileService';
export { HolidayService, holidayService } from './HolidayService';
export { ImportManagerService, importManagerService } from './ImportManagerService';
export { IncidentTypeService, incidentTypeService } from './IncidentTypeService';
export {
  LogisticsEquipmentTypeService,
  logisticsEquipmentTypeService,
} from './LogisticsEquipmentTypeService';
export {
  StopDeliverService as StopService,
  stopDeliverService as stopService,
} from './StopService';
export {
  TourAssignmentManagerService,
  tourAssignmentManagerService,
} from './TourAssignmentManagerService';
export { TourDeliverService, tourDeliverService } from './TourDeliverService';
export { TourManagerService, tourManagerService } from './TourManagerService';
export { TourReceptionistService, tourReceptionistService } from './TourReceptionistService';
export { UserService, userService } from './UserService';
