import { IVehicleEntity } from '../interfaces/entity/i-vehicle-entity';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { ICreateVehicleDto, IUpdateVehicleDto } from '../interfaces/dto/vehicle.dto';
import { apiService } from './ApiService';

export class VehicleService {
  private readonly managerEndpoint = '/manager/vehicles';
  private readonly operatorEndpoint = '/deliver/vehicles';

  /**
   * Manager: Liste paginée des véhicules
   */
  async getVehicles(params?: IPaginationParams): Promise<IPaginatedResponse<IVehicleEntity>> {
    return apiService.getPaginated<IVehicleEntity>(this.managerEndpoint, params);
  }

  /**
   * Deliver: Liste paginée des véhicules (lecture seule)
   */
  async getVehiclesDeliver(
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<IVehicleEntity>> {
    return apiService.getPaginated<IVehicleEntity>(this.operatorEndpoint, params);
  }

  /**
   * Manager: Créer un véhicule
   */
  async createVehicle(data: ICreateVehicleDto): Promise<IVehicleEntity> {
    return apiService.post<IVehicleEntity>(this.managerEndpoint, data);
  }

  /**
   * Manager: Récupérer un véhicule par ID
   */
  async getVehicleById(id: string): Promise<IVehicleEntity> {
    return apiService.get<IVehicleEntity>(`${this.managerEndpoint}/${id}`);
  }

  /**
   * Manager: Mettre à jour un véhicule
   */
  async updateVehicle(id: string, data: IUpdateVehicleDto): Promise<IVehicleEntity> {
    return apiService.patch<IVehicleEntity>(`${this.managerEndpoint}/${id}`, data);
  }

  /**
   * Manager: Supprimer un véhicule
   */
  async deleteVehicle(id: string): Promise<void> {
    return apiService.delete<void>(`${this.managerEndpoint}/${id}`);
  }
}

export const vehicleService = new VehicleService();
