import APP_CONFIG from '../config/app.config';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { authService } from '../plugin/keycloak/auth-service';

interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
  isFormData?: boolean;
  params?: IPaginationParams;
}

export class ApiService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = APP_CONFIG.ENV.API_URL;
  }

  /**
   * Construit les paramètres de pagination pour les requêtes API
   */
  buildPaginationParams(params?: IPaginationParams): string {
    if (!params) {
      return '';
    }

    const queryParams = new URLSearchParams();

    if (params.page) {
      queryParams.append('page', params.page.toString());
    }

    if (params.limit) {
      queryParams.append('limit', params.limit.toString());
    }

    if (params.sortBy || APP_CONFIG.SORT.DEFAULT_FIELD) {
      queryParams.append('sortBy', params.sortBy || APP_CONFIG.SORT.DEFAULT_FIELD);
    }

    if (params.sortOrder || APP_CONFIG.SORT.DEFAULT_ORDER) {
      queryParams.append('sortOrder', params.sortOrder || APP_CONFIG.SORT.DEFAULT_ORDER);
    }

    const queryString = queryParams.toString();

    return queryString ? `?${queryString}` : '';
  }

  /**
   * Méthode générique pour effectuer des requêtes
   */
  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    // Appliquer automatiquement les paramètres de pagination si présents
    let finalEndpoint = endpoint;

    if (options.params) {
      finalEndpoint += this.buildPaginationParams(options.params);
    }

    const url = `${this.baseUrl}${finalEndpoint}`;

    // Headers par défaut
    const headers: Record<string, string> = {
      ...options.headers,
    };

    // N'ajoute Content-Type que si ce n'est pas FormData
    if (!options.isFormData) {
      headers['Content-Type'] = 'application/json';
    }

    // Ajout du token d'authentification si disponible
    try {
      const token = await authService.getValidToken();

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Failed to get auth token:', error);
      // Continue sans authentification
    }

    // Configuration de la requête
    const config: RequestInit = {
      method: options.method || 'GET',
      headers,
      credentials: options.credentials || 'same-origin',
    };

    // Traitement du body selon son type
    if (options.body) {
      config.body = options.isFormData ? options.body : JSON.stringify(options.body);
    }

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorMessage = await this.parseErrorResponse(response);
        throw new Error(errorMessage);
      }

      // Si la réponse est vide ou pas du JSON
      if (response.status === 204 || response.headers.get('content-length') === '0') {
        return null as unknown as T;
      }

      const data = await response.json();

      return data as T;
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }

  /**
   * Méthode d'aide pour parser les réponses d'erreur
   */
  private async parseErrorResponse(response: Response): Promise<string> {
    try {
      const errorData = await response.json();

      return errorData?.message || `Request failed with status ${response.status}`;
    } catch {
      const errorText = await response.text();

      return errorText || `Request failed with status ${response.status}`;
    }
  }

  /**
   * Méthodes HTTP de base
   */
  async get<T>(endpoint: string, options: Partial<RequestOptions> = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'GET',
    });
  }

  async post<T>(endpoint: string, data?: any, options: Partial<RequestOptions> = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data,
    });
  }

  async put<T>(endpoint: string, data?: any, options: Partial<RequestOptions> = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data,
    });
  }

  async delete<T>(endpoint: string, options: Partial<RequestOptions> = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'DELETE',
    });
  }

  async patch<T>(endpoint: string, data?: any, options: Partial<RequestOptions> = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data,
    });
  }

  /**
   * Méthode pour télécharger des fichiers binaires (Blob)
   */
  async getBlob(endpoint: string, options: Partial<RequestOptions> = {}): Promise<Blob> {
    const url = `${this.baseUrl}${endpoint}`;

    try {
      const authHeaders = await authService.getAuthHeaders();
      const headers = {
        ...authHeaders,
        ...options.headers,
      };

      const config: RequestInit = {
        method: 'GET',
        headers,
        credentials: options.credentials || 'same-origin',
      };

      const response = await fetch(url, config);

      if (!response.ok) {
        const errorMessage = await this.parseErrorResponse(response);
        throw new Error(errorMessage);
      }

      return await response.blob();
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }

  /**
   * Requête GET avec pagination généralisée
   */
  async getPaginated<T>(
    endpoint: string,
    params?: IPaginationParams,
    options: Partial<RequestOptions> = {},
  ): Promise<IPaginatedResponse<T>> {
    return this.get<IPaginatedResponse<T>>(endpoint, {
      ...options,
      params,
    });
  }

  /**
   * Retourne l'URL de base de l'API
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}

export const apiService = new ApiService();
