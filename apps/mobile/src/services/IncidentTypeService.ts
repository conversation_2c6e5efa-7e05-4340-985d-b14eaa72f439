import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { IIncidentTypeEntity } from '../interfaces/entity/i-incident-type-entity';
import {
  ICreateIncidentTypeDto,
  IUpdateIncidentTypeDto,
} from '../interfaces/dto/incident-type.dto';
import { apiService } from './ApiService';

export class IncidentTypeService {
  private readonly managerEndpoint = '/api/manager/incident-types';
  private readonly operatorEndpoint = '/api/deliver/incident-types';

  /**
   * Manager: Créer un type d'incident
   */
  async create(data: ICreateIncidentTypeDto): Promise<IIncidentTypeEntity> {
    return apiService.post<IIncidentTypeEntity>(this.managerEndpoint, data);
  }

  /**
   * Manager: Récupérer tous les types d'incidents (paginés)
   */
  async findAll(params?: IPaginationParams): Promise<IPaginatedResponse<IIncidentTypeEntity>> {
    return apiService.getPaginated<IIncidentTypeEntity>(this.managerEndpoint, params);
  }

  /**
   * Manager: Récupérer un type d'incident par ID
   */
  async findOne(id: string): Promise<IIncidentTypeEntity> {
    return apiService.get<IIncidentTypeEntity>(`${this.managerEndpoint}/${id}`);
  }

  /**
   * Manager: Mettre à jour un type d'incident
   */
  async update(id: string, data: IUpdateIncidentTypeDto): Promise<IIncidentTypeEntity> {
    return apiService.patch<IIncidentTypeEntity>(`${this.managerEndpoint}/${id}`, data);
  }

  /**
   * Manager: Supprimer un type d'incident
   */
  async delete(id: string): Promise<void> {
    return apiService.delete<void>(`${this.managerEndpoint}/${id}`);
  }

  /**
   * Deliver: Récupérer tous les types d'incidents actifs
   */
  async findAllForDeliver(): Promise<IIncidentTypeEntity[]> {
    return apiService.get<IIncidentTypeEntity[]>(this.operatorEndpoint);
  }
}

export const incidentTypeService = new IncidentTypeService();
