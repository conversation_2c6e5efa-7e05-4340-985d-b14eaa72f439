import type { IHolidayEntity } from '../interfaces/entity/i-holiday-entity';
import { apiService } from './ApiService';

export class HolidayService {
  private readonly endpoint = '/api/holidays';

  /**
   * Liste des jours fériés
   */
  async getHolidays(): Promise<IHolidayEntity[]> {
    return apiService.get<IHolidayEntity[]>(this.endpoint);
  }

  /**
   * Récupère les jours fériés pour une année donnée
   */
  async getHolidaysByYear(year: number): Promise<IHolidayEntity[]> {
    return apiService.get<IHolidayEntity[]>(`${this.endpoint}/year/${year}`);
  }
}

export const holidayService = new HolidayService();
