import { IUpdateFileRequestDto } from '../interfaces/dto/file-update-request.dto';
import { IUploadFileRequestDto } from '../interfaces/dto/file-upload-request.dto';
import { IFileEntity } from '../interfaces/entity/i-file-entity';
import { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination';
import { apiService } from './ApiService';

export class FileService {
  private readonly endpoint = '/api/file';

  /**
   * Liste paginée des fichiers
   */
  async getAll(params?: IPaginationParams): Promise<IPaginatedResponse<IFileEntity>> {
    return apiService.getPaginated<IFileEntity>(this.endpoint, params);
  }

  /**
   * Récupère un fichier par son ID
   */
  async getById(id: string): Promise<IFileEntity> {
    return apiService.get<IFileEntity>(`${this.endpoint}/${id}`);
  }

  /**
   * <PERSON><PERSON>ne l'URL de téléchargement d'un fichier
   */
  getFileDownloadUrl(id: string): string {
    return `${apiService.getBaseUrl()}${this.endpoint}/${id}/download`;
  }

  /**
   * Upload un fichier
   */
  async upload(request: IUploadFileRequestDto): Promise<IFileEntity> {
    const formData = new FormData();
    formData.append('file', request.file);

    if (request.userId) {
      formData.append('userId', request.userId.toString());
    }

    if (request.path) {
      formData.append('path', request.path);
    }
    formData.append('isPublic', (request.isPublic || false).toString());

    if (request.metadata) {
      formData.append('metadata', JSON.stringify(request.metadata));
    }

    return apiService.post<IFileEntity>(`${this.endpoint}`, formData, {
      isFormData: true,
    });
  }

  /**
   * Met à jour un fichier
   */
  async update(id: string, data: IUpdateFileRequestDto): Promise<IFileEntity> {
    return apiService.put<IFileEntity>(`${this.endpoint}/${id}`, data);
  }

  /**
   * Supprime un fichier
   */
  async delete(id: string): Promise<void> {
    return apiService.delete<void>(`${this.endpoint}/${id}`);
  }
}

export const fileService = new FileService();
