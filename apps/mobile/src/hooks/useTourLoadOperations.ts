/**
 * @file useTourLoadOperations.ts
 * @description Hook pour gérer les opérations de chargement des tournées
 * avec intégration de la queue d'événements
 */

import { useIonToast } from '@ionic/react';
import { useCallback } from 'react';
import { useHistory } from 'react-router';

import { PATHS } from '../config/routes';
import { ILoadStopDto } from '../interfaces/dto/load-stop.dto';
import { ILogisticsEquipmentDetailDto } from '../interfaces/dto/stop-delivery.dto';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { loadingControlPersistenceService } from '../services/LoadingControlPersistenceService';
import { addEventToQueue } from '../stores/eventQueueSlice';
import { useAppDispatch } from '../utils/redux';
import { useLogisticsEquipmentTypes } from './useLogisticsEquipmentTypes';

/**
 * Hook pour gérer les opérations de chargement des tournées
 */
export function useTourLoadOperations() {
  const dispatch = useAppDispatch();
  const history = useHistory();
  const [presentToast] = useIonToast();
  const { getDefaultTypeForKind } = useLogisticsEquipmentTypes();

  /**
   * Charge une tournée spécifique en utilisant la queue d'événements
   * pour chaque arrêt de la tournée
   */
  const loadTour = useCallback(
    async (tour: ITourEntity) => {
      try {
        // Récupérer les validations pour cette tournée
        const tourValidations = await loadingControlPersistenceService.getStopValidationsForTour(
          tour.id,
          'load',
        );

        // Traiter chaque arrêt de la tournée
        for (const stop of tour.stops || []) {
          const stopValidation = tourValidations[stop.id];

          if (stopValidation?.equipmentItems && stopValidation.isValidated) {
            // Calculer les équipements pour ce stop
            const equipmentCount = {
              palletCount: 0,
              rollCount: 0,
              packageCount: 0,
            };
            const equipmentDetails: ILogisticsEquipmentDetailDto[] = [];

            console.log('🔍 [useTourLoadOperations] Processing stop:', {
              stopId: stop.id,
              equipmentItems: stopValidation.equipmentItems,
            });

            Object.entries(stopValidation.equipmentItems).forEach(([kind, data]) => {
              if (data.isValidated && data.adjustedQuantity > 0) {
                console.log('🔍 [useTourLoadOperations] Processing equipment item:', {
                  type: kind,
                  adjustedQuantity: data.adjustedQuantity,
                  isValidated: data.isValidated,
                });

                // Compter par type d'équipement
                switch (kind as LogisticsEquipmentKind) {
                  case LogisticsEquipmentKind.PALLET:
                    equipmentCount.palletCount += data.adjustedQuantity;
                    break;
                  case LogisticsEquipmentKind.ROLL:
                    equipmentCount.rollCount += data.adjustedQuantity;
                    break;
                  case LogisticsEquipmentKind.PACKAGE:
                    equipmentCount.packageCount += data.adjustedQuantity;
                    break;
                }

                // Créer les détails d'équipement avec le type par défaut pour chaque kind
                const defaultType = getDefaultTypeForKind(kind as LogisticsEquipmentKind);

                if (defaultType) {
                  equipmentDetails.push({
                    logisticsEquipmentTypeId: defaultType.id,
                    quantity: data.adjustedQuantity,
                  });
                }
              }
            });

            // Créer le DTO pour le chargement
            const loadStopDto: ILoadStopDto = {
              equipmentCount,
              equipmentDetails: equipmentDetails.length > 0 ? equipmentDetails : [],
            };

            // Ajouter l'événement à la queue
            console.log('🔍 [useTourLoadOperations] Adding event to queue:', {
              eventType: 'stop-load',
              stopId: stop.id,
              loadStopDto,
            });

            dispatch(
              addEventToQueue({
                eventType: 'stop-load',
                payload: { ...loadStopDto, stopId: stop.id },
              }),
            );
          }
        }
      } catch (error) {
        console.error('Erreur lors du chargement de la tournée:', error);
        presentToast({
          message: 'Erreur lors du chargement de la tournée',
          duration: 3000,
          color: 'danger',
        });
      }
    },
    [dispatch, presentToast, getDefaultTypeForKind],
  );

  /**
   * Charge toutes les tournées planifiées
   */
  const loadAllTours = useCallback(
    async (plannedTours: ITourEntity[]) => {
      try {
        for (const tour of plannedTours) {
          await loadTour(tour);
        }

        // Nettoyer les caches après succès
        for (const tour of plannedTours) {
          await loadingControlPersistenceService.resetStopValidationsForTour(tour.id, 'load');
        }

        // Marquer que le contrôle de chargement a été effectué pour éviter les redirections
        localStorage.setItem('loadingControlCompleted', Date.now().toString());
        localStorage.setItem('loadingControlDate', new Date().toDateString());

        presentToast({
          message: 'Toutes les tournées ont été mises en queue de chargement !',
          duration: 2000,
          color: 'success',
        });

        // Rediriger vers la liste des tournées
        history.push(PATHS.TOURS);
      } catch (error) {
        console.error('Erreur lors du chargement des tournées:', error);
        presentToast({
          message: 'Erreur lors du chargement des tournées',
          duration: 3000,
          color: 'danger',
        });
      }
    },
    [loadTour, presentToast, history],
  );

  return {
    loadTour,
    loadAllTours,
  };
}
