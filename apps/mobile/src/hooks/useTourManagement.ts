import { useMemo, useState } from 'react';

import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { TourStatus, TourType } from '../interfaces/enum/tour.enums';

export interface UseTourManagementProps {
  tours: ITourEntity[];
}

export const useTourManagement = ({ tours }: UseTourManagementProps) => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Filter tours by search term
  const filteredTours = useMemo(() => {
    if (!searchTerm.trim()) {
      return tours;
    }

    const searchLower = searchTerm.toLowerCase().trim();

    return tours.filter((tour) => {
      const tourOriginalNumber = tour.tourIdentifier.originalNumber?.toLowerCase() || '';

      return tourOriginalNumber.includes(searchLower);
    });
  }, [tours, searchTerm]);

  // Group tours by status
  const plannedTours = useMemo(() => {
    return filteredTours.filter((tour) => tour.status === TourStatus.Planned);
  }, [filteredTours]);

  const completedTours = useMemo(() => {
    return filteredTours.filter((tour) => tour.status === TourStatus.Completed);
  }, [filteredTours]);

  const inProgressTours = useMemo(() => {
    return filteredTours.filter((tour) => tour.status === TourStatus.InProgress);
  }, [filteredTours]);

  // Group tours by type
  const frozenTours = useMemo(() => {
    return filteredTours.filter((tour) => tour.tourIdentifier.type === TourType.Frozen);
  }, [filteredTours]);

  const normalTours = useMemo(() => {
    return filteredTours.filter((tour) => tour.tourIdentifier.type !== TourType.Frozen);
  }, [filteredTours]);

  // Check if tour is preloaded
  const isTourPreloaded = (tour: ITourEntity): boolean => {
    let hasPreloadedEquipment = false;

    if (tour.totalPreloadedEquipmentCount) {
      if (
        tour.totalPreloadedEquipmentCount?.packageCount != null &&
        tour.totalPreloadedEquipmentCount?.palletCount != null &&
        tour.totalPreloadedEquipmentCount?.rollCount != null
      ) {
        hasPreloadedEquipment = true;
      }
    }

    return hasPreloadedEquipment;
  };

  // Get tours available for different workflows
  const departureTours = useMemo(() => {
    return plannedTours.filter((tour) => !isTourPreloaded(tour));
  }, [plannedTours]);

  const returnTours = useMemo(() => {
    return completedTours;
  }, [completedTours]);

  const availableToursForPreload = useMemo(() => {
    return tours.filter((tour) => {
      const isValidStatus =
        tour.status === TourStatus.Planned || tour.status === TourStatus.Completed;

      return isValidStatus;
    });
  }, [tours]);

  return {
    // Search
    searchTerm,
    setSearchTerm,
    filteredTours,

    // By status
    plannedTours,
    completedTours,
    inProgressTours,

    // By type
    frozenTours,
    normalTours,

    // By workflow
    departureTours,
    returnTours,
    availableToursForPreload,

    // Utilities
    isTourPreloaded,
  };
};
