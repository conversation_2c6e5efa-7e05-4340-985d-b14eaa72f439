import { useCallback, useMemo, useState } from 'react';

/**
 * Hook pour gérer un processus multi-étapes avec navigation et historique
 * @param steps - Tableau des noms d'étapes
 * @returns {Object} Objet contenant:
 * - `currentStep` - Nom de l'étape actuelle
 * - `currentStepIndex` - Index de l'étape actuelle
 * - `canGoBack` - Booléen indiquant si on peut revenir en arrière
 * - `canGoNext` - Booléen indiquant si on peut avancer
 * - `setStep(stepName)` - Aller directement à une étape nommée
 * - `goToPreviousHistoryStep()` - Revenir à l'étape précédente dans l'historique
 * - `goToNextStep()` - Avancer à l'étape suivante
 * - `reset()` - Retourner à la première étape et vider l'historique
 */
export function useMultiStep(steps: string[]) {
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);
  const [history, setHistory] = useState<number[]>([]);

  const currentStep = useMemo(() => steps[currentStepIndex], [currentStepIndex, steps]);

  const setStep = useCallback(
    (step: string) => {
      if (steps.includes(step)) {
        setHistory([...history, currentStepIndex]);
        setCurrentStepIndex(steps.indexOf(step));
      } else {
        throw new Error(`Step ${step} not found`);
      }
    },
    [steps, history, currentStepIndex],
  );

  const goToPreviousHistoryStep = useCallback(() => {
    if (history.length > 0) {
      const lastStepIndex = history[history.length - 1];
      const rewroteHistory = history.slice(0, -1);
      setHistory(rewroteHistory);
      setCurrentStepIndex(lastStepIndex);
    }
  }, [history]);

  const goToNextStep = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setHistory([...history, currentStepIndex]);
      setCurrentStepIndex(currentStepIndex + 1);
    }
  }, [currentStepIndex, steps, history]);

  const canGoBack = history.length > 0;
  const canGoNext = currentStepIndex < steps.length - 1;

  const reset = useCallback(() => {
    setCurrentStepIndex(0);
    setHistory([]);
  }, []);

  return {
    currentStep,
    currentStepIndex,
    canGoBack,
    canGoNext,
    setStep,
    goToPreviousHistoryStep,
    goToNextStep,
    reset,
  };
}
