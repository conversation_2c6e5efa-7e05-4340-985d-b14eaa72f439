/**
 * @file useTourPreloadOperations.ts
 * @description Hook pour gérer les opérations de préchargement des tournées
 * avec intégration de la queue d'événements
 */

import { useIonToast } from '@ionic/react';
import { useCallback } from 'react';

import { IPreloadStopDto } from '../interfaces/dto/load-stop.dto';
import { ILogisticsEquipmentDetailDto } from '../interfaces/dto/stop-delivery.dto';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { loadingControlPersistenceService } from '../services/LoadingControlPersistenceService';
import { addEventToQueue } from '../stores/eventQueueSlice';
import { useAppDispatch } from '../utils/redux';
import { useLogisticsEquipmentTypes } from './useLogisticsEquipmentTypes';

/**
 * Hook pour gérer les opérations de préchargement des tournées
 */
export function useTourPreloadOperations() {
  const dispatch = useAppDispatch();
  const [presentToast] = useIonToast();
  const { getDefaultTypeForKind } = useLogisticsEquipmentTypes();

  /**
   * Précharge une tournée spécifique en utilisant la queue d'événements
   * pour chaque arrêt de la tournée
   */
  const preloadTour = useCallback(
    async (tour: ITourEntity) => {
      try {
        console.log('🔍 [useTourPreloadOperations] Starting preload for tour:', {
          tourId: tour.id,
          tourNumber: tour.tourIdentifier.originalNumber,
        });

        // Récupérer les validations pour cette tournée (workflow 'preload')
        const tourValidations = await loadingControlPersistenceService.getStopValidationsForTour(
          tour.id,
          'preload',
        );

        console.log('🔍 [useTourPreloadOperations] Retrieved validations:', {
          tourId: tour.id,
          validations: Object.keys(tourValidations).length,
        });

        // Traiter chaque arrêt de la tournée
        for (const stop of tour.stops || []) {
          const stopValidation = tourValidations[stop.id];

          console.log('🔍 [useTourPreloadOperations] Processing stop:', {
            stopId: stop.id,
            hasValidation: !!stopValidation,
            isValidated: stopValidation?.isValidated,
            equipmentItems: stopValidation?.equipmentItems
              ? Object.keys(stopValidation.equipmentItems).length
              : 0,
          });

          if (stopValidation?.equipmentItems && stopValidation.isValidated) {
            // Calculer les équipements pour ce stop
            const equipmentCount = {
              palletCount: 0,
              rollCount: 0,
              packageCount: 0,
            };
            const equipmentDetails: ILogisticsEquipmentDetailDto[] = [];

            Object.entries(stopValidation.equipmentItems).forEach(([kind, data]) => {
              if (data.isValidated && data.adjustedQuantity > 0) {
                console.log('🔍 [useTourPreloadOperations] Processing equipment item:', {
                  type: kind,
                  adjustedQuantity: data.adjustedQuantity,
                  isValidated: data.isValidated,
                });

                // Compter par type d'équipement
                switch (kind as LogisticsEquipmentKind) {
                  case LogisticsEquipmentKind.PALLET:
                    equipmentCount.palletCount += data.adjustedQuantity;
                    break;
                  case LogisticsEquipmentKind.ROLL:
                    equipmentCount.rollCount += data.adjustedQuantity;
                    break;
                  case LogisticsEquipmentKind.PACKAGE:
                    equipmentCount.packageCount += data.adjustedQuantity;
                    break;
                }

                // Créer les détails d'équipement avec le type par défaut pour chaque kind
                const defaultType = getDefaultTypeForKind(kind as LogisticsEquipmentKind);
                console.log('🔍 [useTourPreloadOperations] Default type for kind:', {
                  kind: kind,
                  defaultType: defaultType?.id,
                  defaultTypeName: defaultType?.name,
                });

                if (defaultType) {
                  equipmentDetails.push({
                    logisticsEquipmentTypeId: defaultType.id,
                    quantity: data.adjustedQuantity,
                  });
                }
              }
            });

            console.log('🔍 [useTourPreloadOperations] Final equipment data:', {
              stopId: stop.id,
              equipmentCount,
              equipmentDetails,
            });

            // Créer le DTO pour le préchargement
            const preloadStopDto: IPreloadStopDto = {
              equipmentCount,
              equipmentDetails: equipmentDetails.length > 0 ? equipmentDetails : undefined,
            };

            // Ajouter l'événement à la queue
            console.log('🔍 [useTourPreloadOperations] Dispatching event to queue:', {
              eventType: 'stop-preload',
              stopId: stop.id,
              payload: preloadStopDto,
            });

            dispatch(
              addEventToQueue({
                eventType: 'stop-preload',
                payload: { ...preloadStopDto, stopId: stop.id },
              }),
            );

            console.log('✅ [useTourPreloadOperations] Event dispatched to queue');
          }
        }

        presentToast({
          message: `Tournée n°${tour.tourIdentifier.originalNumber} mise en queue de préchargement`,
          duration: 2000,
          color: 'success',
        });
      } catch (error) {
        console.error('Erreur lors du préchargement de la tournée:', error);
        presentToast({
          message: 'Erreur lors du préchargement de la tournée',
          duration: 3000,
          color: 'danger',
        });
      }
    },
    [dispatch, presentToast, getDefaultTypeForKind],
  );

  /**
   * Précharge toutes les tournées sélectionnées
   */
  const preloadAllTours = useCallback(
    async (tours: ITourEntity[]) => {
      try {
        for (const tour of tours) {
          await preloadTour(tour);
        }

        // Nettoyer les caches après succès
        for (const tour of tours) {
          await loadingControlPersistenceService.resetStopValidationsForTour(tour.id, 'preload');
        }

        presentToast({
          message: 'Toutes les tournées ont été mises en queue de préchargement !',
          duration: 2000,
          color: 'success',
        });
      } catch (error) {
        console.error('Erreur lors du préchargement des tournées:', error);
        presentToast({
          message: 'Erreur lors du préchargement des tournées',
          duration: 3000,
          color: 'danger',
        });
      }
    },
    [preloadTour, presentToast],
  );

  return {
    preloadTour,
    preloadAllTours,
  };
}
