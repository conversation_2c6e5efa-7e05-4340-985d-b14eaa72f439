import { useCallback, useRef, useState } from 'react';

import {
  EquipmentItemForValidationType,
  EquipmentValidationRecord,
} from '../components/services/deliverer/EquipmentValidator';
import { IStopEntity } from '../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { loadingControlPersistenceService } from '../services/LoadingControlPersistenceService';
import { extractEquipmentFromCompletedStop } from '../utils/equipment-extractor';

export interface UseTourValidationStateProps {
  workflow: 'preload' | 'load' | 'return';
}

export const useTourValidationState = ({ workflow }: UseTourValidationStateProps) => {
  const [stopValidationStates, setStopValidationStates] = useState<Record<string, boolean>>({});
  const [stopEquipmentStates, setStopEquipmentStates] = useState<
    Record<string, EquipmentValidationRecord>
  >({});
  const isInitializingRef = useRef(false);

  // Map workflow to persistence service supported types
  const persistenceWorkflow = workflow === 'return' ? 'load' : workflow;

  const loadValidationsForTour = useCallback(
    async (tour: ITourEntity) => {
      if (!tour.stops) {
        return;
      }

      isInitializingRef.current = true;

      try {
        const tourValidations = await loadingControlPersistenceService.getStopValidationsForTour(
          tour.id,
          persistenceWorkflow,
        );
        const validationStates: Record<string, boolean> = {};
        const equipmentStates: Record<string, EquipmentValidationRecord> = {};

        tour.stops.forEach((stop) => {
          const stopId = stop.id;
          const cachedValidation = tourValidations[stopId];

          if (cachedValidation?.equipmentItems) {
            // Convert array to Record if needed
            if (Array.isArray(cachedValidation.equipmentItems)) {
              const record = {
                [LogisticsEquipmentKind.PALLET]: {
                  initialQuantity: 0,
                  adjustedQuantity: 0,
                  isValidated: false,
                },
                [LogisticsEquipmentKind.ROLL]: {
                  initialQuantity: 0,
                  adjustedQuantity: 0,
                  isValidated: false,
                },
                [LogisticsEquipmentKind.PACKAGE]: {
                  initialQuantity: 0,
                  adjustedQuantity: 0,
                  isValidated: false,
                },
              } as EquipmentValidationRecord;
              cachedValidation.equipmentItems.forEach((item: EquipmentItemForValidationType) => {
                record[item.type] = {
                  receivedQuantity: item.receivedQuantity,
                  adjustedQuantity: item.adjustedQuantity,
                  isValidated: item.isValidated,
                };
              });
              equipmentStates[stopId] = record;
            } else {
              equipmentStates[stopId] =
                cachedValidation.equipmentItems as EquipmentValidationRecord;
            }
            validationStates[stopId] = cachedValidation.isValidated;
          } else {
            equipmentStates[stopId] = extractEquipmentFromCompletedStop(stop);
            validationStates[stopId] = false;
          }
        });

        setStopValidationStates(validationStates);
        setStopEquipmentStates(equipmentStates);
      } catch (error) {
        console.error(`Erreur lors du chargement des validations ${workflow}:`, error);
      } finally {
        setTimeout(() => {
          isInitializingRef.current = false;
        }, 100);
      }
    },
    [persistenceWorkflow],
  );

  const handleStopValidation = useCallback(
    async (stopId: string, tourId: string, isValid: boolean) => {
      if (isInitializingRef.current) {
        return;
      }

      setStopValidationStates((prev) => {
        if (prev[stopId] === isValid) {
          return prev;
        }

        return {
          ...prev,
          [stopId]: isValid,
        };
      });

      try {
        const equipmentRecord = stopEquipmentStates[stopId] || {
          [LogisticsEquipmentKind.PALLET]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
          [LogisticsEquipmentKind.ROLL]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
          [LogisticsEquipmentKind.PACKAGE]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
        };

        await loadingControlPersistenceService.saveStopValidation(
          stopId,
          tourId,
          isValid,
          equipmentRecord,
          persistenceWorkflow,
        );
      } catch (error) {
        console.error(`Erreur lors de la sauvegarde de la validation ${workflow}:`, error);
      }
    },
    [stopEquipmentStates, persistenceWorkflow],
  );

  const handleEquipmentChange = useCallback(
    async (stopId: string, tourId: string, equipmentRecord: EquipmentValidationRecord) => {
      if (isInitializingRef.current) {
        return;
      }

      const currentRecord = stopEquipmentStates[stopId];

      if (JSON.stringify(currentRecord) === JSON.stringify(equipmentRecord)) {
        return;
      }

      setStopEquipmentStates((prev) => ({
        ...prev,
        [stopId]: equipmentRecord,
      }));

      const hasValidatedItems = Object.values(equipmentRecord).some((item) => item.isValidated);

      setStopValidationStates((prev) => ({
        ...prev,
        [stopId]: hasValidatedItems,
      }));

      try {
        await loadingControlPersistenceService.saveStopValidation(
          stopId,
          tourId,
          hasValidatedItems,
          equipmentRecord,
          persistenceWorkflow,
        );
      } catch (error) {
        console.error(`Erreur lors de la sauvegarde des équipements ${workflow}:`, error);
      }
    },
    [stopEquipmentStates, persistenceWorkflow],
  );

  const handleToggleAllEquipments = useCallback(
    (stopId: string, tourId: string, isChecked: boolean) => {
      const currentRecord = stopEquipmentStates[stopId];

      if (!currentRecord) {
        return;
      }

      const updatedRecord: EquipmentValidationRecord = Object.fromEntries(
        Object.entries(currentRecord).map(([kind, data]) => [
          kind,
          { ...data, isValidated: isChecked },
        ]),
      ) as EquipmentValidationRecord;

      handleEquipmentChange(stopId, tourId, updatedRecord);
    },
    [stopEquipmentStates, handleEquipmentChange],
  );

  const areAllStopsValidatedForTour = useCallback(
    (tour: ITourEntity): boolean => {
      if (!tour.stops) {
        return false;
      }

      return tour.stops.every((stop) => stopValidationStates[stop.id] === true);
    },
    [stopValidationStates],
  );

  const calculateStopTotalWeight = useCallback((stop: IStopEntity): number => {
    let totalWeight = 0;

    if (stop.shipmentLines && stop.shipmentLines.length > 0) {
      stop.shipmentLines.forEach((shipmentLine) => {
        totalWeight += Number(shipmentLine.weightKg) || 0;
      });
    }

    return totalWeight;
  }, []);

  const getTotalEquipmentCount = useCallback(
    (equipmentItems: EquipmentItemForValidationType[]): number => {
      return equipmentItems.reduce((acc, item) => acc + item.adjustedQuantity, 0) || 0;
    },
    [],
  );

  const resetValidationsForTour = useCallback(
    async (tourId: string) => {
      try {
        await loadingControlPersistenceService.resetStopValidationsForTour(
          tourId,
          persistenceWorkflow,
        );

        // Reset local states
        setStopValidationStates({});
        setStopEquipmentStates({});
      } catch (error) {
        console.error(`Erreur lors du reset des validations ${workflow}:`, error);
      }
    },
    [persistenceWorkflow],
  );

  return {
    stopValidationStates,
    stopEquipmentStates,
    loadValidationsForTour,
    handleStopValidation,
    handleEquipmentChange,
    handleToggleAllEquipments,
    areAllStopsValidatedForTour,
    calculateStopTotalWeight,
    getTotalEquipmentCount,
    resetValidationsForTour,
  };
};
