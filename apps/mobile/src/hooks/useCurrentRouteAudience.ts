import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { UserRole } from '../interfaces/enum/user-role.enum';

export const useCurrentRouteAudience = (): UserRole.RECEPTIONIST | UserRole.DELIVERER => {
  const location = useLocation();
  const path = location.pathname;

  const routeAudience = useMemo(() => {
    if (path.includes('receptionist')) {
      return UserRole.RECEPTIONIST;
    }

    return UserRole.DELIVERER;
  }, [path]);

  return routeAudience;
};
