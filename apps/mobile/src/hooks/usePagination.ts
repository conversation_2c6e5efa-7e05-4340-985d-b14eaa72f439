import { useState } from 'react';
import { IPaginationMeta, IPaginationParams } from '../interfaces/pagination';

interface UsePaginationProps {
  initialParams?: IPaginationParams;
}

interface UsePaginationReturn {
  paginationParams: IPaginationParams;
  paginationMeta: IPaginationMeta | null;
  setPaginationMeta: (meta: IPaginationMeta) => void;
  handlePageChange: (newPage: number) => void;
  handleLimitChange: (newLimit: number) => void;
  handleSortChange: (field: string) => void;
}

export const usePagination = ({ initialParams }: UsePaginationProps = {}): UsePaginationReturn => {
  const [paginationParams, setIPaginationParams] = useState<IPaginationParams>({
    page: 1,
    limit: 10,
    sortBy: 'id',
    sortOrder: 'asc',
    ...initialParams,
  });

  const [paginationMeta, setPaginationMeta] = useState<IPaginationMeta | null>(null);

  const handlePageChange = (newPage: number) => {
    setIPaginationParams((prev: IPaginationParams) => ({
      ...prev,
      page: newPage,
    }));
  };

  const handleLimitChange = (newLimit: number) => {
    setIPaginationParams((prev: IPaginationParams) => ({
      ...prev,
      page: 1, // Reset to first page when changing limit
      limit: newLimit,
    }));
  };

  const handleSortChange = (field: string) => {
    setIPaginationParams((prev: IPaginationParams) => {
      // If clicking the same field, toggle sort order
      const newSortOrder = prev.sortBy === field && prev.sortOrder === 'asc' ? 'desc' : 'asc';

      return {
        ...prev,
        sortBy: field,
        sortOrder: newSortOrder,
      };
    });
  };

  return {
    paginationParams,
    paginationMeta,
    setPaginationMeta,
    handlePageChange,
    handleLimitChange,
    handleSortChange,
  };
};
