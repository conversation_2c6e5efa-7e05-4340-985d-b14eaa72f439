/* Tailwind CSS */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

@import "tailwindcss";

/* Force Light Mode - Disable Dark Mode */
:root {
  color-scheme: light;
}

/* Ionic Variables and Theming */

/* Core CSS required for Ionic components to work properly */
/* Basic CSS for apps built with Ionic */
/* Optional CSS utils that can be commented out */

/* Custom application styles can be added below */

ion-segment-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

ion-segment-button {
  --indicator-color: transparent !important;
  --color: #80868d !important;
  --color-checked: #1059a5 !important;
}

ion-button {
  --color: #1059a5 !important;
}

* {
  font-family: "Inter", sans-serif;
  font-weight: 700;
  font-style: normal;
  font-optical-sizing: auto;
  letter-spacing: 0.25px;
  line-height: 20px;
}

.primary-color {
  color: #1059a5;
}

.primary-bg {
  background-color: #1059a5;
}

.primary-button {
  background-color: #1059a5;
  color: white;
}

.primary-button-outline {
  background-color: white;
  color: #1059a5;
  border: 2px solid #1059a5;
}

.secondary-button {
  background-color: #6b7280;
  color: white;
}

.secondary-button-outline {
  background-color: transparent;
  color: #1059a5;
  border: 2px solid #1059a5;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
}
