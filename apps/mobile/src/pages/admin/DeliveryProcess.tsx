import { createSelector } from '@reduxjs/toolkit';
import { useParams } from 'react-router';
import { Loading } from '../../components/layout/Loading';
import { Page } from '../../components/layout/Page';
import { StopDeliveryProcess } from '../../components/services/deliverer/delivery-process-new/StopDeliveryProcess';
import { getToursWithQueueProjection } from '../../stores/tourSlice';
import { useAppSelector } from '../../utils/redux';

interface RouteParams {
  stopId: string;
}

const getStopById = (stopId: string) =>
  createSelector(getToursWithQueueProjection, (tours) => {
    return tours.flatMap((tour) => tour.stops).find((stop) => stop.id === stopId);
  });

const getTourById = (tourId: string) =>
  createSelector(getToursWithQueueProjection, (tours) => {
    return tours.find((tour) => tour.id === tourId);
  });

export function DeliveryProcessPage() {
  const { stopId } = useParams<RouteParams>();
  const stop = useAppSelector((state) => getStopById(stopId)(state));
  const tour = useAppSelector((state) => getTourById(stop?.tourId ?? '')(state));
  const isDataResolved = useAppSelector((state) => state.tour.isDataResolved);

  if (!isDataResolved) {
    return <Loading />;
  }

  if (!stop || !tour) {
    //TODO A styliser
    return <Page>Livraison non trouvée</Page>;
  }

  return (
    <Page>
      <StopDeliveryProcess stop={stop} tour={tour} />
    </Page>
  );
}
