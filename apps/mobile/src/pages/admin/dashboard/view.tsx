import React from 'react';
import { Redirect } from 'react-router';
import { useAppSelector } from '../../../utils/redux';
import { Loading } from '../../../components/layout/Loading';
import { UserRole } from '../../../interfaces/enum/user-role.enum';
import { PATHS } from '../../../config/routes';

export const DashboardView: React.FC = () => {
  // check the user role and redirect to the appropriate dashboard view
  const currentUser = useAppSelector((state) => state.currentUser);

  if (currentUser?.activeRole) {
    switch (currentUser.activeRole) {
      case UserRole.RECEPTIONIST:
        return <Redirect to={PATHS.DASHBOARD_RECEPTIONIST} />;
      case UserRole.DELIVERER:
        return <Redirect to={PATHS.DASHBOARD_DELIVERER} />;
      default:
        return <Loading />;
    }
  } else {
    // fallback if no active role is set, we check into currentUser roles array
    if (currentUser.user?.roles?.includes(UserRole.RECEPTIONIST)) {
      return <Redirect to={PATHS.DASHBOARD_RECEPTIONIST} />;
    } else if (currentUser.user?.roles?.includes(UserRole.DELIVERER)) {
      return <Redirect to={PATHS.DASHBOARD_DELIVERER} />;
    } else {
      return <Loading />;
    }
  }
};
