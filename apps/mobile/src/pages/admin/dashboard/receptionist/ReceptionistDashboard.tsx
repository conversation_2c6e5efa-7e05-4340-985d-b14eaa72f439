import { Package, Truck } from 'lucide-react';
import { useHistory } from 'react-router-dom';
import { Page } from '../../../../components/layout/Page';

const ReceptionistDashboard: React.FC = () => {
  const history = useHistory();

  return (
    <Page>
      <div className="flex flex-col items-center justify-center h-full bg-gray-50 p-6">
        <h2 className="text-3xl font-bold mb-12 text-gray-800">Choisis<PERSON>z le flux</h2>

        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          <button
            className="w-48 h-48 flex flex-col items-center justify-center bg-blue-500 hover:bg-blue-600 text-white rounded-3xl shadow-xl transition-all duration-200 transform hover:scale-105 active:scale-95"
            onClick={() => history.push('/admin/dashboard/receptionist/preload')}
          >
            <Package size={56} className="mb-4" strokeWidth={1.5} />
            <span className="text-lg font-semibold text-center leading-tight">
              Préchargement
              <br />
              camion
            </span>
          </button>

          <button
            className="w-48 h-48 flex flex-col items-center justify-center bg-green-500 hover:bg-green-600 text-white rounded-3xl shadow-xl transition-all duration-200 transform hover:scale-105 active:scale-95"
            onClick={() => history.push('/admin/dashboard/receptionist/return-control')}
          >
            <Truck size={56} className="mb-4" strokeWidth={1.5} />
            <span className="text-lg font-semibold text-center leading-tight">
              Contrôle
              <br />
              retour camion
            </span>
          </button>
        </div>
      </div>
    </Page>
  );
};

export default ReceptionistDashboard;
