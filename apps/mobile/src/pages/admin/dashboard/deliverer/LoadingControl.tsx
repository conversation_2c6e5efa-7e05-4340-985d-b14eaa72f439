import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Loading } from '../../../../components/layout/Loading';
import { Page } from '../../../../components/layout/Page';
import { LoadingControlTourList } from '../../../../components/services/deliverer/loading-control/LoadingControlTourList';
import { TourDeliverService } from '../../../../services';
import { delivererLoadingControlActions } from '../../../../stores/delivererLoadingControlSlice';
import { getToursWithQueueProjection } from '../../../../stores/tourSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';

export const DelivererLoadingControlPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const history = useHistory();
  const [isLoading, setIsLoading] = useState(true);
  const globalTourArray = useAppSelector(getToursWithQueueProjection);
  const isGlobalTourDataResolved = useAppSelector((state) => state.tour.isDataResolved);

  useEffect(() => {
    if (!isGlobalTourDataResolved) {
      return;
    }

    const hasTourEligibleToLoad = globalTourArray.some((tour) =>
      TourDeliverService.isTourEligibleToLoad(tour),
    );

    if (!hasTourEligibleToLoad && globalTourArray.length > 0) {
      history.push('/admin/deliverer/tours');
    }

    dispatch(delivererLoadingControlActions.setTours(globalTourArray));

    setIsLoading(false);
  }, [dispatch, isGlobalTourDataResolved, globalTourArray]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <Page>
      <LoadingControlTourList />
    </Page>
  );
};
