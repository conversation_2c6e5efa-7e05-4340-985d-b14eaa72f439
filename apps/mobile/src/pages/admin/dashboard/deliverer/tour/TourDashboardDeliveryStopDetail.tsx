import { IonIcon } from '@ionic/react';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../../../interfaces/entity/i-tour-entity';
import {
  documentTextOutline,
  scaleOutline,
  cubeOutline,
  businessOutline,
  timeOutline,
} from 'ionicons/icons';
import { formatDateWithWeekday } from '../../../../../utils/dateUtils';

interface TourDashboardDeliveryStopDetailProps {
  tour: ITourEntity;
  stop: IStopEntity;
}

export default function TourDashboardDeliveryStopDetail({
  tour,
  stop,
}: TourDashboardDeliveryStopDetailProps) {
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'En attente';
      case 'COMPLETED':
        return 'Terminé';
      case 'FAILED':
        return 'Échec';
      default:
        return status;
    }
  };

  return (
    <div className="p-6 space-y-8 font-sans">
      {/* En-tête avec informations tournée */}
      <div className="space-y-2 pb-6 border-b border-gray-200">
        <h1 className="text-2xl font-bold primary-color">Tournée {tour.tourIdentifier.number}</h1>
        <p className="text-base text-gray-600 font-medium">
          {formatDateWithWeekday(tour.deliveryDate)} • Arrêt n°
          {stop.sequenceInTour + 1}
        </p>
        {stop.completion && (
          <p className="text-lg font-bold primary-color">
            Statut: {getStatusLabel(stop.completion.deliveryStatus)}
          </p>
        )}
      </div>

      {/* Informations Client */}
      <div className="space-y-4 pb-6 border-b border-gray-200">
        <h2 className="text-xl font-bold flex items-center primary-color">
          <IonIcon icon={businessOutline} className="mr-3 primary-color" />
          Informations Client
        </h2>

        <div className="space-y-3 ">
          <div>
            <h3 className="font-bold text-xl text-gray-900">{stop.originalClientInfo.name}</h3>
            <p className="text-base text-gray-600 font-medium">
              Code client: {stop.originalClientInfo.code}
            </p>
          </div>

          <div className="flex items-start">
            <div className="space-y-1">
              <p className="text-base font-medium text-gray-800">
                {stop.originalClientInfo.address.line1}
              </p>
              {stop.originalClientInfo.address.line2 && (
                <p className="text-base font-medium text-gray-800">
                  {stop.originalClientInfo.address.line2}
                </p>
              )}
              <p className="text-base font-bold text-gray-900">
                {stop.originalClientInfo.address.line4}
              </p>
            </div>
          </div>

          {stop.sortingCode && (
            <p className="text-base font-medium text-gray-800">
              <span className="font-bold primary-color">Code de tri:</span>
              <span className="ml-2 font-mono primary-color">{stop.sortingCode.toUpperCase()}</span>
            </p>
          )}
        </div>
      </div>

      {/* Informations Expédition */}
      {stop.shipmentLines && stop.shipmentLines.length > 0 && (
        <div className="space-y-4 pb-6 border-b border-gray-200">
          <h2 className="text-xl font-bold flex items-center primary-color">
            <IonIcon icon={cubeOutline} className="mr-3 primary-color" />
            Marchandises à livrer
          </h2>

          <div className="space-y-6">
            {stop.shipmentLines.map((shipment, index) => (
              <div key={index} className="space-y-3">
                <div>
                  <h4 className="font-bold text-lg primary-color">{shipment.shipperName}</h4>
                  <p className="text-base text-gray-600 font-medium">
                    {shipment.operation === 'UNLOAD' ? 'Déchargement' : shipment.operation}
                    {shipment.isFrozen && ' • Produits congelés'}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {shipment.weightKg && (
                    <div className="flex items-center">
                      <IonIcon icon={scaleOutline} className="mr-2 text-gray-600" size="small" />
                      <span className="text-base font-bold text-gray-800">
                        {shipment.weightKg} kg
                      </span>
                    </div>
                  )}
                  {shipment.palletCount && (
                    <div className="flex items-center">
                      <IonIcon icon={cubeOutline} className="mr-2 text-gray-600" size="small" />
                      <span className="text-base font-bold text-gray-800">
                        {shipment.palletCount} palette
                        {shipment.palletCount > 1 ? 's' : ''}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Documents de livraison */}
      {stop.deliveryNotes && stop.deliveryNotes.length > 0 && (
        <div
          className={`space-y-4 ${stop.deliveryTimeWindow ? 'pb-6 border-b border-gray-200' : ''}`}
        >
          <h2 className="text-xl font-bold flex items-center primary-color">
            <IonIcon icon={documentTextOutline} className="mr-3 primary-color" />
            Documents de livraison
          </h2>

          <div className="space-y-3">
            {stop.deliveryNotes.map((note, index) => (
              <div key={index}>
                <h3 className="text-base font-bold primary-color">{note.filename}</h3>
                <p className="text-sm text-gray-600 font-medium">Bon de livraison</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Fenêtre de livraison si disponible */}
      {stop.deliveryTimeWindow && (
        <div className="space-y-4">
          <h2 className="text-xl font-bold flex items-center primary-color">
            <IonIcon icon={timeOutline} className="mr-3 primary-color" />
            Créneau de livraison
          </h2>

          <div>
            <p className="text-base font-medium text-gray-800">{stop.deliveryTimeWindow}</p>
          </div>
        </div>
      )}
    </div>
  );
}
