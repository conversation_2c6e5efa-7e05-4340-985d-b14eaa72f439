import React from 'react';
import { Snowflake } from 'lucide-react';
import TruncatedText from '../../../../../components/ui/stylized/TruncatedText';
import { isStopCompleted } from '../../../../../components/services/utils';
import { IAddress } from '../../../../../interfaces/entity/i-address';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../../../interfaces/entity/i-tour-entity';

interface TourDashboardDeliveryStopListProps {
  tour: ITourEntity;
  onStopClick: (stop: IStopEntity) => void;
}

const TourDashboardDeliveryStopList: React.FC<TourDashboardDeliveryStopListProps> = ({
  tour,
  onStopClick,
}) => {
  const formatAddress = (address: IAddress) => {
    console.log('🎯 [TourDashboardDeliveryStopList] formatAddress', address);
    let formattedAddress = '';

    if (address?.line1) {
      formattedAddress += address.line1;
    }

    if (address?.line2) {
      formattedAddress += `, ${address.line2}`;
    }

    if (address?.line3) {
      formattedAddress += `, ${address.line3}`;
    }

    if (address?.line4) {
      formattedAddress += `, ${address.line4}`;
    }

    return formattedAddress;
  };

  const checkIfShiplinesIsFrozen = (stop: IStopEntity) => {
    return stop.shipmentLines.some((shipmentLine) => shipmentLine?.isFrozen);
  };

  return (
    <div className="flex flex-col gap-3 sm:gap-4 md:gap-6 mt-6 sm:mt-8 md:mt-10 w-full h-full">
      {tour.stops.map((stop) => {
        const isCompleted = isStopCompleted(stop);

        return (
          <div
            className={`flex w-full p-3 sm:p-4 md:p-5 border border-gray-200 shadow-md rounded-lg cursor-pointer hover:shadow-lg transition-shadow ${
              isCompleted ? 'bg-green-50 border-green-300' : ''
            }`}
            key={stop.id}
            onClick={() => onStopClick(stop)}
          >
            <div className="flex flex-col gap-1 sm:gap-2 w-full h-full">
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 justify-between w-full">
                <div className="flex flex-col gap-1 sm:gap-2">
                  <TruncatedText
                    text={stop.originalClientInfo?.name?.toUpperCase() || ''}
                    maxLength={15}
                    className={`font-bold text-base sm:text-lg md:text-xl lg:text-2xl ${isCompleted ? 'text-green-600' : 'primary-color'}`}
                  />
                  <p className="text-xs sm:text-sm md:text-base font-semibold text-neutral-500">
                    {formatAddress(stop.originalClientInfo?.address)}
                  </p>
                </div>
                <div className="flex flex-row gap-2 sm:gap-3 items-center justify-start sm:justify-end  sm:mt-0">
                  {checkIfShiplinesIsFrozen(stop) && (
                    <div className="flex flex-row gap-1 sm:gap-2 items-center text-sm sm:text-base md:text-lg">
                      <Snowflake className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" color="#1059A5" />
                      <span className="font-semibold primary-color text-xs sm:text-sm md:text-base">
                        SURGELÉ
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TourDashboardDeliveryStopList;
