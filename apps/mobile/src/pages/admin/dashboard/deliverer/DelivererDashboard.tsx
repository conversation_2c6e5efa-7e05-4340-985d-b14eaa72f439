import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonLabel,
  IonModal,
  IonPage,
  IonSegment,
  IonSegmentButton,
  IonSegmentContent,
  IonSegmentView,
  IonTitle,
  IonToolbar,
  SegmentChangeEventDetail,
} from '@ionic/react';
import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { X } from 'lucide-react';
import Button from '../../../../components/ui/Button';
import TruncatedText from '../../../../components/ui/stylized/TruncatedText';
import { PATHS } from '../../../../config/routes';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { TourStatus } from '../../../../interfaces/enum/tour.enums';
import { getToursWithQueueProjection } from '../../../../stores/tourSlice';
import { formatDate } from '../../../../utils/dateUtils';
import { useAppSelector } from '../../../../utils/redux';
import TourDashboardDeliveryStopDetail from './tour/TourDashboardDeliveryStopDetail';
import TourDashboardDeliveryStopList from './tour/TourDashboardDeliveryStopList';

const DelivererDashboard: React.FC = () => {
  const history = useHistory();

  const tours = useAppSelector(getToursWithQueueProjection);
  const isLoading = useAppSelector((state) => !state.tour.isDataResolved);

  const currentUser = useAppSelector((state) => state.currentUser.user);

  const [selectedStop, setSelectedStop] = useState<IStopEntity | null>(null);
  const [selectedTour, setSelectedTour] = useState<ITourEntity | null>(null);

  useEffect(() => {
    if (selectedTour) {
      return;
    }

    if (tours.length > 0) {
      setSelectedTour(tours[0]);
    }
  }, [tours, selectedTour]);

  const handleStopClick = (stop: IStopEntity) => {
    setSelectedStop(stop);
  };

  async function handleStartTour() {
    const plannedTours = tours.filter((tour) => tour.status === TourStatus.Planned);

    if (plannedTours.length > 0) {
      history.push(PATHS.LOADING_CONTROL);
    } else {
      history.push(PATHS.TOURS);
    }
  }

  const handleSegmentChange = (e: CustomEvent<SegmentChangeEventDetail>) => {
    const tour = tours.find((tour) => tour.id === e.detail.value);

    if (tour) {
      setSelectedTour(tour);
    }
  };

  if (isLoading) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Tableau de bord</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent fullscreen>
          <div className=" bg-gray-50 h-full mx-1 overflow-auto pt-4 sm:pt-6 md:pt-8 lg:pt-10 pb-10 sm:pb-12 md:pb-14 lg:pb-16">
            <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
              <div className="my-2 sm:my-4 mb-6 sm:mb-8 md:mb-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4">
                <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-500 dark:text-neutral-300">
                  Bonjour {currentUser?.firstName || '--'}
                </p>
              </div>
            </div>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Tableau de bord</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        <div className=" bg-gray-50 h-full mx-1 overflow-auto pt-4 sm:pt-6 md:pt-8 lg:pt-10 pb-10 sm:pb-12 md:pb-14 lg:pb-16">
          <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
            <div className="my-2 sm:my-4 mb-6 sm:mb-8 md:mb-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4">
              <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-500 dark:text-neutral-300">
                Bonjour {currentUser?.firstName || '--'}
              </p>
              <div className="flex items-center gap-4">
                <p className="text-sm sm:text-base md:text-lg lg:text-xl font-bold text-neutral-500 dark:text-neutral-300">
                  le {formatDate()}
                </p>
              </div>
            </div>
            <div className="mb-4 sm:mb-6 max-h-[70vh] flex-1 rounded-xl ">
              <IonSegment
                scrollable={true}
                value={selectedTour?.id}
                onIonChange={handleSegmentChange}
                className="bg-white rounded-lg shadow-sm h-16"
              >
                {tours.map((tour, index) => (
                  <>
                    <IonSegmentButton
                      key={tour.id}
                      value={tour.id}
                      className={`border-neutral-200 border-r-2 border-l-2`}
                    >
                      <IonLabel>N°{tour.tourIdentifier.originalNumber}</IonLabel>
                    </IonSegmentButton>
                  </>
                ))}
              </IonSegment>

              {tours.length > 0 && (
                <IonSegmentView className="max-h-[calc(100vh-400px)] sm:max-h-[calc(100vh-450px)] md:max-h-[calc(100vh-500px)] lg:max-h-[calc(100vh-550px)] overflow-y-auto mt-2 sm:mt-4">
                  {selectedTour && (
                    <IonSegmentContent key={selectedTour.id} id={selectedTour.id}>
                      <TourDashboardDeliveryStopList
                        tour={selectedTour}
                        key={selectedTour.id}
                        onStopClick={handleStopClick}
                      />
                    </IonSegmentContent>
                  )}
                </IonSegmentView>
              )}
              {tours.length === 0 && (
                <IonSegmentView className="max-h-[calc(100vh-400px)] sm:max-h-[calc(100vh-450px)] md:max-h-[calc(100vh-500px)] lg:max-h-[calc(100vh-550px)] overflow-y-auto mt-2 sm:mt-4">
                  <IonLabel className="text-neutral font-bold text-sm sm:text-base md:text-lg m-1 rounded-lg text-center">
                    Aucune tournée disponible
                  </IonLabel>
                </IonSegmentView>
              )}

              <Button
                children={
                  <span className="font-bold text-sm sm:text-base md:text-lg mt-1 sm:mt-2 text-nowrap primary-color">
                    {tours.length > 1 ? 'Démarrer les tournées' : 'Commencer la tournée'}
                  </span>
                }
                onClick={handleStartTour}
                fill="clear"
                className="mt-1 sm:mt-2 font-bold p-0 h-fit border-2 sm:border-3 border-[#1059A5] mx-2 sm:mx-4 rounded-full px-3 sm:px-4 py-3 sm:py-4 shadow-none"
              ></Button>
            </div>
          </div>
          <IonModal isOpen={!!selectedStop} onDidDismiss={() => setSelectedStop(null)}>
            <IonHeader className="shadow-none border-none">
              <IonToolbar>
                <IonTitle>
                  <TruncatedText
                    text={selectedStop?.originalClientInfo?.name?.toUpperCase() || ''}
                    className="text-lg sm:text-xl md:text-2xl font-bold mx-4 sm:mx-6 md:mx-8 p-1 sm:p-2 py-2 sm:py-3 md:py-4 primary-color"
                    maxLength={30}
                  />
                </IonTitle>
                <IonButtons slot="end">
                  <IonButton className="p-4 sm:p-6 md:p-8" onClick={() => setSelectedStop(null)}>
                    <X className="primary-color h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8" />
                  </IonButton>
                </IonButtons>
              </IonToolbar>
            </IonHeader>
            <IonContent className="ion-padding">
              {selectedStop && selectedTour && (
                <TourDashboardDeliveryStopDetail
                  tour={selectedTour}
                  stop={selectedStop}
                  key={selectedStop.id}
                />
              )}
            </IonContent>
          </IonModal>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default DelivererDashboard;
