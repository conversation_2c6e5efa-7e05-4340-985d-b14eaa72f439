import {
  IonAlert,
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonSelect,
  IonSelectOption,
  IonSpinner,
  IonToast,
  IonToggle,
} from '@ionic/react';
import {
  closeCircleOutline,
  cloudOfflineOutline,
  helpOutline,
  languageOutline,
  lockClosedOutline,
  logOutOutline,
  moonOutline,
  notificationsOutline,
  refreshOutline,
  personOutline,
  trashOutline,
} from 'ionicons/icons';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { PATHS } from '../../config/routes';
import { useAppSelector, useAppDispatch } from '../../utils/redux';
import { resetStuckDownloads } from '../../stores/deliveryNotesSlice';
import { deliveryNoteCleanupService } from '../../services/DeliveryNoteCleanupService';

const Settings: React.FC = () => {
  const history = useHistory();
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector((state) => state.currentUser.user);
  const downloadQueue = useAppSelector((state) => state.deliveryNotes.downloadQueue);

  // Settings states
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('en');
  const [notifications, setNotifications] = useState(true);
  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [offlineStats, setOfflineStats] = useState<{
    count: number;
    size: string;
  }>({
    count: 0,
    size: '0 MB',
  });
  const [showClearAlert, setShowClearAlert] = useState(false);

  // Load settings from local storage
  useEffect(() => {
    const loadSettings = async () => {
      const savedDarkMode = localStorage.getItem('darkMode') === 'true';
      const savedLanguage = localStorage.getItem('language') || 'en';
      const savedNotifications = localStorage.getItem('notifications') !== 'false';

      setDarkMode(savedDarkMode);
      setLanguage(savedLanguage);
      setNotifications(savedNotifications);

      // Apply dark mode if needed
      if (savedDarkMode) {
        document.body.classList.add('dark');
      } else {
        document.body.classList.remove('dark');
      }

      // Load storage stats
      try {
        const stats = await deliveryNoteCleanupService.getStorageStats();
        setOfflineStats({
          count: stats.totalFiles,
          size: `${(stats.totalSizeBytes / (1024 * 1024)).toFixed(2)} MB`,
        });
      } catch (error) {
        console.error('Failed to load storage stats:', error);
      }
    };

    loadSettings();
  }, []);

  // Handle setting changes
  const handleDarkModeChange = (checked: boolean) => {
    setDarkMode(checked);
    localStorage.setItem('darkMode', checked.toString());

    if (checked) {
      document.body.classList.add('dark');
    } else {
      document.body.classList.remove('dark');
    }
  };

  const handleLanguageChange = (value: string) => {
    setLanguage(value);
    localStorage.setItem('language', value);
    setToastMessage('Language setting saved. Some changes may require a restart.');
    setShowToast(true);
  };

  const handleNotificationsChange = (checked: boolean) => {
    setNotifications(checked);
    localStorage.setItem('notifications', checked.toString());
  };

  const handleLogout = async () => {
    setLoading(true);
    try {
      // Implement logout logic here
      history.push(PATHS.LOGIN);
    } catch (error) {
      console.error('Logout error:', error);
      setToastMessage('Failed to log out. Please try again.');
      setShowToast(true);
    } finally {
      setLoading(false);
    }
  };

  const handleClearOfflineStorage = async () => {
    try {
      const stats = await deliveryNoteCleanupService.cleanupOldDeliveryNotes();
      await deliveryNoteCleanupService.cleanupReduxState(dispatch);

      setOfflineStats({ count: 0, size: '0 MB' });
      setToastMessage(
        stats.deletedFolders.length > 0
          ? `Cleared ${stats.deletedFiles} files from ${stats.deletedFolders.length} old folders`
          : 'No old files to clear',
      );
      setShowToast(true);

      // Reload storage stats
      setTimeout(async () => {
        const newStats = await deliveryNoteCleanupService.getStorageStats();
        setOfflineStats({
          count: newStats.totalFiles,
          size: `${(newStats.totalSizeBytes / (1024 * 1024)).toFixed(2)} MB`,
        });
      }, 1000);
    } catch (error) {
      console.error('Failed to clear offline storage:', error);
      setToastMessage('Failed to clear offline storage');
      setShowToast(true);
    }
  };

  const handleResetStuckDownloads = () => {
    const stuckCount = downloadQueue.filter((item) => item.status === 'downloading').length;

    if (stuckCount > 0) {
      dispatch(resetStuckDownloads());
      setToastMessage(`Reset ${stuckCount} stuck download${stuckCount > 1 ? 's' : ''}`);
      setShowToast(true);
    } else {
      setToastMessage('No stuck downloads found');
      setShowToast(true);
    }
  };

  return (
    <div
      style={{ paddingTop: '80px', paddingBottom: '60px' }}
      className="ion-padding bg-gray-50 h-full overflow-auto"
    >
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-4">Settings</h1>

        {/* User Profile Card */}
        <IonCard className="mb-6">
          <IonCardHeader>
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-2xl font-bold text-blue-600">
                  {currentUser
                    ? `${currentUser?.firstName?.[0] || ''}${currentUser?.lastName?.[0] || ''}`
                    : '--'}
                </span>
              </div>
              <div>
                <h2 className="text-xl font-semibold">
                  {currentUser
                    ? `${currentUser?.firstName || '--'} ${currentUser?.lastName || '--'}`
                    : '--'}
                </h2>
                <p className="text-gray-500">{currentUser?.email || '--'}</p>
              </div>
            </div>
          </IonCardHeader>
          <IonCardContent>
            <IonButton expand="block" fill="outline" color="primary" className="mt-2">
              <IonIcon slot="start" icon={personOutline} />
              Edit Profile
            </IonButton>
          </IonCardContent>
        </IonCard>

        {/* Settings List */}
        <IonList className="bg-transparent rounded-lg overflow-hidden">
          <div className="bg-gray-100 px-4 py-2 font-semibold">Appearance</div>

          {/* Dark Mode */}
          <IonItem className="py-2">
            <IonIcon icon={moonOutline} slot="start" className="text-purple-600" />
            <IonLabel>Dark Mode</IonLabel>
            <IonToggle
              checked={darkMode}
              onIonChange={(e) => handleDarkModeChange(e.detail.checked)}
              slot="end"
            />
          </IonItem>

          {/* Language */}
          <IonItem className="py-2">
            <IonIcon icon={languageOutline} slot="start" className="text-green-600" />
            <IonLabel>Language</IonLabel>
            <IonSelect
              value={language}
              onIonChange={(e) => handleLanguageChange(e.detail.value)}
              interface="popover"
            >
              <IonSelectOption value="en">English</IonSelectOption>
              <IonSelectOption value="fr">Français</IonSelectOption>
              <IonSelectOption value="es">Español</IonSelectOption>
              <IonSelectOption value="de">Deutsch</IonSelectOption>
            </IonSelect>
          </IonItem>

          <div className="bg-gray-100 px-4 py-2 font-semibold mt-4">Notifications</div>

          {/* Notifications */}
          <IonItem className="py-2">
            <IonIcon icon={notificationsOutline} slot="start" className="text-blue-600" />
            <IonLabel>Enable Notifications</IonLabel>
            <IonToggle
              checked={notifications}
              onIonChange={(e) => handleNotificationsChange(e.detail.checked)}
              slot="end"
            />
          </IonItem>

          <div className="bg-gray-100 px-4 py-2 font-semibold mt-4">Security</div>

          {/* Change Password */}
          <IonItem button detail className="py-2">
            <IonIcon icon={lockClosedOutline} slot="start" className="text-red-600" />
            <IonLabel>Change Password</IonLabel>
          </IonItem>

          <div className="bg-gray-100 px-4 py-2 font-semibold mt-4">Offline Storage</div>

          {/* Offline Storage Info */}
          <IonItem className="py-2">
            <IonIcon icon={cloudOfflineOutline} slot="start" className="text-indigo-600" />
            <IonLabel>
              <h3>Offline Delivery Notes</h3>
              <p className="text-sm text-gray-500">
                {offlineStats.count} files ({offlineStats.size})
              </p>
            </IonLabel>
            <IonButton
              fill="clear"
              color="danger"
              slot="end"
              onClick={() => setShowClearAlert(true)}
              disabled={offlineStats.count === 0}
            >
              <IonIcon icon={trashOutline} />
            </IonButton>
          </IonItem>

          {/* Reset Stuck Downloads */}
          <IonItem className="py-2" button onClick={handleResetStuckDownloads}>
            <IonIcon icon={refreshOutline} slot="start" className="text-orange-500" />
            <IonLabel>
              <h3>Reset Stuck Downloads</h3>
              <p className="text-sm text-gray-500">
                {downloadQueue.filter((item) => item.status === 'downloading').length > 0
                  ? `${downloadQueue.filter((item) => item.status === 'downloading').length} stuck download(s)`
                  : 'No stuck downloads'}
              </p>
            </IonLabel>
          </IonItem>

          <div className="bg-gray-100 px-4 py-2 font-semibold mt-4">Support</div>

          {/* Help & Support */}
          <IonItem button detail className="py-2" routerLink={PATHS.HELP}>
            <IonIcon icon={helpOutline} slot="start" className="text-orange-600" />
            <IonLabel>Help & Support</IonLabel>
          </IonItem>
        </IonList>

        {/* Logout Button */}
        <div className="mt-6">
          <IonButton expand="block" color="danger" onClick={handleLogout} disabled={loading}>
            {loading ? (
              <IonSpinner name="dots" />
            ) : (
              <>
                <IonIcon slot="start" icon={logOutOutline} />
                Log Out
              </>
            )}
          </IonButton>
        </div>

        {/* Version Info */}
        <div className="text-center text-gray-500 text-xs mt-6">
          <p>Scanner App v1.0.0</p>
          <p>© 2025 Scanner App. All rights reserved.</p>
        </div>

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          position="bottom"
          buttons={[
            {
              icon: closeCircleOutline,
              role: 'cancel',
            },
          ]}
        />

        <IonAlert
          isOpen={showClearAlert}
          onDidDismiss={() => setShowClearAlert(false)}
          header="Clear Old Delivery Notes"
          message={`This will delete all delivery notes from previous days, keeping only today's files. Currently ${offlineStats.count} files stored. Continue?`}
          buttons={[
            {
              text: 'Cancel',
              role: 'cancel',
            },
            {
              text: 'Clear',
              handler: handleClearOfflineStorage,
              cssClass: 'text-danger',
            },
          ]}
        />
      </div>
    </div>
  );
};

export default Settings;
