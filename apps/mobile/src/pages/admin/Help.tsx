import {
  IonButton,
  IonIcon,
  IonCard,
  IonCardHeader,
  IonCardContent,
  IonItem,
  IonLabel,
  IonSearchbar,
  IonAccordionGroup,
  IonAccordion,
  IonBadge,
  IonList,
} from '@ionic/react';
import {
  helpCircleOutline,
  documentTextOutline,
  chatbubbleEllipsesOutline,
  schoolOutline,
  bulbOutline,
  videocamOutline,
  ellipsisHorizontal,
  checkmarkCircleOutline,
  chevronForward,
} from 'ionicons/icons';
import { useHistory, useLocation } from 'react-router-dom';
import { useState } from 'react';
import { getRouteTitle, PATHS } from '../../config/routes';

const Help: React.FC = () => {
  const location = useLocation();
  const pageTitle = getRouteTitle(location.pathname);
  const [searchText, setSearchText] = useState('');

  const history = useHistory();

  return (
    <div
      style={{ paddingTop: '60px', paddingBottom: '60px' }}
      className="ion-padding bg-gray-50 h-full overflow-auto"
    >
      <div className="max-w-4xl mx-auto pt-4">
        <div className="flex items-center mb-6">
          <IonButton
            fill="clear"
            onClick={() => history.push(PATHS.SETTINGS)}
            className="text-gray-600"
          >
            &larr; Retour aux paramètres
          </IonButton>
        </div>
        <div className="my-4">
          <h1 className="text-4xl font-bold mb-4">{pageTitle}</h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-300">
            Find answers and learn how to use the platform
          </p>
        </div>

        {/* Search Section */}
        <div className="mb-6 p-6 bg-white rounded-xl shadow-md">
          <h2 className="text-xl font-bold mb-3 text-center">How can we help you?</h2>
          <IonSearchbar
            value={searchText}
            onIonChange={(e) => setSearchText(e.detail.value!)}
            placeholder="Search for help topics"
            className="mb-2"
          />
        </div>

        {/* Quick Help Cards */}
        <div className="grid md:grid-cols-2 gap-4 mb-6">
          <IonCard className="shadow-md">
            <IonCardHeader className="flex items-center">
              <IonIcon
                size="large"
                icon={chatbubbleEllipsesOutline}
                className="text-blue-600 mr-2"
              />
              <h3 className="font-semibold text-gray-700">Contact Support</h3>
            </IonCardHeader>
            <IonCardContent>
              <p className="text-gray-600 mb-4">
                Need personal assistance? Our support team is here to help you.
              </p>
              <div className="flex space-x-3">
                <IonButton fill="outline" color="medium">
                  Live Chat
                </IonButton>
                <IonButton color="primary">Open Ticket</IonButton>
              </div>
            </IonCardContent>
          </IonCard>

          <IonCard className="shadow-md">
            <IonCardHeader className="flex items-center">
              <IonIcon size="large" icon={videocamOutline} className="text-red-600 mr-2" />
              <h3 className="font-semibold text-gray-700">Video Tutorials</h3>
            </IonCardHeader>
            <IonCardContent>
              <p className="text-gray-600 mb-4">Learn by watching our step-by-step video guides.</p>
              <IonButton expand="block" color="primary">
                Browse Tutorials
              </IonButton>
            </IonCardContent>
          </IonCard>
        </div>

        {/* Frequently Asked Questions */}
        <IonCard className="shadow-md mb-6">
          <IonCardHeader className="flex items-center">
            <IonIcon size="large" icon={helpCircleOutline} className="text-yellow-600 mr-2" />
            <h3 className="font-semibold text-gray-700">Frequently Asked Questions</h3>
          </IonCardHeader>
          <IonCardContent>
            <IonAccordionGroup>
              <IonAccordion value="first">
                <IonItem slot="header" lines="none" className="my-1">
                  <IonLabel>How do I change my password?</IonLabel>
                </IonItem>
                <div slot="content" className="p-4 bg-gray-50">
                  <p className="text-gray-700">
                    To change your password, go to the Profile page, then click on the "Security"
                    section. You'll find a "Change Password" option where you can update your
                    credentials.
                  </p>
                </div>
              </IonAccordion>

              <IonAccordion value="second">
                <IonItem slot="header" lines="none" className="my-1">
                  <IonLabel>How to set up two-factor authentication?</IonLabel>
                </IonItem>
                <div slot="content" className="p-4 bg-gray-50">
                  <p className="text-gray-700">
                    Two-factor authentication adds an extra layer of security. Navigate to Profile /
                    Security, then enable the two-factor authentication toggle. Follow the on-screen
                    instructions to link your account with an authenticator app.
                  </p>
                </div>
              </IonAccordion>

              <IonAccordion value="third">
                <IonItem slot="header" lines="none" className="my-1">
                  <IonLabel>Can I export my data?</IonLabel>
                </IonItem>
                <div slot="content" className="p-4 bg-gray-50">
                  <p className="text-gray-700">
                    Yes, you can export your data. Go to Settings / Privacy & Data, then scroll down
                    to find the "Export Data" option. You can choose between various formats like
                    CSV or JSON.
                  </p>
                </div>
              </IonAccordion>

              <IonAccordion value="fourth">
                <IonItem slot="header" lines="none" className="my-1">
                  <IonLabel>What happens if I delete my account?</IonLabel>
                </IonItem>
                <div slot="content" className="p-4 bg-gray-50">
                  <p className="text-gray-700">
                    Deleting your account will permanently remove all your data from our servers.
                    This action cannot be undone. Before deletion, we recommend exporting any
                    important data.
                  </p>
                </div>
              </IonAccordion>
            </IonAccordionGroup>

            <div className="flex justify-center mt-4">
              <IonButton fill="clear" color="medium">
                View All FAQs
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>

        {/* Documentation */}
        <IonCard className="shadow-md mb-6">
          <IonCardHeader className="flex items-center">
            <IonIcon size="large" icon={documentTextOutline} className="text-green-600 mr-2" />
            <h3 className="font-semibold text-gray-700">Documentation</h3>
          </IonCardHeader>
          <IonCardContent>
            <IonList className="bg-transparent">
              <IonItem button detail={true} lines="full" className="my-1">
                <IonIcon slot="start" icon={bulbOutline} className="text-blue-500" />
                <IonLabel>
                  <h2>Getting Started Guide</h2>
                  <p className="text-sm text-gray-500">Learn the basics of the platform</p>
                </IonLabel>
                <IonBadge color="success" slot="end">
                  Beginner
                </IonBadge>
              </IonItem>

              <IonItem button detail={true} lines="full" className="my-1">
                <IonIcon slot="start" icon={schoolOutline} className="text-purple-500" />
                <IonLabel>
                  <h2>Advanced Features</h2>
                  <p className="text-sm text-gray-500">Unlock the full potential</p>
                </IonLabel>
                <IonBadge color="warning" slot="end">
                  Advanced
                </IonBadge>
              </IonItem>

              <IonItem button detail={true} lines="none" className="my-1">
                <IonIcon slot="start" icon={checkmarkCircleOutline} className="text-green-500" />
                <IonLabel>
                  <h2>Best Practices</h2>
                  <p className="text-sm text-gray-500">Tips for optimal usage</p>
                </IonLabel>
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Community Forums */}
        <IonCard className="shadow-md">
          <IonCardHeader className="flex items-center">
            <IonIcon size="large" icon={ellipsisHorizontal} className="text-purple-600 mr-2" />
            <h3 className="font-semibold text-gray-700">Community Forums</h3>
          </IonCardHeader>
          <IonCardContent>
            <p className="text-gray-600 mb-4">
              Connect with other users, share ideas, and find solutions in our community forums.
            </p>

            <div className="space-y-3">
              <div className="p-3 bg-gray-50 rounded-lg flex justify-between items-center">
                <div>
                  <h4 className="font-medium">General Discussion</h4>
                  <p className="text-sm text-gray-500">2.5k topics • 18.7k posts</p>
                </div>
                <IonIcon icon={chevronForward} className="text-gray-400" />
              </div>

              <div className="p-3 bg-gray-50 rounded-lg flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Tips & Tricks</h4>
                  <p className="text-sm text-gray-500">1.3k topics • 9.4k posts</p>
                </div>
                <IonIcon icon={chevronForward} className="text-gray-400" />
              </div>

              <div className="p-3 bg-gray-50 rounded-lg flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Feature Requests</h4>
                  <p className="text-sm text-gray-500">780 topics • 5.2k posts</p>
                </div>
                <IonIcon icon={chevronForward} className="text-gray-400" />
              </div>
            </div>

            <div className="flex justify-center mt-4">
              <IonButton expand="block" color="primary">
                Visit Forums
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>
      </div>
    </div>
  );
};

export default Help;
