import {
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonChip,
  IonIcon,
  IonItem,
  IonList,
  IonSpinner,
  IonToast,
} from '@ionic/react';
import {
  chevronBackOutline,
  chevronForwardOutline,
  closeCircleOutline,
  cloudUploadOutline,
  documentOutline,
  downloadOutline,
  trashOutline,
} from 'ionicons/icons';
import { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { getRouteTitle } from '../../config/routes';
import { usePagination } from '../../hooks/usePagination';
import { IFileEntity } from '../../interfaces/entity/i-file-entity';
import { fileService } from '../../services';
import { formatDateShort } from '../../utils/dateUtils';

const FileManagement: React.FC = () => {
  const location = useLocation();
  const pageTitle = getRouteTitle(location.pathname);

  const [files, setFiles] = useState<IFileEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const {
    paginationParams,
    paginationMeta,
    setPaginationMeta,
    handlePageChange,
    handleLimitChange,
    handleSortChange,
  } = usePagination({
    initialParams: {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    },
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchFiles();
  }, [paginationParams]);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await fileService.getAll(paginationParams);
      setFiles(response.items);
      setPaginationMeta(response.meta);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching files:', err);
      setError('Failed to load files');
      setLoading(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (!files || files.length === 0) {
      return;
    }

    const file = files[0];
    try {
      setIsUploading(true);
      await fileService.upload({
        file,
        isPublic: false,
      });
      setToastMessage('File uploaded successfully');
      setShowToast(true);
      fetchFiles();

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (err) {
      console.error('Error uploading file:', err);
      setToastMessage('Failed to upload file');
      setShowToast(true);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = async (_fileId: string) => {
    // À adapter selon la future implémentation d'URL de téléchargement
    // Par exemple : window.open(fileService.getDownloadUrlByKey(fileKey), '_blank');
    setToastMessage('Download not implemented');
    setShowToast(true);
  };

  const handleDelete = async (fileId: string) => {
    try {
      await fileService.delete(fileId);
      setToastMessage('File deleted successfully');
      setShowToast(true);
      fetchFiles(); // Refresh the file list
    } catch (err) {
      console.error('Error deleting file:', err);
      setToastMessage('Failed to delete file');
      setShowToast(true);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) {
      return '0 Bytes';
    }

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  // Add this function before the return statement, after formatFileSize

  return (
    <div
      style={{ paddingTop: '60px', paddingBottom: '60px' }}
      className="ion-padding bg-gray-50 h-full overflow-auto"
    >
      <div className="max-w-4xl mx-auto pt-4">
        <div className="my-4">
          <h1 className="text-4xl font-bold mb-4">{pageTitle}</h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-300">Manage your files</p>
        </div>

        {/* Upload Card */}
        <IonCard className="shadow-md mb-6">
          <IonCardHeader className="flex items-center">
            <IonIcon size="large" icon={cloudUploadOutline} className="text-blue-600 mr-2" />
            <h3 className="font-semibold text-gray-700">Upload New File</h3>
          </IonCardHeader>
          <IonCardContent>
            <div className="flex flex-col md:flex-row items-center gap-4">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="flex-1 cursor-pointer border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors"
              >
                <div className="flex flex-col items-center">
                  <IonIcon icon={cloudUploadOutline} className="text-4xl text-gray-400 mb-2" />
                  <p className="text-gray-600">Click to select a file</p>
                  <p className="text-xs text-gray-400 mt-1">Max file size: 10MB</p>
                </div>
              </label>

              {isUploading && (
                <div className="flex items-center">
                  <IonSpinner name="dots" />
                  <span className="ml-2">Uploading...</span>
                </div>
              )}
            </div>
          </IonCardContent>
        </IonCard>

        {/* Files List */}
        <IonCard className="shadow-md">
          <IonCardHeader className="flex items-center">
            <IonIcon size="large" icon={documentOutline} className="text-indigo-600 mr-2" />
            <h3 className="font-semibold text-gray-700">Your Files</h3>
          </IonCardHeader>
          <IonCardContent>
            {loading ? (
              <div className="flex justify-center p-4">
                <IonSpinner />
              </div>
            ) : error ? (
              <p className="text-red-500 p-4">{error}</p>
            ) : files.length === 0 ? (
              <p className="text-gray-500 p-4 text-center">No files found</p>
            ) : (
              <>
                {/* Pagination Controls - Top */}
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <span className="mr-2">Show:</span>
                    <select
                      value={paginationParams.limit}
                      onChange={(e) => handleLimitChange(Number(e.target.value))}
                      className="border rounded p-1"
                    >
                      <option value={5}>5</option>
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                    </select>
                  </div>

                  {paginationMeta && (
                    <div className="text-sm text-gray-500">
                      Showing {(paginationMeta.page - 1) * paginationMeta.limit + 1} to{' '}
                      {Math.min(
                        paginationMeta.page * paginationMeta.limit,
                        paginationMeta.totalItems,
                      )}{' '}
                      of {paginationMeta.totalItems} files
                    </div>
                  )}
                </div>

                {/* Files List */}
                <IonList className="bg-transparent">
                  <div className="flex w-full border-b py-2 px-3 font-medium text-sm text-gray-600">
                    <div className="flex-1" onClick={() => handleSortChange('originalName')}>
                      Name{' '}
                      {paginationParams.sortBy === 'originalName' &&
                        (paginationParams.sortOrder === 'asc' ? '↑' : '↓')}
                    </div>
                    <div className="w-24 text-center" onClick={() => handleSortChange('size')}>
                      Size{' '}
                      {paginationParams.sortBy === 'size' &&
                        (paginationParams.sortOrder === 'asc' ? '↑' : '↓')}
                    </div>
                    <div className="w-32 text-center" onClick={() => handleSortChange('createdAt')}>
                      Date{' '}
                      {paginationParams.sortBy === 'createdAt' &&
                        (paginationParams.sortOrder === 'asc' ? '↑' : '↓')}
                    </div>
                    <div className="w-24 text-center">Actions</div>
                  </div>
                  {files.map((file) => (
                    <IonItem key={file.id} lines="full" className="py-2">
                      <div className="flex flex-col md:flex-row w-full items-start md:items-center">
                        <div className="flex items-center flex-1">
                          <IonIcon
                            icon={documentOutline}
                            className="text-gray-500 mr-3"
                            size="large"
                          />
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-800">{file.originalFilename}</h4>
                            <div className="flex flex-wrap gap-2 mt-1">
                              <IonChip className="text-xs px-2">
                                {file.contentType ?? 'unknown'}
                              </IonChip>
                              <IonChip className="text-xs px-2">
                                {formatFileSize(Number(file.fileSize))}
                              </IonChip>
                              <IonChip className="text-xs px-2">
                                {formatDateShort(file.createdAt)}
                              </IonChip>
                              {file.uploadedBy && (
                                <IonChip className="text-xs px-2">
                                  {file.uploadedBy.username}
                                </IonChip>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex mt-2 md:mt-0">
                          <IonButton
                            fill="clear"
                            color="primary"
                            onClick={() => handleDownload(file.id)}
                          >
                            <IonIcon slot="icon-only" icon={downloadOutline} />
                          </IonButton>
                          <IonButton
                            fill="clear"
                            color="danger"
                            onClick={() => handleDelete(file.id)}
                          >
                            <IonIcon slot="icon-only" icon={trashOutline} />
                          </IonButton>
                        </div>
                      </div>
                    </IonItem>
                  ))}
                </IonList>

                {/* Pagination Controls - Bottom */}
                {paginationMeta && paginationMeta.totalPages > 1 && (
                  <div className="flex justify-between items-center mt-4">
                    <div>
                      <IonButton
                        fill="clear"
                        disabled={!paginationMeta.hasPreviousPage}
                        onClick={() => handlePageChange(paginationMeta.page - 1)}
                      >
                        <IonIcon icon={chevronBackOutline} slot="icon-only" />
                        Previous
                      </IonButton>
                    </div>

                    <div className="flex items-center">
                      {Array.from({ length: paginationMeta.totalPages }, (_, i) => i + 1).map(
                        (page) => (
                          <button
                            key={page}
                            className={`mx-1 px-3 py-1 rounded ${
                              page === paginationMeta.page
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-200 hover:bg-gray-300'
                            }`}
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </button>
                        ),
                      )}
                    </div>

                    <div>
                      <IonButton
                        fill="clear"
                        disabled={!paginationMeta.hasNextPage}
                        onClick={() => handlePageChange(paginationMeta.page + 1)}
                      >
                        Next
                        <IonIcon icon={chevronForwardOutline} slot="icon-only" />
                      </IonButton>
                    </div>
                  </div>
                )}
              </>
            )}
          </IonCardContent>
        </IonCard>
      </div>

      <IonToast
        isOpen={showToast}
        onDidDismiss={() => setShowToast(false)}
        message={toastMessage}
        duration={3000}
        position="bottom"
        buttons={[
          {
            icon: closeCircleOutline,
            role: 'cancel',
            handler: () => setShowToast(false),
          },
        ]}
      />
    </div>
  );
};

export default FileManagement;
