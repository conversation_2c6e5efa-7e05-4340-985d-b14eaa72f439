import { useAuth } from '../../plugin/keycloak/context';
import { keycloakService } from '../../plugin/keycloak/keycloak-service';
import { signOut } from '../../stores/currentUserSlice';
import { useAppDispatch } from '../../utils/redux';
import { IonContent, IonImg, IonPage } from '@ionic/react';
import Button from '../../components/ui/Button';
import APP_CONFIG from '../../config/app.config';
import { useEffect } from 'react';
import { PATHS } from '../../config/routes';
import { useHistory } from 'react-router-dom';

export const Login: React.FC = () => {
  //const location = useLocation();
  //const pageTitle = getRouteTitle(location.pathname);
  const appName = APP_CONFIG.ENV.APP_NAME;
  const dispatch = useAppDispatch();
  const auth = useAuth();
  const history = useHistory();

  // Vérification d'authentification
  useEffect(() => {
    if (auth.isAuthenticated) {
      console.log('Already logged in ! Redirect to Dashboard');
      history.push(PATHS.ADMIN);
    }
  }, [history]);

  async function handleLogin() {
    console.log('login');
    const res = await keycloakService.login();
    console.log('login done', res);
  }

  async function handleLogout() {
    console.log('logout');
    dispatch(signOut());
  }

  return (
    <>
      <IonPage>
        <IonContent className="ion-padding">
          <div className="h-screen flex items-center justify-center">
            <div>
              <IonImg src="../../logo-LRG.png" alt={appName}></IonImg>

              {/*              <h1 className="justify-center flex mb-2 font-bold text-gray-900">{appName}</h1>*/}
              {auth.isAuthenticated ? (
                <Button
                  onClick={handleLogout}
                  fill="clear"
                  color="primary"
                  size="small"
                  className="mt-2 font-medium p-0 h-auto"
                  isLink={true}
                >
                  Se deconnecter
                </Button>
              ) : (
                <Button
                  onClick={handleLogin}
                  fill="clear"
                  color="primary"
                  size="small"
                  className="mt-2 font-medium p-0 h-auto"
                  isLink={true}
                >
                  Se connecter
                </Button>
              )}
            </div>
          </div>
          <p className="flex justify-center items-center text-sm text-neutral-600 dark:text-neutral-400">
            {appName} 2025
          </p>
        </IonContent>
      </IonPage>
    </>
  );
};
