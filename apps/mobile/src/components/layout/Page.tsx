import { IonContent, IonPage } from '@ionic/react';
import React, { useMemo } from 'react';
import { useAppSelector } from '../../utils/redux';

/**
 * Wrapper pour IonPage avec padding dynamique selon header/footer.
 * @param disableHeaderMargin Désactive le padding top si true
 * @param disableFooterMargin Désactive le padding bottom si true
 */
interface PageProps {
  children: React.ReactNode;
  disableHeaderMargin?: boolean;
}

export function Page({ children, disableHeaderMargin = false }: PageProps) {
  const headerHeight = useAppSelector((state) => state.applicationState.headerSize);

  const style: React.CSSProperties = useMemo(
    () => ({
      paddingTop: disableHeaderMargin ? 0 : `${headerHeight}px`,
    }),
    [disableHeaderMargin, headerHeight],
  );

  return (
    <IonPage>
      <IonContent>
        <div className="h-full overflow-auto w-full" style={style}>
          {children}
        </div>
      </IonContent>
    </IonPage>
  );
}
