import { <PERSON><PERSON>vat<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IonHeader } from '@ionic/react';
import { LogOut } from 'lucide-react';
import React, { useEffect } from 'react';
import { setHeaderSize } from '../../stores/applicationStateSlice';
import { signOut } from '../../stores/currentUserSlice';
import { useAppDispatch } from '../../utils/redux';
import Dropdown from '../ui/stylized/Dropdown';
import Logo from '../ui/stylized/Logo';

const Header: React.FC = () => {
  const dispatch = useAppDispatch();
  const handleLogout = async () => {
    dispatch(signOut());
  };
  const headerRef = React.useRef<HTMLIonHeaderElement>(null);

  useEffect(() => {
    let headerSizeResizeObserver: ResizeObserver | null = null;

    if (headerRef.current) {
      dispatch(setHeaderSize(headerRef.current.getBoundingClientRect().height));
      headerSizeResizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === headerRef.current) {
            dispatch(setHeaderSize(entry.target.getBoundingClientRect().height));
          }
        }
      });
      headerSizeResizeObserver.observe(headerRef.current);
    }

    return () => {
      if (headerSizeResizeObserver && headerRef.current) {
        headerSizeResizeObserver.unobserve(headerRef.current);
      }
    };
  }, [dispatch]);

  return (
    <IonHeader
      style={{
        border: 'none',
        boxShadow: 'none',
        overflow: 'none',
        backgroundColor: 'white',
      }}
      ref={headerRef}
      className="py-2 sm:py-3 md:py-4"
    >
      <div className="px-4 sm:px-6 md:px-8 lg:px-10">
        <div className="flex justify-between items-center h-12 sm:h-14 md:h-16">
          <IonButtons slot="start" className="flex-shrink-0">
            <Logo />
          </IonButtons>

          <Dropdown
            position="bottom-right"
            trigger={
              <IonAvatar className="h-10 w-10 sm:h-12 sm:w-12 md:h-14 md:w-14 flex-shrink-0">
                <img alt="Avatar" src="https://ionicframework.com/docs/img/demos/avatar.svg" />
              </IonAvatar>
            }
            options={[
              {
                id: 'logout',
                value: 'logout',
                label: 'Se déconnecter',
                icon: <LogOut className="primary-color h-4 w-4" />,
                onClick: handleLogout,
                iconClassName: 'primary-color h-4 w-4',
              },
            ]}
          />
        </div>
      </div>
    </IonHeader>
  );
};

export default Header;
