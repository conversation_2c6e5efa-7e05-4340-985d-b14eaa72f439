import {
  IonContent,
  IonHeader,
  IonItem,
  IonLabel,
  IonList,
  IonMenu,
  IonMenuToggle,
  IonTitle,
  IonToolbar,
} from '@ionic/react';
import React from 'react';
import { useHistory } from 'react-router-dom';
import APP_CONFIG from '../../config/app.config';
import { getMainMenuRoutes, PATHS } from '../../config/routes';
import { signOut } from '../../stores/currentUserSlice';
import { useAppDispatch } from '../../utils/redux';

interface MenuProps {
  appTitle?: string;
}

const Menu: React.FC<MenuProps> = ({ appTitle = APP_CONFIG.ENV.APP_NAME }) => {
  const mainMenuRoutes = getMainMenuRoutes();
  const history = useHistory();
  const dispatch = useAppDispatch();

  const handleLogout = () => {
    console.log('Logging out...');
    dispatch(signOut()).then(() => {
      history.push(PATHS.LOGIN);
    });
  };

  return (
    <IonMenu contentId="main-content" menuId="mainMenu" className="bg-white" side="start">
      <IonHeader className="px-4 py-2">
        <IonToolbar>
          <IonTitle>{appTitle}</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent>
        <IonList className="pt-4">
          {mainMenuRoutes.map((route, index) => {
            // Si c'est l'élément Logout, gérer la déconnexion
            if (route.path === PATHS.LOGIN && route.label === 'Logout') {
              return (
                <IonMenuToggle key={index}>
                  <IonItem className="px-4 py-2" button onClick={handleLogout} lines="none">
                    {route.icon && <route.icon size={24} />}
                    <IonLabel>{route.label}</IonLabel>
                  </IonItem>
                </IonMenuToggle>
              );
            }

            // Pour tous les autres éléments, utiliser routerLink
            return (
              <IonMenuToggle key={index}>
                <IonItem
                  className="px-4 py-2"
                  button
                  routerLink={route.path}
                  routerDirection="root"
                  lines="none"
                >
                  {route.icon && <route.icon size={24} />}
                  <IonLabel>{route.label}</IonLabel>
                </IonItem>
              </IonMenuToggle>
            );
          })}
        </IonList>
      </IonContent>
    </IonMenu>
  );
};

export default Menu;
