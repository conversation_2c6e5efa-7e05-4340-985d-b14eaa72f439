import { IonContent, IonPage, IonSplitPane } from '@ionic/react';
import React, { useEffect, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import APP_CONFIG from '../../config/app.config';
import { getRouteTitle, PATHS, TitleContext } from '../../config/routes';
import Header from './Header';
import Menu from './Menu';
import { useAuth } from '../../plugin/keycloak/context';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const [title, setTitle] = useState(APP_CONFIG.ENV.APP_NAME);
  const auth = useAuth();
  const history = useHistory();

  // Vérification d'authentification
  useEffect(() => {
    if (!auth.isAuthenticated) {
      console.log('Unauthorized access to protected route, redirecting to login');
      history.push(PATHS.LOGIN);
    }
  }, [history]);

  useEffect(() => {
    setTitle(getRouteTitle(location.pathname));
  }, [location.pathname]);

  return (
    <IonSplitPane contentId="main-content" when="md">
      <TitleContext.Provider value={title}>
        <div className="block md:hidden">
          <Menu />
        </div>
        <IonPage id="main-content">
          <Header />
          <IonContent scrollY={true} fullscreen>
            {children}
          </IonContent>
        </IonPage>
      </TitleContext.Provider>
    </IonSplitPane>
  );
};
