import { IonButton, IonIcon, IonSpinner } from '@ionic/react';
import { checkmarkCircle, cloudDownload, document, warningOutline } from 'ionicons/icons';
import { useMemo, useState } from 'react';
import { IDeliveryNoteEntity } from '../interfaces/entity/i-delivery-note-entity';
import { deliveryNotesActions } from '../stores/deliveryNotesSlice';
import { loadDeliveryNote } from '../utils/delivery-note-loader';
import { useAppDispatch, useAppSelector } from '../utils/redux';

interface DeliveryNoteButtonProps {
  tourId: string;
  deliveryNote: IDeliveryNoteEntity;
  compact?: boolean;
}

export const DeliveryNoteButton: React.FC<DeliveryNoteButtonProps> = ({
  tourId,
  deliveryNote,
  compact = false,
}) => {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const hasInternet = useAppSelector((state) => state.applicationState.hasInternet);
  const offlineDeliveryNotes = useAppSelector((state) => state.deliveryNotes.offlineDeliveryNotes);
  const downloadQueue = useAppSelector((state) => state.deliveryNotes.downloadQueue);

  // Check if delivery note is already downloaded
  const isOffline = useMemo(() => {
    return !!offlineDeliveryNotes[deliveryNote.id];
  }, [offlineDeliveryNotes, deliveryNote.id]);

  // Check download status from queue
  const downloadItem = useMemo(() => {
    return downloadQueue.find((item) => item.deliveryNoteId === deliveryNote.id);
  }, [downloadQueue, deliveryNote.id]);

  const isDownloading = downloadItem?.status === 'downloading';
  const isPending = downloadItem?.status === 'pending';
  const _isFailed = downloadItem?.status === 'failed';
  const isNotAvailable = downloadItem?.error === 'File not yet imported';

  const handleClick = async () => {
    if (isLoading || isDownloading || isPending) {
      return;
    }

    setIsLoading(true);
    try {
      const result = await loadDeliveryNote(tourId, deliveryNote, {
        openAfterDownload: true,
        forceRefresh: false,
      });

      if (!result.success && result.error) {
        console.error('Failed to load delivery note:', result.error);

        // If it's a file not yet available error, add to queue for background download
        if (result.error.includes('not yet available') && deliveryNote.file) {
          await dispatch(
            deliveryNotesActions.queueDeliveryNotesForDownload([deliveryNote]),
          ).unwrap();
        }
        // TODO: Show error toast with result.error message
      }
    } catch (error) {
      console.error('Failed to load delivery note:', error);
      // TODO: Show error toast
    } finally {
      setIsLoading(false);
    }
  };

  if (compact) {
    return (
      <IonButton
        fill="clear"
        size="small"
        onClick={handleClick}
        disabled={
          isLoading || isDownloading || isPending || (!hasInternet && !isOffline) || isNotAvailable
        }
      >
        {isLoading || isDownloading || isPending ? (
          <IonSpinner name="crescent" />
        ) : (
          <IonIcon
            icon={isNotAvailable ? warningOutline : isOffline ? document : cloudDownload}
            color={isNotAvailable ? 'warning' : isOffline ? 'success' : 'medium'}
          />
        )}
      </IonButton>
    );
  }

  return (
    <IonButton
      expand="block"
      fill="outline"
      onClick={handleClick}
      disabled={isLoading || isDownloading || isPending || isNotAvailable}
    >
      {isLoading || isDownloading || isPending ? (
        <>
          <IonSpinner name="crescent" />
          <span className="ml-2">Chargement...</span>
        </>
      ) : isNotAvailable ? (
        <>
          <IonIcon icon={warningOutline} slot="start" />
          Document non disponible
        </>
      ) : (
        <>
          <IonIcon icon={isOffline ? checkmarkCircle : cloudDownload} slot="start" />
          {isOffline ? 'Ouvrir BL' : 'Télécharger BL'}
        </>
      )}
    </IonButton>
  );
};

export default DeliveryNoteButton;
