import { IonIcon } from '@ionic/react';
import { chevronDown, chevronUp } from 'ionicons/icons';
import { useState, useEffect, ReactNode } from 'react';

interface AccordionProps {
  title: ReactNode;
  children: ReactNode;
  isOpenDefault?: boolean;
  isControlled?: boolean;
  isOpen?: boolean;
  onToggle?: (isOpen: boolean) => void;
  classNames?: {
    container?: string;
    header?: string;
    title?: string;
    content?: string;
    icon?: string;
  };
  disabled?: boolean;
}

const Accordion: React.FC<AccordionProps> = ({
  title,
  children,
  isOpenDefault = false,
  isControlled = false,
  isOpen: controlledIsOpen,
  onToggle,
  classNames,
  disabled = false,
}) => {
  const [internalIsOpen, setInternalIsOpen] = useState(isOpenDefault);

  // Utiliser l'état contrôlé si fourni, sinon l'état interne
  const isOpen = isControlled ? (controlledIsOpen ?? false) : internalIsOpen;

  useEffect(() => {
    if (!isControlled) {
      setInternalIsOpen(isOpenDefault);
    }
  }, [isOpenDefault, isControlled]);

  const handleToggle = () => {
    if (disabled) {
      return;
    }

    const newIsOpen = !isOpen;

    if (isControlled) {
      // Mode contrôlé : notifier le parent
      onToggle?.(newIsOpen);
    } else {
      // Mode non contrôlé : gérer l'état interne
      setInternalIsOpen(newIsOpen);
      onToggle?.(newIsOpen);
    }
  };

  return (
    <div
      className={`border border-gray-200 rounded-lg overflow-hidden ${classNames?.container || ''}`}
    >
      {/* Header */}
      <div
        className={`
          flex items-center justify-between p-2 cursor-pointer 
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50 active:bg-gray-100'}
          ${isOpen ? 'bg-gray-50' : 'bg-white'}
          ${classNames?.header || ''}
        `}
        onClick={handleToggle}
      >
        <div className={`flex-1 ${classNames?.title || ''}`}>{title}</div>

        <IonIcon
          icon={isOpen ? chevronUp : chevronDown}
          className={`text-xl text-gray-600 transition-transform duration-200 ml-2 ${classNames?.icon || ''}`}
        />
      </div>

      {/* Content */}
      <div
        className={`
          transition-all duration-300 ease-in-out overflow-hidden
          ${isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}
        `}
      >
        <div className={`p-2 bg-white border-t border-gray-100 ${classNames?.content || ''}`}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default Accordion;
