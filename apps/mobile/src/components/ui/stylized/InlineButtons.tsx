import { IonIcon } from '@ionic/react';

interface InlineButtonsProps {
  buttons: ButtonProps[];
}

export interface ButtonProps {
  label: string;
  onClick: () => void;
  disabled?: boolean;
  icon?: string;
  classNames?: {
    container?: string;
    button?: string;
    label?: string;
  };
}

export default function InlineButtons({ buttons }: InlineButtonsProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full justify-center items-center">
      {buttons.map((button) => (
        <div
          key={button.label}
          onClick={() => {
            if (!button.disabled) {
              button.onClick();
            }
          }}
          className={`cursor-pointer rounded-full px-4 py-2 w-full sm:w-48 md:w-52 lg:w-56 min-w-fit text-nowrap flex items-center justify-center gap-2 text-sm sm:text-base md:text-lg ${button.classNames?.button} ${button.disabled ? 'opacity-50' : ''}`}
        >
          {button.icon && <IonIcon icon={button.icon} className="text-lg sm:text-xl" />}
          <span className={`font-medium ${button.classNames?.label}`}>{button.label}</span>
        </div>
      ))}
    </div>
  );
}
