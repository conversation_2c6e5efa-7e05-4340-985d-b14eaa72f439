import { useState, useRef, useEffect, ReactNode } from 'react';

interface DropdownOption {
  id: string;
  label: string;
  value: any;
  icon?: ReactNode;
  disabled?: boolean;
  onClick?: () => void;
  iconClassName?: string;
}

interface DropdownProps {
  trigger: ReactNode;
  options: DropdownOption[];
  onSelect?: (option: DropdownOption) => void;
  className?: string;
  menuClassName?: string;
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  disabled?: boolean;
}

export default function Dropdown({
  trigger,
  options,
  onSelect,
  className = '',
  menuClassName = '',
  position = 'bottom-left',
  disabled = false,
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fermer le dropdown en cliquant à l'extérieur
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleTriggerClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleOptionClick = (option: DropdownOption) => {
    if (!option.disabled) {
      if (option.onClick) {
        option.onClick();
      }

      if (onSelect) {
        onSelect(option);
      }
      setIsOpen(false);
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'top-full left-0 mt-2';
      case 'bottom-right':
        return 'top-full right-0 mt-2';
      case 'top-left':
        return 'bottom-full left-0 mb-2';
      case 'top-right':
        return 'bottom-full right-0 mb-2';
      default:
        return 'top-full left-0 mt-2';
    }
  };

  return (
    <div ref={dropdownRef} className={`relative inline-block ${className}`}>
      {/* Trigger */}
      <div
        onClick={handleTriggerClick}
        className={`cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {trigger}
      </div>

      {/* Menu déroulant */}
      {isOpen && (
        <div className={`absolute z-50 ${getPositionClasses()}`}>
          <div
            className={`
            bg-white border border-gray-200 rounded-lg shadow-lg  min-w-48
            max-h-64 overflow-y-auto
            ${menuClassName}
          `}
          >
            {options.map((option) => (
              <div
                key={option.id}
                onClick={() => handleOptionClick(option)}
                className={`
                  px-4 py-3 text-sm font-medium cursor-pointer transition-colors primary-color
                  flex items-center space-x-3
                 
                `}
              >
                {option.icon && <div className={option.iconClassName}>{option.icon}</div>}
                <span className="">{option.label}</span>
              </div>
            ))}

            {options.length === 0 && (
              <div className="px-4 py-3 text-sm text-gray-500 text-center">
                Aucune option disponible
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
