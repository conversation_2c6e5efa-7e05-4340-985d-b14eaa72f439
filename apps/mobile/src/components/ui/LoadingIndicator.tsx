import { IonSpinner } from '@ionic/react';
import React from 'react';

interface LoadingIndicatorProps {
  text?: string;
  fullscreen?: boolean;
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  text = 'Chargement...',
  fullscreen = true,
}) => {
  return (
    <div
      className={`z-50 flex flex-col items-center justify-center ${fullscreen ? 'fixed inset-0' : 'w-full h-full'} bg-black bg-opacity-40`}
      style={{ minHeight: fullscreen ? '100vh' : undefined }}
    >
      <IonSpinner name="dots" className="w-16 h-16 text-white" />
      {text && <span className="mt-4 text-lg text-white font-medium drop-shadow-lg">{text}</span>}
    </div>
  );
};

export default LoadingIndicator;
