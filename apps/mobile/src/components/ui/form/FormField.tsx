import React from 'react';
import Input from '../Input';
import Select from './Select';
import TextArea from './TextArea';
import Checkbox from './Checkbox';
import RadioGroup from './RadioGroup';

interface FormFieldProps {
  label: string;
  type: string;
  className?: string;
  [key: string]: any;
}

const FormField: React.FC<FormFieldProps> = ({ label, type, className = '', ...rest }) => {
  const renderField = () => {
    switch (type) {
      case 'select': {
        const { name, options, required, error, register } = rest;

        return (
          <Select
            name={name}
            options={options}
            required={required}
            error={error}
            register={register}
          />
        );
      }
      case 'textarea': {
        const { name, placeholder, required, error, register } = rest;

        return (
          <TextArea
            name={name}
            placeholder={placeholder}
            required={required}
            error={error}
            register={register}
          />
        );
      }
      case 'checkbox': {
        const { name, label, required, error, register } = rest;

        return (
          <Checkbox
            name={name}
            label={label}
            required={required}
            error={error}
            register={register}
          />
        );
      }
      case 'radio': {
        const { name, options, required, error, register } = rest;

        return (
          <RadioGroup
            name={name}
            options={options}
            required={required}
            error={error}
            register={register}
          />
        );
      }
      default: {
        const { name, placeholder, icon, required, minLength, error, register } = rest;

        return (
          <Input
            type={type}
            name={name}
            placeholder={placeholder}
            icon={icon}
            required={required}
            minLength={minLength}
            error={error}
            register={register}
          />
        );
      }
    }
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
        {label}
      </label>
      {renderField()}
    </div>
  );
};

export default FormField;
