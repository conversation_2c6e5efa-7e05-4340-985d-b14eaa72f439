import React from 'react';

interface CheckboxProps {
  name: string;
  label: string;
  required?: boolean;
  error?: string;
  register?: any;
}

const Checkbox: React.FC<CheckboxProps> = ({ name, label, required = false, error, register }) => {
  const checkboxProps = register ? register(name, { required }) : { name, required };

  return (
    <div>
      <label className="flex items-center">
        <input type="checkbox" className="form-checkbox text-primary" {...checkboxProps} />
        <span className="ml-2">{label}</span>
      </label>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );
};

export default Checkbox;
