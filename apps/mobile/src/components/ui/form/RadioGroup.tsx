import React from 'react';

interface RadioOption {
  label: string;
  value: string;
}

interface RadioGroupProps {
  name: string;
  options: RadioOption[];
  required?: boolean;
  error?: string;
  register?: any;
}

const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  options,
  required = false,
  error,
  register,
}) => {
  const radioProps = (value: string) =>
    register ? register(name, { required }) : { name, required, value };

  return (
    <div>
      {options.map((opt) => (
        <label key={opt.value} className="flex items-center mb-1">
          <input
            type="radio"
            value={opt.value}
            className="form-radio text-primary"
            {...radioProps(opt.value)}
          />
          <span className="ml-2">{opt.label}</span>
        </label>
      ))}
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );
};

export default RadioGroup;
