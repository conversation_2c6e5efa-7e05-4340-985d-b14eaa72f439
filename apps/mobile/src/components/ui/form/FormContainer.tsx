import React from 'react';

interface FormContainerProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
}

const FormContainer: React.FC<FormContainerProps> = ({ title, subtitle, children, footer }) => {
  return (
    <div className="w-full max-w-md">
      <div className="text-center mb-8">
        <h1 className="mb-2 font-bold text-gray-900">{title}</h1>
        {subtitle && <p className="text-neutral-600 dark:text-neutral-300">{subtitle}</p>}
      </div>

      <div className="bg-white dark:bg-neutral-800 p-8 rounded-xl">{children}</div>

      {footer && <div className="text-center mt-6">{footer}</div>}
    </div>
  );
};

export default FormContainer;
