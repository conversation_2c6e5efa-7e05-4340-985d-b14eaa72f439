import React from 'react';

interface SelectProps {
  name: string;
  options: { label: string; value: string }[];
  required?: boolean;
  error?: string;
  register?: any;
}

const Select: React.FC<SelectProps> = ({ name, options, required = false, error, register }) => {
  const selectClasses = `block w-full px-3 py-2 border rounded-lg
        bg-white dark:bg-neutral-900 text-neutral-900 dark:text-white
        focus:ring-2 focus:ring-primary
        ${error ? 'border-red-500 dark:border-red-500' : 'border-neutral-300 dark:border-neutral-600'}`;

  const selectProps = register ? register(name, { required }) : { name, required };

  return (
    <>
      <select className={selectClasses} {...selectProps}>
        {options.map((opt) => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </select>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </>
  );
};

export default Select;
