import React from 'react';

interface TextAreaProps {
  name: string;
  placeholder?: string;
  required?: boolean;
  error?: string;
  register?: any;
}

const TextArea: React.FC<TextAreaProps> = ({
  name,
  placeholder,
  required = false,
  error,
  register,
}) => {
  const textAreaClasses = `block w-full px-3 py-2 border rounded-lg
        bg-white dark:bg-neutral-900 text-neutral-900 dark:text-white
        focus:ring-2 focus:ring-primary
        ${error ? 'border-red-500 dark:border-red-500' : 'border-neutral-300 dark:border-neutral-600'}`;

  const textAreaProps = register ? register(name, { required }) : { name, required };

  return (
    <>
      <textarea className={textAreaClasses} placeholder={placeholder} {...textAreaProps} />
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </>
  );
};

export default TextArea;
