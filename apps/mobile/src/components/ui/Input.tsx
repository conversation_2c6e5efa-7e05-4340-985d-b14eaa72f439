import React from 'react';
import { IonIcon } from '@ionic/react';

interface InputProps {
  type: string;
  placeholder?: string;
  icon?: any;
  name: string;
  required?: boolean;
  minLength?: number;
  error?: string;
  register?: any;
}

const Input: React.FC<InputProps> = ({
  type,
  placeholder,
  icon,
  name,
  required = false,
  minLength,
  error,
  register,
}) => {
  const inputClasses = `block w-full pl-10 pr-3 py-2 border rounded-lg
        bg-white dark:bg-neutral-900 text-neutral-900 dark:text-white
        focus:ring-2 focus:ring-primary focus:border-transparent
        ${error ? 'border-red-500 dark:border-red-500' : 'border-neutral-300 dark:border-neutral-600'}`;

  const inputProps = register
    ? register(name, { required, minLength })
    : { name, required, minLength };

  return (
    <>
      <div className="relative">
        {icon && (
          <div className="absolute top-1/2 transform -translate-y-1/2 left-0 pl-3 flex items-center pointer-events-none">
            <IonIcon icon={icon} className="text-neutral-500" />
          </div>
        )}
        <input type={type} className={inputClasses} placeholder={placeholder} {...inputProps} />
      </div>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </>
  );
};

export default Input;
