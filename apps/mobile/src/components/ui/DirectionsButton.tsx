import { IonButton, IonIcon, useIonToast } from '@ionic/react';
import { navigate } from 'ionicons/icons';
import { openDirectionsToAddress, openGoogleMapsDirections } from '../../utils/google-maps-utils';

interface DirectionsButtonProps {
  // Option 1: Adresse formatée
  destination?: string;
  // Option 2: Objet adresse (format standard)
  address?: {
    street?: string;
    city?: string;
    postalCode?: string;
    country?: string;
  };
  // Option 3: Objet adresse (format IAddress)
  addressLines?: {
    line1?: string;
    line2?: string;
    line3?: string;
    line4?: string;
  };
  // Configuration
  useCurrentLocation?: boolean;
  travelMode?: 'driving' | 'walking' | 'bicycling' | 'transit';
  // Style et comportement
  fill?: 'clear' | 'outline' | 'solid' | 'ghost';
  size?: 'small' | 'default' | 'large';
  color?: string;
  expand?: 'full' | 'block';
  disabled?: boolean;
  className?: string;
  // Contenu du bouton
  children?: React.ReactNode;
  showIcon?: boolean;
}

const DirectionsButton: React.FC<DirectionsButtonProps> = ({
  destination,
  address,
  addressLines,
  useCurrentLocation = true,
  travelMode = 'driving',
  fill = 'ghost',
  size = 'default',
  color = 'primary',
  expand,
  disabled = false,
  className = '',
  children = 'Itinéraire',
  showIcon = true,
}) => {
  const [presentToast] = useIonToast();

  const handleOpenDirections = async () => {
    try {
      if (destination) {
        // Utiliser la destination directe
        await openGoogleMapsDirections({
          destination,
          useCurrentLocation,
          travelMode,
        });
      } else if (address) {
        // Utiliser l'objet adresse (format standard)
        await openDirectionsToAddress(address, {
          useCurrentLocation,
          travelMode,
        });
      } else if (addressLines) {
        // Utiliser l'objet adresse (format IAddress - lignes)
        const formattedAddress = [
          addressLines.line1,
          addressLines.line2,
          addressLines.line3,
          addressLines.line4,
        ]
          .filter(Boolean)
          .join(', ');

        if (!formattedAddress) {
          throw new Error('Adresse vide ou invalide');
        }

        await openGoogleMapsDirections({
          destination: formattedAddress,
          useCurrentLocation,
          travelMode,
        });
      } else {
        throw new Error('Aucune destination spécifiée');
      }

      presentToast({
        message: 'Ouverture de Google Maps...',
        duration: 2000,
        color: 'success',
      });
    } catch (error) {
      console.error("Erreur lors de l'ouverture des directions:", error);

      let errorMessage = "Impossible d'ouvrir l'itinéraire";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      presentToast({
        message: errorMessage,
        duration: 3000,
        color: 'danger',
      });
    }
  };

  const isDisabled = disabled || (!destination && !address && !addressLines);

  return (
    <div
      className={`
        primary-color
        text-sm
        directions-button 
        ${className} 
        flex gap-2 items-center 
        cursor-pointer hover:underline
        ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
      onClick={handleOpenDirections}
    >
      {showIcon && <IonIcon icon={navigate} />}
      {children}
    </div>
  );
};

export default DirectionsButton;
