import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { IonIcon, IonLoading, IonText, IonToast } from '@ionic/react';
import { camera, checkmark, close, images, refresh } from 'ionicons/icons';
import { useState } from 'react';
import InlineButtons from './stylized/InlineButtons';

interface PhotoCaptureProps {
  onPhotoCapture: (photoBase64: string) => void;
  onCancel: () => void;
}

const PhotoCapture: React.FC<PhotoCaptureProps> = ({ onPhotoCapture, onCancel }) => {
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const takePhoto = async (source: CameraSource = CameraSource.Camera) => {
    try {
      setIsLoading(true);
      setError(null);

      const image = await Camera.getPhoto({
        quality: 80,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: source,
        width: 1024,
        height: 1024,
      });

      if (image.base64String) {
        const base64WithPrefix = `data:image/jpeg;base64,${image.base64String}`;
        setCapturedPhoto(base64WithPrefix);
      }
    } catch (error: any) {
      console.error('Error taking photo:', error);

      if (error.message && error.message.includes('User cancelled')) {
        // L'utilisateur a annulé, pas d'erreur à afficher
        return;
      }
      setError("Erreur lors de la capture photo. Vérifiez les permissions de l'appareil photo.");
    } finally {
      setIsLoading(false);
    }
  };

  const retakePhoto = () => {
    setCapturedPhoto(null);
    takePhoto(CameraSource.Camera);
  };

  const selectFromGallery = async () => {
    await takePhoto(CameraSource.Photos);
  };

  const savePhoto = () => {
    if (capturedPhoto) {
      onPhotoCapture(capturedPhoto);
    }
  };

  return (
    <div className="pt-6 pb-6">
      {/* Titre et description */}
      <div className="text-center mb-8">
        <IonText className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-4 block">
          Photo de preuve
        </IonText>
        <IonText className="text-sm sm:text-base text-gray-600">
          Prenez une photo pour prouver la livraison
        </IonText>
      </div>

      {/* Zone de photo */}
      <div className="mb-8">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50 min-h-64 flex items-center justify-center">
          {capturedPhoto ? (
            <div className="w-full">
              <img
                src={capturedPhoto}
                alt="Photo de preuve"
                className="w-full max-h-64 object-contain rounded-lg shadow-sm"
              />
            </div>
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center">
              <IonIcon icon={camera} className="text-6xl text-gray-400 mb-4" />
              <IonText className="text-gray-500">Aucune photo prise</IonText>
            </div>
          )}
        </div>

        {/* Indication visuelle */}
        <div className="text-center mt-4">
          <IonText className="text-xs sm:text-sm text-gray-500">
            {capturedPhoto ? 'Photo capturée' : 'Appuyez sur un bouton ci-dessous pour capturer'}
          </IonText>
        </div>
      </div>

      {/* Boutons d'action avec InlineButtons */}
      <div className="space-y-4 mx-4">
        {!capturedPhoto ? (
          <>
            <InlineButtons
              buttons={[
                {
                  label: 'Appareil photo',
                  icon: camera,
                  onClick: isLoading ? () => {} : () => takePhoto(CameraSource.Camera),
                  classNames: {
                    button: `${isLoading ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'primary-button'} px-6 py-3 text-sm font-medium`,
                  },
                },
                {
                  label: 'Galerie',
                  icon: images,
                  onClick: isLoading ? () => {} : selectFromGallery,
                  classNames: {
                    button: `${isLoading ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'secondary-button-outline'} px-6 py-3 text-sm font-medium`,
                  },
                },
              ]}
            />

            <InlineButtons
              buttons={[
                {
                  label: 'Annuler',
                  icon: close,
                  onClick: isLoading ? () => {} : onCancel,
                  classNames: {
                    button: `${isLoading ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'secondary-button-outline'} px-8 py-3 font-medium`,
                  },
                },
              ]}
            />
          </>
        ) : (
          <>
            <InlineButtons
              buttons={[
                {
                  label: 'Reprendre',
                  icon: refresh,
                  onClick: isLoading ? () => {} : retakePhoto,
                  classNames: {
                    button: `${isLoading ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'secondary-button-outline'} px-6 py-3 text-sm font-medium`,
                  },
                },
              ]}
            />

            <InlineButtons
              buttons={[
                {
                  label: 'Annuler',
                  icon: close,
                  onClick: isLoading ? () => {} : onCancel,
                  classNames: {
                    button: `${isLoading ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'secondary-button-outline'} px-8 py-3 font-medium`,
                  },
                },
                {
                  label: 'Valider',
                  icon: checkmark,
                  onClick: isLoading ? () => {} : savePhoto,
                  classNames: {
                    button: `${isLoading ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'primary-button'} px-8 py-3 font-medium`,
                  },
                },
              ]}
            />
          </>
        )}
      </div>

      <IonLoading isOpen={isLoading} message="Capture en cours..." spinner="crescent" />

      <IonToast
        isOpen={!!error}
        message={error || ''}
        duration={3000}
        color="danger"
        onDidDismiss={() => setError(null)}
      />
    </div>
  );
};

export default PhotoCapture;
