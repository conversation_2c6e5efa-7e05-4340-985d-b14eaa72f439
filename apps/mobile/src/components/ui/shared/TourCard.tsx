import { IonText } from '@ionic/react';
import React from 'react';

import { ITourEntity } from '../../../interfaces/entity/i-tour-entity';
import { TourStatus } from '../../../interfaces/enum/tour.enums';
import { formatDate } from '../../../utils/dateUtils';

export interface TourCardProps {
  tour: ITourEntity;
  onClick: () => void;
  statusColor?: 'success' | 'warning';
  className?: string;
  showDeliveryDate?: boolean;
}

export const TourCard: React.FC<TourCardProps> = ({
  tour,
  onClick,
  statusColor,
  className = '',
  showDeliveryDate = false,
}) => {
  const getBackgroundColor = () => {
    if (statusColor === 'success') {
      return 'bg-green-50';
    }

    if (statusColor === 'warning') {
      return 'bg-yellow-50';
    }

    return 'bg-gray-50';
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-shadow ${getBackgroundColor()} ${className}`}
      onClick={onClick}
    >
      <div className="flex flex-col gap-2 p-4">
        <p className="text-xl font-semibold primary-color">
          Tournée {tour.tourIdentifier.originalNumber}
        </p>

        {showDeliveryDate && tour.status === TourStatus.Completed && tour.deliveryDate && (
          <IonText className="text-sm font-medium text-gray-900/60">
            Fait le {formatDate(tour.deliveryDate)}
          </IonText>
        )}
      </div>
    </div>
  );
};
