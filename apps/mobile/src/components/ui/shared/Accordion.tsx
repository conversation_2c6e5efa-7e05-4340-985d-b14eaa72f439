import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';

export interface AccordionProps {
  children: React.ReactNode;
  value: string;
  count: number;
  onToggle: (value: string, isChecked: boolean) => void;
  title?: string;
  className?: string;
}

export const Accordion: React.FC<AccordionProps> = ({
  children,
  value,
  onToggle,
  count,
  title = 'Valider',
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className={`bg-white flex flex-col p-3 ${className}`}>
      <div
        className="flex items-center gap-2 justify-between cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-2 primary-color cursor-pointer">
          <label className="font-medium text-neutral-700">{title}</label>
        </div>
        <div className="flex items-center gap-2 justify-end">
          <p className="text-neutral-500">total : {count}</p>
          <ChevronDown className={`h-6 w-6 ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </div>
      <div className={`transition-all duration-300 ${isOpen ? 'h-fit pt-4' : 'h-0'}`}>
        {isOpen && children}
      </div>
    </div>
  );
};
