import { IonIcon, IonInput, IonItem } from '@ionic/react';
import { search } from 'ionicons/icons';
import React from 'react';

export interface SearchInputProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onValueChange,
  placeholder = 'Rechercher...',
  className = '',
}) => {
  return (
    <div className={`relative ${className}`}>
      <IonItem className="rounded-lg border border-gray-200 w-full" lines="none">
        <IonIcon icon={search} slot="start" className="text-gray-400" />
        <IonInput
          placeholder={placeholder}
          value={value}
          onIonInput={(e) => onValueChange(e.detail.value!)}
          clearInput
          className="text-sm"
        />
      </IonItem>
    </div>
  );
};
