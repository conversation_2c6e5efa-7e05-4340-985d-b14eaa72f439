import React from 'react';
import { IonText } from '@ionic/react';
import { formatAddress } from '../../services/utils';
import { IStopEntity } from '../../../interfaces/entity/i-stop-entity';

export type ClientCardProps = {
  stop: IStopEntity;
  className?: string;
};

export const ClientCard = (props: ClientCardProps) => {
  return (
    <div className="flex flex-col gap-4 my-6">
      <div
        className={`flex justify-between items-start ${props.className ?? ` ${props.className}`}`}
      >
        <div className={'flex-1'}>
          <IonText className="text-base text-xl primary-color font-bold text-gray-800 block">
            {props.stop.originalClientInfo?.name?.toUpperCase()}
          </IonText>
        </div>
        <div className={'text-nowrap pr-3'}>
          <IonText className="text-sm text-neutral-500 font-medium">
            {props.stop.deliveryTimeWindow || '--:--'}
          </IonText>
        </div>
      </div>
      <IonText className="text-sm  text-gray-500">
        {formatAddress(props.stop.originalClientInfo?.address)}
      </IonText>
    </div>
  );
};
