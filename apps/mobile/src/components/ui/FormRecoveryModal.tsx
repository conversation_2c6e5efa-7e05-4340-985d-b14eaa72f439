import {
  IonButton,
  IonContent,
  IonHeader,
  IonItem,
  IonList,
  IonModal,
  IonText,
  IonTitle,
  IonToolbar,
} from '@ionic/react';
import {
  CheckCircle2,
  RotateCcw,
  Clock,
  FileText,
  Image,
  PenTool,
  CreditCard,
  Package,
} from 'lucide-react';
import InlineButtons from './stylized/InlineButtons';

interface FormRecoveryModalProps {
  isOpen: boolean;
  onContinue: () => void;
  onRestart: () => void;
  onCancel: () => void;
  formSummary: {
    hasPayment: boolean;
    hasEquipmentDeposit: boolean;
    hasEquipmentPickup: boolean;
    hasDocumentation: boolean;
    hasSignature: boolean;
    hasIncident: boolean;
    lastUpdated?: Date;
  };
  stopName?: string;
}

const FormRecoveryModal: React.FC<FormRecoveryModalProps> = ({
  isOpen,
  onContinue,
  onRestart,
  onCancel,
  formSummary,
  stopName = 'cet arrêt',
}) => {
  const getStepIcon = (step: string) => {
    const iconMap = {
      payment: CreditCard,
      equipmentDeposit: Package,
      equipmentPickup: Package,
      documentation: Image,
      signature: PenTool,
      incident: FileText,
    };

    return iconMap[step as keyof typeof iconMap] || FileText;
  };

  const getCompletedSteps = () => {
    const steps = [
      { key: 'payment', label: 'Paiement', completed: formSummary.hasPayment },
      {
        key: 'equipmentDeposit',
        label: "Dépôt d'équipement",
        completed: formSummary.hasEquipmentDeposit,
      },
      {
        key: 'equipmentPickup',
        label: "Ramassage d'équipement",
        completed: formSummary.hasEquipmentPickup,
      },
      {
        key: 'documentation',
        label: 'Photos et commentaires',
        completed: formSummary.hasDocumentation,
      },
      {
        key: 'signature',
        label: 'Signature',
        completed: formSummary.hasSignature,
      },
      {
        key: 'incident',
        label: 'Incident',
        completed: formSummary.hasIncident,
      },
    ];

    return steps.filter((step) => step.completed);
  };

  const completedSteps = getCompletedSteps();
  const totalCompletedSteps = completedSteps.length;

  return (
    <IonModal isOpen={isOpen} onDidDismiss={onCancel}>
      <IonContent className="ion-padding">
        <div className="mb-6">
          <IonText className="text-lg font-bold primary-color">
            Livraison en cours pour {stopName}
          </IonText>
        </div>

        {/* Résumé des étapes complétées */}
        {totalCompletedSteps > 0 && (
          <div className="mb-6">
            <IonText className="text-base font-medium mb-3 block">
              Étapes complétées ({totalCompletedSteps})
            </IonText>

            <IonList>
              {completedSteps.map((step) => {
                const IconComponent = getStepIcon(step.key);

                return (
                  <IonItem key={step.key} lines="none">
                    <IconComponent className="w-5 h-5 text-green-600 mr-3" />
                    <IonText className="text-sm flex-1">{step.label}</IonText>
                    <CheckCircle2 className="w-5 h-5 text-green-600" />
                  </IonItem>
                );
              })}
            </IonList>
          </div>
        )}

        {/* Boutons d'action */}
        <div className="space-y-3">
          <InlineButtons
            buttons={[
              {
                label: 'Continuer',
                onClick: onContinue,
                classNames: {
                  button: 'primary-button px-8 sm:px-12 py-3 sm:py-4',
                },
              },
              {
                label: 'Recommencer',
                onClick: onRestart,
                classNames: {
                  button: 'primary-button-outline px-8 sm:px-12 py-3 sm:py-4',
                },
              },
              {
                label: 'Annuler',
                onClick: onCancel,
                classNames: {
                  button: 'primary-button-outline px-8 sm:px-12 py-3 sm:py-4',
                },
              },
            ]}
          />
        </div>
      </IonContent>
    </IonModal>
  );
};

export default FormRecoveryModal;
