import { IonButton } from '@ionic/react';
import React from 'react';
import { useHistory } from 'react-router-dom';

interface ButtonProps {
  type?: 'button' | 'submit' | 'reset';
  color?: string;
  disabled?: boolean;
  expand?: 'block' | 'full';
  onClick?: () => void;
  className?: string;
  children: React.ReactNode;
  fill?: 'clear' | 'outline' | 'solid' | 'default';
  size?: 'small' | 'default' | 'large';
  // Propriétés pour la fonctionnalité de lien
  to?: string;
  isLink?: boolean;
  routerDirection?: 'forward' | 'back' | 'root' | 'none';
  style?: React.CSSProperties;
}

/**
 * Composant Button polyvalent qui peut fonctionner comme un bouton standard ou un lien de navigation.
 *
 * @example Utilisation comme bouton standard
 * <Button type="submit" color="primary">Submit</Button>
 *
 * @example Utilisation comme bouton avec fonction onClick
 * <Button onClick={() => handleClick()} color="secondary" fill="outline">Cliquez ici</Button>
 *
 * @example Utilisation comme bouton de navigation (utilise history.push)
 * <Button to="/dashboard" color="tertiary">Aller au tableau de bord</Button>
 *
 * @example Utilisation comme lien Ionic (utilise IonRouterLink)
 * <Button to="/profile" isLink={true} routerDirection="forward">Voir le profil</Button>
 *
 * @example Bouton avec style personnalisé
 * <Button className="my-4 rounded-full" fill="clear" size="small">Action</Button>
 */
const Button: React.FC<ButtonProps> = ({
  type = 'button',
  color = 'dark',
  disabled = false,
  expand = 'block',
  onClick,
  className = 'w-full',
  children,
  fill = 'solid',
  size = 'default',
  to,
  isLink = false,
  routerDirection = 'forward',
  style,
}) => {
  const history = useHistory();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (to && !isLink) {
      // Si to est fourni mais pas isLink, utiliser history.push
      history.push(to);
    }
  };

  // Si c'est un lien et que "to" est fourni, utiliser IonRouterLink
  if (isLink && to) {
    return (
      <IonButton
        expand={expand}
        className={className}
        color={color}
        disabled={disabled}
        fill={fill}
        size={size}
        routerLink={to}
        routerDirection={routerDirection}
      >
        {children}
      </IonButton>
    );
  }

  // Sinon, utiliser un bouton normal
  return (
    <IonButton
      expand={expand}
      className={className}
      color={color}
      type={type}
      disabled={disabled}
      onClick={handleClick}
      fill={fill}
      size={size}
      style={style}
    >
      {children}
    </IonButton>
  );
};

export default Button;
