import { IonContent, IonModal } from '@ionic/react';
import { createSelector } from '@reduxjs/toolkit';
import { X } from 'lucide-react';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { TourStatus } from '../../../../interfaces/enum/tour.enums';
import { receptionistPreloadActions } from '../../../../stores/receptionistPreloadSlice';
import { RootState } from '../../../../stores/store';
import { formatDate } from '../../../../utils/dateUtils';
import { isDev } from '../../../../utils/env';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import InlineButtons from '../../../ui/stylized/InlineButtons';
import { PreloadTourStopList } from './PreloadTourStopList';

interface PreloadTourModalProps {
  tour: ITourEntity | null;
  isOpen: boolean;
  onClose: () => void;
  onTourValidated: (tour: ITourEntity) => void;
}

const selectAreAllStopsValidatedForTourId = (tourId: string) =>
  createSelector(
    (state: RootState) => state.receptionistPreload.preloadEquipmentValidationByStop,
    (state: RootState) => state.receptionistPreload.tours,
    (preloadEquipmentValidationByStop, tours) => {
      const tour = tours.find((tour) => tour.id === tourId);

      if (!tour) {
        return false;
      }

      const stopIds = tour.stops.map((stop) => stop.id);

      return stopIds.every((stopId) => {
        const stopValidation = preloadEquipmentValidationByStop[stopId];

        return Object.values(stopValidation).every((equipment) => equipment.isValidated);
      });
    },
  );

export function PreloadTourModal({
  tour,
  isOpen,
  onClose,
  onTourValidated,
}: PreloadTourModalProps) {
  const dispatch = useAppDispatch();
  const areAllStopsValidated = useAppSelector((state) =>
    tour ? selectAreAllStopsValidatedForTourId(tour.id)(state) : false,
  );

  const handleValidateTour = () => {
    if (tour && areAllStopsValidated) {
      onTourValidated(tour);
    }
  };

  const debugValidateAllStops = () => {
    for (const stop of tour?.stops || []) {
      dispatch(
        receptionistPreloadActions.updateEquipmentValidationOnStop({
          stopId: stop.id,
          isValidated: true,
        }),
      );
    }
  };

  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
      <div className="shadow-none border-none">
        <div className="flex justify-between items-center px-4 py-4 border-b border-gray-200 bg-gray-50 primary-color">
          <div className="flex flex-col gap-2">
            <div className="text-xl font-bold">
              {tour?.status === TourStatus.Completed
                ? 'Tournée finalisée'
                : 'Pré-chargement des agrès'}{' '}
              n°{tour?.tourIdentifier.originalNumber}
            </div>
            <div className="text-sm font-medium text-gray-900/80">
              {tour && formatDate(tour.deliveryDate)}
            </div>
          </div>
          {isDev() && (
            <div className="flex items-center gap-2 cursor-pointer" onClick={debugValidateAllStops}>
              <p className="text-sm text-blue-500 font-bold">
                $DEBUG: Valider toutes les livraisons
              </p>
            </div>
          )}

          <div className="cursor-pointer" onClick={onClose}>
            <X className="h-6 w-6" />
          </div>
        </div>
      </div>

      <IonContent className="ion-padding">
        {tour && (
          <div className="pt-2 pb-20">
            {/* Liste des cartes de livraisons */}
            <PreloadTourStopList tour={tour} />
          </div>
        )}
      </IonContent>

      {/* Bouton de validation flottant dans la modal */}
      {tour && (
        <div className="fixed bottom-0 bg-white py-6 left-0 right-0 z-10 p-4">
          <div className="max-w-7xl mx-4">
            <InlineButtons
              buttons={[
                {
                  label: 'Valider le pré-chargement',
                  onClick: handleValidateTour,
                  disabled: !areAllStopsValidated,
                  classNames: {
                    label: 'text-lg font-semibold',
                    button: areAllStopsValidated
                      ? 'primary-button py-6 px-6'
                      : 'primary-button-outline opacity-50 py-6 px-6',
                  },
                },
              ]}
            />
          </div>
        </div>
      )}
    </IonModal>
  );
}
