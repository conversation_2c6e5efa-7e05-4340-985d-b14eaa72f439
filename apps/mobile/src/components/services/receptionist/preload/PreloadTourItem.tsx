import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { TourCard } from '../../../ui/shared/TourCard';

interface PreloadTourItemProps {
  tour: ITourEntity;
  onTourClick: (tour: ITourEntity) => void;
}

export function PreloadTourItem({ tour, onTourClick }: PreloadTourItemProps) {
  return <TourCard tour={tour} onClick={() => onTourClick(tour)} showDeliveryDate={true} />;
}
