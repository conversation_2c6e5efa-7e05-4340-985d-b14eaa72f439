import { IonButton, IonText } from '@ionic/react';
import { useEffect, useState } from 'react';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import {
  processEquipmentToPreloadForTourId,
  receptionistPreloadActions,
  receptionistPreloadSelectors,
} from '../../../../stores/receptionistPreloadSlice';
import { getToursWithQueueProjection } from '../../../../stores/tourSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { Loading } from '../../../layout/Loading';
import { SearchInput } from '../../../ui/shared/SearchInput';
import { PreloadTourItem } from './PreloadTourItem';
import { PreloadTourModal } from './PreloadTourModal';

export function ReceptionistPreloadTourList() {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTour, setSelectedTour] = useState<ITourEntity | null>(null);

  const globalTourArray = useAppSelector(getToursWithQueueProjection);
  const isGlobalTourDataLoaded = useAppSelector((state) => state.tour.isDataResolved);

  // Sélecteurs Redux
  const { searchTerm } = useAppSelector((state) => state.receptionistPreload);

  const filteredTours = useAppSelector(receptionistPreloadSelectors.selectFilteredTours);
  const currentUser = useAppSelector((state) => state.currentUser.user);

  const handleTourClick = (tour: ITourEntity) => {
    setSelectedTour(tour);
  };

  const handleTourValidation = (tour: ITourEntity) => {
    dispatch(processEquipmentToPreloadForTourId(tour.id));
    setSelectedTour(null);
  };

  useEffect(() => {
    if (!isGlobalTourDataLoaded) {
      return;
    }

    dispatch(receptionistPreloadActions.setTours(globalTourArray));
    setIsLoading(false);
  }, [isGlobalTourDataLoaded, globalTourArray, dispatch]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="bg-gray-50 h-full mx-1 overflow-auto pt-3 pb-12">
      <div className="max-w-7xl mx-auto pt-2 flex flex-col h-full px-4">
        {/* Header */}
        <div className="my-2 mb-6 flex flex-col justify-between gap-3">
          <div className="flex-1">
            <p className="text-2xl font-bold text-neutral-500">
              Bonjour {currentUser?.firstName || '--'}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              Contrôlez et validez les agrès de toutes les tournées a précharger
            </p>

            {/* Input de recherche */}
            <SearchInput
              value={searchTerm}
              onValueChange={(value) => dispatch(receptionistPreloadActions.setSearchTerm(value))}
              placeholder="Rechercher une tournée ou un chauffeur..."
              className="mt-3"
            />
          </div>
        </div>

        {/* Liste des tournées filtrées */}
        {filteredTours.length > 0 && (
          <div className="space-y-3">
            {filteredTours.map((tour: ITourEntity) => (
              <PreloadTourItem key={tour.id} tour={tour} onTourClick={handleTourClick} />
            ))}
          </div>
        )}

        {/* Message si aucune tournée */}
        {filteredTours.length === 0 && !searchTerm.trim() && (
          <div className="flex flex-col items-center justify-center h-64">
            <IonText className="text-gray-500 text-center">
              <p className="text-lg font-semibold">Aucune tournée à pré-charger</p>
            </IonText>
          </div>
        )}

        {/* Message si aucun résultat de recherche */}
        {filteredTours.length === 0 && searchTerm.trim() && (
          <div className="flex flex-col items-center justify-center h-64">
            <IonText className="text-gray-500 text-center">
              <p className="text-lg font-semibold">Aucune tournée trouvée</p>
              <p className="text-sm">Aucune tournée ne correspond à "{searchTerm}"</p>
              <IonButton
                fill="clear"
                size="small"
                onClick={() => dispatch(receptionistPreloadActions.clearSearchTerm())}
                className="mt-2 text-blue-600"
              >
                Effacer la recherche
              </IonButton>
            </IonText>
          </div>
        )}

        {/* Modal de pré-chargement */}
        {selectedTour && (
          <PreloadTourModal
            tour={selectedTour}
            isOpen={!!selectedTour}
            onClose={() => setSelectedTour(null)}
            onTourValidated={handleTourValidation}
          />
        )}
      </div>
    </div>
  );
}
