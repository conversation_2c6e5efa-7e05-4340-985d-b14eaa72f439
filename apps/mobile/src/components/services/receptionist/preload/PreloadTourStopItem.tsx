import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { LogisticsEquipmentKind } from '../../../../interfaces/enum/logistics-equipment-kind.enum';
import { receptionistPreloadActions } from '../../../../stores/receptionistPreloadSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { EquipmentValidationSection } from '../../../services/deliverer/loading-control/EquipmentValidationSection';

interface PreloadTourStopItemProps {
  stop: IStopEntity;
}

export function PreloadTourStopItem({ stop }: PreloadTourStopItemProps) {
  const dispatch = useAppDispatch();

  const equipmentItemsRecords = useAppSelector(
    (state) => state.receptionistPreload.preloadEquipmentValidationByStop[stop.id],
  );

  const handleEquipmentChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    newQuantity: number,
  ) => {
    dispatch(
      receptionistPreloadActions.updateEquipmentQuantity({
        stopId: stop.id,
        equipmentKind: logisticEquipmentKind,
        quantity: newQuantity,
      }),
    );
  };

  const handleValidationChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    isValid: boolean,
  ) => {
    dispatch(
      receptionistPreloadActions.updateEquipmentValidation({
        stopId: stop.id,
        equipmentKind: logisticEquipmentKind,
        isValidated: isValid,
      }),
    );
  };

  const handleValidationChangeOnStop = (stopId: string, isValid: boolean) => {
    dispatch(
      receptionistPreloadActions.updateEquipmentValidationOnStop({
        stopId,
        isValidated: isValid,
      }),
    );
  };

  return (
    <EquipmentValidationSection
      stop={stop}
      equipmentRecords={equipmentItemsRecords}
      onEquipmentChange={handleEquipmentChange}
      onValidationChange={handleValidationChange}
      onValidationChangeOnStop={handleValidationChangeOnStop}
    />
  );
}
