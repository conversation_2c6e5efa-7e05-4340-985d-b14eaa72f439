import { IonText } from '@ionic/react';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { PreloadTourStopItem } from './PreloadTourStopItem';

interface PreloadTourStopListProps {
  tour: ITourEntity;
}

export function PreloadTourStopList({ tour }: PreloadTourStopListProps) {
  if (!tour.stops || tour.stops.length === 0) {
    return (
      <div className="text-center py-8">
        <IonText>
          <p className="text-lg text-neutral-500">Aucune livraison dans cette tournée</p>
        </IonText>
      </div>
    );
  }

  return (
    <div className="space-y-4 mb-8">
      {tour.stops.map((stop) => (
        <PreloadTourStopItem key={stop.id} stop={stop} />
      ))}
    </div>
  );
}
