import { IonIcon, IonText } from '@ionic/react';
import { add, remove } from 'ionicons/icons';
import { getLogisticsEquipmentDisplayName } from '../../../constants/logistics-equipment-display-names';
import { LogisticsEquipmentKind } from '../../../interfaces/enum/logistics-equipment-kind.enum';

export const getEmptyEquipmentValidationItem = (
  initialQuantity = 0,
): EquipmentItemForValidationItemType => {
  return {
    initialQuantity,
    adjustedQuantity: initialQuantity,
    isValidated: false,
  };
};

export const getEmptyEquipmentValidationRecord = (
  palletCount = 0,
  rollCount = 0,
  packageCount = 0,
): EquipmentValidationRecord => {
  return {
    [LogisticsEquipmentKind.PALLET]: getEmptyEquipmentValidationItem(palletCount),
    [LogisticsEquipmentKind.ROLL]: getEmptyEquipmentValidationItem(rollCount),
    [LogisticsEquipmentKind.PACKAGE]: getEmptyEquipmentValidationItem(packageCount),
  };
};

export interface EquipmentItemForValidationItemType {
  initialQuantity: number;
  adjustedQuantity: number;
  isValidated: boolean;
}

export type EquipmentValidationRecord = Record<
  LogisticsEquipmentKind,
  EquipmentItemForValidationItemType
>;

export interface EquipmentValidatorProps {
  equipmentRecords: EquipmentValidationRecord;
  onEquipmentChange: (logisitEquipmentKind: LogisticsEquipmentKind, newValue: number) => void;
  onValidationChange?: (logisitEquipmentKind: LogisticsEquipmentKind, isValid: boolean) => void;
  hideValidationCheckbox?: boolean;
}

export const EquipmentValidator = ({
  equipmentRecords,
  onEquipmentChange,
  onValidationChange,
  hideValidationCheckbox = false,
}: EquipmentValidatorProps) => {
  const handleValidationChange = (equipmentKind: LogisticsEquipmentKind, isValid: boolean) => {
    onValidationChange?.(equipmentKind, isValid);
  };

  const handleQuantityChange = (equipmentKind: LogisticsEquipmentKind, newQuantity: number) => {
    if (newQuantity < 0) {
      newQuantity = 0;
    }

    onEquipmentChange(equipmentKind, newQuantity);
  };

  const incrementQuantity = (equipmentKind: LogisticsEquipmentKind) => {
    handleQuantityChange(equipmentKind, equipmentRecords[equipmentKind].adjustedQuantity + 1);
  };

  const decrementQuantity = (equipmentKind: LogisticsEquipmentKind) => {
    handleQuantityChange(equipmentKind, equipmentRecords[equipmentKind].adjustedQuantity - 1);
  };

  if (Object.keys(equipmentRecords).length === 0) {
    return (
      <div className="text-center py-4">
        <IonText>
          <p className="text-sm text-neutral-500">Aucun équipement à valider</p>
        </IonText>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {Object.entries(equipmentRecords).map(([equipmentKind, item]) => {
        const kind = equipmentKind as LogisticsEquipmentKind;

        return (
          <div
            key={equipmentKind}
            className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm"
          >
            <div className="flex items-center justify-between">
              {/* Section gauche : Checkbox + Info */}
              <div className="flex items-center gap-3 flex-1">
                {!hideValidationCheckbox && (
                  <input
                    type="checkbox"
                    onChange={(e) => handleValidationChange(kind, e.target.checked)}
                    checked={item.isValidated}
                    className="h-5 w-5 primary-color border-gray-300 rounded "
                  />
                )}

                <div className="flex-1">
                  <span className="font-semibold text-gray-800 text-sm">
                    {getLogisticsEquipmentDisplayName(kind)}
                  </span>
                </div>
              </div>

              {/* Section droite : Contrôles de quantité */}
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => decrementQuantity(kind)}
                  disabled={item.adjustedQuantity <= 0}
                  className="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <IonIcon icon={remove} className="text-gray-600" />
                </button>

                <div className="w-12 text-center">
                  {/*<span className="text-sm font-medium text-gray-800">{item.adjustedQuantity}</span>*/}
                  <input
                    type={'number'}
                    className={
                      'w-full text-center text-sm font-medium text-gray-800 border border-gray-300 rounded p-1'
                    }
                    value={item.adjustedQuantity}
                    onChange={(e) => handleQuantityChange(kind, parseInt(e.target.value, 10))}
                    min={0}
                  />
                </div>

                <button
                  type="button"
                  onClick={() => incrementQuantity(kind)}
                  className="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-full"
                >
                  <IonIcon icon={add} className="text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
