import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { LogisticsEquipmentKind } from '../../../../interfaces/enum/logistics-equipment-kind.enum';
import { delivererLoadingControlActions } from '../../../../stores/delivererLoadingControlSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { EquipmentValidationSection } from './EquipmentValidationSection';

export function LoadingControlStopItem({ stop }: { stop: IStopEntity }) {
  const equipmentItems = useAppSelector(
    (state) => state.delivererLoadingControl.loadEquipmentValidationByStop[stop.id],
  );
  const dispatch = useAppDispatch();

  const handleEquipmentChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    newQuantity: number,
  ) => {
    dispatch(
      delivererLoadingControlActions.updateEquipmentQuantity({
        stopId: stop.id,
        equipmentKind: logisticEquipmentKind,
        quantity: newQuantity,
      }),
    );
  };

  const handleValidationChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    isValid: boolean,
  ) => {
    dispatch(
      delivererLoadingControlActions.updateEquipmentValidation({
        stopId: stop.id,
        equipmentKind: logisticEquipmentKind,
        isValidated: isValid,
      }),
    );
  };

  const handleValidationChangeOnStop = (stopId: string, isValid: boolean) => {
    dispatch(
      delivererLoadingControlActions.updateEquipmentValidationOnStop({
        stopId,
        isValidated: isValid,
      }),
    );
  };

  return (
    <EquipmentValidationSection
      stop={stop}
      equipmentRecords={equipmentItems}
      onEquipmentChange={handleEquipmentChange}
      onValidationChange={handleValidationChange}
      onValidationChangeOnStop={handleValidationChangeOnStop}
    />
  );
}
