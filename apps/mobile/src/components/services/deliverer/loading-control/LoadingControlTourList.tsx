import { Ion<PERSON>abel, IonSegment, IonSegmentButton } from '@ionic/react';
import { ArrowLeft } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import {
  delivererLoadingControlActions,
  delivererLoadingControlSelectors,
  processEquipmentToLoadForAllTours,
} from '../../../../stores/delivererLoadingControlSlice';
import { isDev } from '../../../../utils/env';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import InlineButtons from '../../../ui/stylized/InlineButtons';
import { LoadingControlStopItem } from './LoadingControlStopItem';

export function LoadingControlTourList() {
  const currentStep = useAppSelector((state) => state.delivererLoadingControl.loadingStep);
  const toursForCurrentStep = useAppSelector(delivererLoadingControlSelectors.toursForCurrentStep);
  const currentTourId = useAppSelector((state) => state.delivererLoadingControl.currentTourId);
  const currentTour = useAppSelector((state) =>
    currentTourId ? state.delivererLoadingControl.tours[currentTourId] : null,
  );

  const areAllStopsValidatedForCurrentStep = useAppSelector(
    delivererLoadingControlSelectors.areAllStopsValidatedForCurrentStep,
  );

  useEffect(() => {
    if (toursForCurrentStep.length > 0) {
      dispatch(delivererLoadingControlActions.setCurrentTourId(toursForCurrentStep[0].id));
    } else if (currentStep === 'frozen') {
      dispatch(delivererLoadingControlActions.setLoadingStep('regular'));
    }
  }, [toursForCurrentStep, currentStep]);

  const dispatch = useAppDispatch();
  const history = useHistory();

  const handleTourChange = (tourId: string) => {
    dispatch(delivererLoadingControlActions.setCurrentTourId(tourId));
  };

  const handleBackClick = () => {
    history.goBack();
  };

  const isOnLastPage = useMemo(() => {
    return currentStep === 'regular';
  }, [currentStep]);

  const handleNextClick = () => {
    if (!areAllStopsValidatedForCurrentStep) {
      return;
    }

    if (isOnLastPage) {
      dispatch(processEquipmentToLoadForAllTours());
      history.push('/admin/deliverer/tours');
    } else {
      dispatch(delivererLoadingControlActions.setLoadingStep('regular'));
    }
  };

  const debugValidateAllStops = () => {
    for (const stop of currentTour?.stops || []) {
      dispatch(
        delivererLoadingControlActions.updateEquipmentValidationOnStop({
          stopId: stop.id,
          isValidated: true,
        }),
      );
    }
  };

  return (
    <div>
      <div className="max-w-7xl mx-auto pt-2 sm:pt-4 md:pt-6 flex flex-col h-full px-2 sm:px-4 md:px-6 lg:px-8">
        {/* En-tête avec informations */}
        <div className="my-2 sm:my-4 mb-6 sm:mb-8 md:mb-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4">
          <div className="flex items-center gap-2 cursor-pointer" onClick={handleBackClick}>
            <ArrowLeft className="h-6 w-6" />
            <p className="text-2xl font-bold text-neutral-700">
              {currentStep === 'frozen' && 'Chargement surgelés'}
              {currentStep === 'regular' && 'Chargement frais'}
            </p>
          </div>
          {isDev() && (
            <div className="flex items-center gap-2 cursor-pointer" onClick={debugValidateAllStops}>
              <p className="text-sm text-blue-500 font-bold">
                $DEBUG: Valider toutes les livraisons
              </p>
            </div>
          )}
        </div>

        <IonSegment
          value={currentTourId ?? undefined}
          onIonChange={(e) => handleTourChange(e.detail.value as string)}
          className="bg-white rounded-lg shadow-sm h-16"
          scrollable
        >
          {toursForCurrentStep.map((tour) => (
            <IonSegmentButton
              key={tour.id}
              value={tour.id}
              className={`border-neutral-200 border-r-2 border-l-2 `}
            >
              <IonLabel>N°{tour.tourIdentifier.originalNumber}</IonLabel>
            </IonSegmentButton>
          ))}
        </IonSegment>
      </div>

      <div>
        {!currentTour?.stops.length ? (
          <div>Aucune livraison dans cette tournée</div>
        ) : (
          <div className="flex flex-col gap-4 pt-4 px-8 pb-16">
            {currentTour?.stops.map((stop) => (
              <LoadingControlStopItem key={stop.id} stop={stop} />
            ))}
          </div>
        )}
      </div>

      <div className=" fixed  h-16 w-full bottom-0 bg-white flex items-center justify-center">
        <div className="max-w-7xl mx-auto">
          <InlineButtons
            buttons={[
              {
                disabled: !areAllStopsValidatedForCurrentStep,
                label: isOnLastPage ? 'Commencer les tournées' : 'Suivant',
                onClick: handleNextClick,
                classNames: {
                  button: `primary-button py-4`,
                  label: 'text-lg font-semibold',
                },
              },
            ]}
          />
        </div>
      </div>
    </div>
  );
}
