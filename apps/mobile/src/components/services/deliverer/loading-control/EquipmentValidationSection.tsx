/**
 * @file EquipmentValidationSection.tsx
 * @description Composant pour la validation des équipements d'un arrêt
 * dans le contexte du contrôle de chargement
 */

import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import {
  EquipmentValidator,
  EquipmentValidatorProps,
} from '../../../services/deliverer/EquipmentValidator';
import { StopCard } from '../../../ui/shared/StopCard';

interface EquipmentValidationSectionProps extends EquipmentValidatorProps {
  stop: IStopEntity;
  onValidationChangeOnStop: (stopId: string, isValid: boolean) => void;
}

/**
 * Composant principal pour la section de validation des équipements
 */
export const EquipmentValidationSection: React.FC<EquipmentValidationSectionProps> = ({
  stop,
  equipmentRecords,
  onEquipmentChange,
  onValidationChange,
  onValidationChangeOnStop,
}) => {
  const handleValidationChangeOnStop = (isValid: boolean) => {
    onValidationChangeOnStop(stop.id, isValid);
  };

  return (
    <StopCard
      stop={stop}
      equipmentItems={equipmentRecords}
      onValidationChange={handleValidationChangeOnStop}
    >
      <EquipmentValidator
        key={`${stop.id}-equipment-validator`}
        equipmentRecords={equipmentRecords}
        onEquipmentChange={onEquipmentChange}
        onValidationChange={onValidationChange}
      />
    </StopCard>
  );
};
