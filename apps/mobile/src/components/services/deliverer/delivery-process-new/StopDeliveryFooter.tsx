import InlineButtons from '../../../ui/stylized/InlineButtons';

export function StopDeliveryFooter({
  isDisabled,
  onClick,
}: {
  isDisabled: boolean;
  onClick: () => void;
}) {
  return (
    <div className="absolute bottom-6 left-1/2 right-1/2 w-full max-w-3xl transform -translate-x-1/2  px-4 z-20 ">
      <InlineButtons
        buttons={[
          {
            label: 'Suivant',
            onClick: isDisabled ? () => {} : onClick,
            classNames: {
              button: `px-8 sm:px-12 py-5 sm:py-4 ${isDisabled ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'primary-button'}`,
            },
            disabled: isDisabled,
          },
        ]}
      />
    </div>
  );
}
