import { IonCheckbox, IonIcon, IonText } from '@ionic/react';
import { warning } from 'ionicons/icons';
import { ChevronLeft } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { deliveryProcessActions } from '../../../../stores/deliveryProcessSlice';
import { formatDateShort } from '../../../../utils/dateUtils';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { StopDeliveryIncidentModal } from './StopDeliveryIncidentModal';

function BackButton() {
  const history = useHistory();
  const currentStep = useAppSelector((state) => state.deliveryProcess.currentStep);
  const dispatch = useAppDispatch();

  const canGoToPreviousStep = currentStep !== 'delivery-details' && currentStep !== 'success';

  const backButtonTitle = useMemo(() => {
    const backButtonTextMap = {
      'delivery-details': 'Livraison',
      payment: 'Paiement',
      'equipment-deposit': 'Dépôt agrès',
      'equipment-pickup': 'Ramasse agrès',
      attachments: 'Commentaire client',
      signature: 'Signature',
      success: 'Livraison terminée',
    };

    if (canGoToPreviousStep) {
      return backButtonTextMap[currentStep];
    }

    return 'Retour';
  }, [currentStep, canGoToPreviousStep]);

  const handleBackClick = useCallback(() => {
    if (canGoToPreviousStep) {
      dispatch(deliveryProcessActions.goBack());
    } else {
      history.goBack();
    }
  }, [canGoToPreviousStep, history]);

  return (
    <div
      className="flex items-center gap-2 cursor-pointer p-2 sm:p-3 rounded-lg hover:bg-gray-100 active:bg-gray-200"
      onClick={handleBackClick}
    >
      <ChevronLeft size={30} />

      <IonText color="medium" className="text-xl">
        {backButtonTitle}
      </IonText>
    </div>
  );
}

export function StopDeliveryHeader() {
  const stop = useAppSelector((state) => state.deliveryProcess.stop);
  const tour = useAppSelector((state) => state.deliveryProcess.tour);
  const [isIncidentModalOpen, setIsIncidentModalOpen] = useState(false);
  const toPayAmount = useMemo(() => {
    if (!stop.shipmentLines) {
      return 0;
    }

    const amount = stop.shipmentLines.reduce(
      (acc, shipmentLine) => acc + Number(shipmentLine.amount),
      0,
    );

    return amount;
  }, [stop.shipmentLines]);

  function handleIncidentClick() {
    setIsIncidentModalOpen(true);
  }

  return (
    <div className="mb-6 pt-2 sm:pt-4 md:pt-6">
      <div className="flex justify-between items-center mb-3 sm:mb-4">
        <BackButton />
        <IonText color="medium" className="text-sm">
          {tour.deliveryDate ? formatDateShort(tour.deliveryDate) : '--'}
        </IonText>
      </div>

      <div className="">
        <IonText className="text-lg font-bold">{stop.originalClientInfo?.name}</IonText>
        <br />
        {Object.entries(stop.originalClientInfo?.address || {})
          .filter(([_, value]) => value?.trim() !== '' && Boolean(value))
          .map(([key, value]) => (
            <>
              <IonText className="text-sm font-lightl" key={key}>
                {value}
              </IonText>
              <br />
            </>
          ))}
      </div>

      <div className="flex gap-2 sm:gap-3 md:gap-4 mb-4 sm:mb-6 justify-between flex-wrap">
        <div
          onClick={handleIncidentClick}
          className="flex items-center cursor-pointer p-2 sm:p-3 text-red-600 underline rounded-lg hover:bg-gray-100 active:bg-gray-200"
        >
          <IonIcon icon={warning} size="small" className="text-xl sm:text-2xl md:text-3xl mr-2" />
          <IonText className="text-sm">Incident</IonText>
        </div>

        <div className="flex items-center gap-2">
          <IonCheckbox checked={toPayAmount === 0} disabled={true} />
          <IonText className="text-sm sm:text-base font-medium text-gray-800">Comptant</IonText>
        </div>
      </div>
      <StopDeliveryIncidentModal
        isOpen={isIncidentModalOpen}
        onClose={() => setIsIncidentModalOpen(false)}
      />
    </div>
  );
}
