import { useMemo, useState } from 'react';
import { LogisticsEquipmentKind } from '../../../../../interfaces/enum/logistics-equipment-kind.enum';
import { deliveryProcessActions } from '../../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../../utils/redux';
import { EquipmentValidator } from '../../EquipmentValidator';
import { StopDeliveryFooter } from '../StopDeliveryFooter';

export function EquipmentPickupStep() {
  const stopCompletionFormData = useAppSelector(
    (state) => state.deliveryProcess.stopCompletionFormData,
  );
  const dispatch = useAppDispatch();
  const [validatedKinds, setValidatedKinds] = useState<LogisticsEquipmentKind[]>([]);

  const equipmentItems = useMemo(() => {
    return {
      [LogisticsEquipmentKind.PALLET]: {
        initialQuantity: stopCompletionFormData.returnedEquipment?.palletCount || 0,
        adjustedQuantity: stopCompletionFormData.returnedEquipment?.palletCount || 0,
        isValidated: validatedKinds.includes(LogisticsEquipmentKind.PALLET),
      },
      [LogisticsEquipmentKind.ROLL]: {
        initialQuantity: stopCompletionFormData.returnedEquipment?.rollCount || 0,
        adjustedQuantity: stopCompletionFormData.returnedEquipment?.rollCount || 0,
        isValidated: validatedKinds.includes(LogisticsEquipmentKind.ROLL),
      },
      [LogisticsEquipmentKind.PACKAGE]: {
        initialQuantity: stopCompletionFormData.returnedEquipment?.packageCount || 0,
        adjustedQuantity: stopCompletionFormData.returnedEquipment?.packageCount || 0,
        isValidated: validatedKinds.includes(LogisticsEquipmentKind.PACKAGE),
      },
    };
  }, [stopCompletionFormData.returnedEquipment, validatedKinds]);

  const handleEquipmentChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    newValue: number,
  ) => {
    const kindToPropertyMap = {
      [LogisticsEquipmentKind.PALLET]: 'palletCount',
      [LogisticsEquipmentKind.ROLL]: 'rollCount',
      [LogisticsEquipmentKind.PACKAGE]: 'packageCount',
    };

    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        returnedEquipment: {
          ...stopCompletionFormData.returnedEquipment,
          [kindToPropertyMap[logisticEquipmentKind]]: newValue,
        },
      }),
    );
  };

  const handleValidationChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    isValid: boolean,
  ) => {
    if (isValid) {
      setValidatedKinds((prev) => [...prev, logisticEquipmentKind]);
    } else {
      setValidatedKinds((prev) => prev.filter((kind) => kind !== logisticEquipmentKind));
    }
  };

  return (
    <>
      <div className="pt-6 pb-6">
        <EquipmentValidator
          equipmentRecords={equipmentItems}
          onEquipmentChange={handleEquipmentChange}
          onValidationChange={handleValidationChange}
        />
      </div>

      <StopDeliveryFooter
        isDisabled={validatedKinds.length !== 3}
        onClick={() => dispatch(deliveryProcessActions.setNextStep())}
      />
    </>
  );
}
