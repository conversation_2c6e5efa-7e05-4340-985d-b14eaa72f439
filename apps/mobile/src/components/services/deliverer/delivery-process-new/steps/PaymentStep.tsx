import { IonCheckbox, IonItem, IonText } from '@ionic/react';
import { useEffect, useMemo, useState } from 'react';
import { deliveryProcessActions } from '../../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../../utils/redux';
import { ClientCard } from '../../../../ui/shared/ClientCard';
import { StopDeliveryFooter } from '../StopDeliveryFooter';

export function PaymentStep() {
  const stop = useAppSelector((state) => state.deliveryProcess.stop);
  const dispatch = useAppDispatch();

  const [isPaid, setIsPaid] = useState(false);

  const totalAmountToPay = useMemo(() => {
    return stop.shipmentLines?.reduce((acc, shipmentLine) => acc + Number(shipmentLine.amount), 0);
  }, [stop.shipmentLines]);

  const isAlreadyPaid = useMemo(() => {
    return totalAmountToPay === 0;
  }, [stop.shipmentLines]);

  useEffect(() => {
    if (isAlreadyPaid) {
      setIsPaid(true);
    }
  }, [isAlreadyPaid]);

  const handlePaymentToggle = (checked: boolean) => {
    setIsPaid(checked);
  };

  const devise = 'CHF';

  return (
    <>
      <div className="ion-padding pt-6 pb-6">
        <ClientCard stop={stop} className={'mb-4'} />

        <div className="mb-6 mt-10">
          <div className="flex justify-between items-center">
            <IonText className="text-base sm:text-lg md:text-xl font-bold text-black">
              À régler
            </IonText>
            <IonText className="text-base sm:text-lg md:text-xl font-semibold text-black">
              {totalAmountToPay} {devise}
            </IonText>
          </div>
        </div>

        <div className="mb-8 flex justify-between items-center">
          <IonItem lines="none" className="ion-no-padding">
            <IonCheckbox
              slot="start"
              checked={isPaid}
              onIonChange={(e) => handlePaymentToggle(e.detail.checked)}
              className="mr-3"
              color="success"
            />
            <IonText className="text-base sm:text-lg">Payé</IonText>
          </IonItem>
          <IonText className="text-base sm:text-lg md:text-xl font-semibold text-black">
            {totalAmountToPay} {devise}
          </IonText>
        </div>

        {isPaid && (
          <div className="mb-8 p-4 rounded-lg bg-green-100 border border-green-200">
            <IonText className="text-base sm:text-lg md:text-xl font-semibold text-green-800">
              Montant payé: {totalAmountToPay} {devise}
            </IonText>
          </div>
        )}
      </div>

      <StopDeliveryFooter
        isDisabled={!isPaid}
        onClick={() => dispatch(deliveryProcessActions.setNextStep())}
      />
    </>
  );
}
