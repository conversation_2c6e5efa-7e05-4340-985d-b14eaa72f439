import { IonIcon, IonText } from '@ionic/react';
import { snow } from 'ionicons/icons';
import { Weight } from 'lucide-react';
import { deliveryProcessActions } from '../../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../../utils/redux';
import { ClientCard } from '../../../../ui/shared/ClientCard';
import InlineButtons from '../../../../ui/stylized/InlineButtons';
import { StopDeliveryFooter } from '../StopDeliveryFooter';

export function DeliveryDetailStep() {
  const stop = useAppSelector((state) => state.deliveryProcess.stop);
  const dispatch = useAppDispatch();

  const weight = stop.shipmentLines?.reduce(
    (acc, shipmentLine) => acc + Number(shipmentLine.weightKg),
    0,
  );

  const onClientAbsent = () => {
    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        isSecureLocation: true,
      }),
    );
    dispatch(deliveryProcessActions.setNextStep());
  };

  return (
    <>
      <div className="ion-padding pt-6 pb-6">
        <ClientCard stop={stop} className="mb-4" />

        <div className="mb-24 flex items-center gap-4 justify-between">
          <div className="flex items-center gap-2">
            <Weight size={16} className={'text-gray-500'} />
            <IonText className="font-semibold text-gray-500 text-sm sm:text-base">
              {weight || '--'} Kg
            </IonText>
          </div>
          {stop.shipmentLines?.[0]?.isFrozen && (
            <div className="flex items-center gap-2">
              <IonIcon icon={snow} color="medium" size="small" className="text-gray-500" />
              <IonText className="font-medium text-gray-500 text-sm sm:text-base">SURGELÉS</IonText>
            </div>
          )}
        </div>

        <div className="space-y-3 flex flex-col gap-4 justify-center items-center">
          <InlineButtons
            buttons={[
              {
                label: 'Client absent',
                onClick: onClientAbsent,
                classNames: {
                  button: 'primary-button-outline w-fit',
                },
              },
            ]}
          />
        </div>
      </div>

      <StopDeliveryFooter
        isDisabled={false}
        onClick={() => dispatch(deliveryProcessActions.setNextStep())}
      />
    </>
  );
}
