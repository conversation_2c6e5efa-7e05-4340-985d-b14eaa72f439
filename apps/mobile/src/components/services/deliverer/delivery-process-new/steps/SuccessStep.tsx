import { IonIcon, IonText } from '@ionic/react';
import { checkmarkCircle } from 'ionicons/icons';
import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { PATHS } from '../../../../../config/routes';
import {
  completeStopDelivery,
  deliveryProcessActions,
} from '../../../../../stores/deliveryProcessSlice';
import { useAppDispatch } from '../../../../../utils/redux';

export function SuccessStep() {
  const dispatch = useAppDispatch();
  const history = useHistory();

  useEffect(() => {
    dispatch(completeStopDelivery());
  }, []);

  const handleFinish = () => {
    history.push(PATHS.TOURS);
    dispatch(deliveryProcessActions.reset());
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-96">
      {/* Icône de succès verte */}
      <div className="mb-6 sm:mb-8">
        <div className="w-16 sm:w-18 md:w-20 h-16 sm:h-18 md:h-20 bg-green-500 rounded-full flex items-center justify-center">
          <IonIcon icon={checkmarkCircle} className="text-white text-2xl sm:text-3xl md:text-4xl" />
        </div>
      </div>

      {/* Message de succès */}
      <div className="mb-6 sm:mb-8 text-center">
        <IonText className="text-xl sm:text-2xl md:text-3xl font-bold text-green-600 mb-2 sm:mb-3 block">
          LIVRAISON TERMINÉE !
        </IonText>
        <IonText className="text-base sm:text-lg md:text-xl text-gray-600">
          Toutes les étapes ont été complétées avec succès
        </IonText>
      </div>

      {/* Résumé rapide */}
      <div className="mb-6 sm:mb-8 p-3 sm:p-4 bg-green-50 rounded-lg border border-green-200 w-full max-w-sm">
        <IonText className="text-xs sm:text-sm md:text-base text-green-800 text-center block">
          ✓ Livraison enregistrée avec succès
        </IonText>
      </div>

      {/* Bouton terminer */}
      <div className="w-full max-w-xs">
        <button
          onClick={handleFinish}
          className="primary-button w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold"
        >
          TERMINER
        </button>
      </div>
    </div>
  );
}
