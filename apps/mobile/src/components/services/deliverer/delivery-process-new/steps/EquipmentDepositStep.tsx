import { useMemo, useState } from 'react';
import { LogisticsEquipmentKind } from '../../../../../interfaces/enum/logistics-equipment-kind.enum';
import { deliveryProcessActions } from '../../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../../utils/redux';
import { EquipmentValidator } from '../../EquipmentValidator';
import { StopDeliveryFooter } from '../StopDeliveryFooter';

export function EquipmentDepositStep() {
  const stopCompletionFormData = useAppSelector(
    (state) => state.deliveryProcess.stopCompletionFormData,
  );
  const dispatch = useAppDispatch();
  const [validatedKinds, setValidatedKinds] = useState<LogisticsEquipmentKind[]>([]);

  const equipmentItems = useMemo(() => {
    return {
      [LogisticsEquipmentKind.PALLET]: {
        initialQuantity: stopCompletionFormData.unloadedEquipment?.palletCount || 0,
        adjustedQuantity: stopCompletionFormData.unloadedEquipment?.palletCount || 0,
        isValidated: validatedKinds.includes(LogisticsEquipmentKind.PALLET),
      },
      [LogisticsEquipmentKind.ROLL]: {
        initialQuantity: stopCompletionFormData.unloadedEquipment?.rollCount || 0,
        adjustedQuantity: stopCompletionFormData.unloadedEquipment?.rollCount || 0,
        isValidated: validatedKinds.includes(LogisticsEquipmentKind.ROLL),
      },
      [LogisticsEquipmentKind.PACKAGE]: {
        initialQuantity: stopCompletionFormData.unloadedEquipment?.packageCount || 0,
        adjustedQuantity: stopCompletionFormData.unloadedEquipment?.packageCount || 0,
        isValidated: validatedKinds.includes(LogisticsEquipmentKind.PACKAGE),
      },
    };
  }, [stopCompletionFormData.unloadedEquipment, validatedKinds]);

  const handleEquipmentChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    newValue: number,
  ) => {
    const kindToPropertyMap = {
      [LogisticsEquipmentKind.PALLET]: 'palletCount',
      [LogisticsEquipmentKind.ROLL]: 'rollCount',
      [LogisticsEquipmentKind.PACKAGE]: 'packageCount',
    };

    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        unloadedEquipment: {
          ...stopCompletionFormData.unloadedEquipment,
          [kindToPropertyMap[logisticEquipmentKind]]: newValue,
        },
      }),
    );
  };

  const handleValidationChange = (
    logisticEquipmentKind: LogisticsEquipmentKind,
    isValid: boolean,
  ) => {
    if (isValid) {
      setValidatedKinds((prev) => [...prev, logisticEquipmentKind]);
    } else {
      setValidatedKinds((prev) => prev.filter((kind) => kind !== logisticEquipmentKind));
    }
  };

  return (
    <>
      <div className="pt-6 pb-6">
        <EquipmentValidator
          equipmentRecords={equipmentItems}
          onEquipmentChange={handleEquipmentChange}
          onValidationChange={handleValidationChange}
        />
      </div>

      <StopDeliveryFooter
        isDisabled={validatedKinds.length !== 3}
        onClick={() => dispatch(deliveryProcessActions.setNextStep())}
      />
    </>
  );
}
