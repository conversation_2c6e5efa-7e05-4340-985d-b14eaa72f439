import { IonIcon, IonModal, IonText, IonTextarea, useIonToast } from '@ionic/react';
import { camera } from 'ionicons/icons';
import { useMemo, useState } from 'react';
import { Base64, fileSystemService } from '../../../../../services/FileSystemService';
import { deliveryProcessActions } from '../../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../../utils/redux';
import PhotoCapture from '../../../../ui/PhotoCapture';
import { StopDeliveryFooter } from '../StopDeliveryFooter';

export function AttachmentsStep() {
  const dispatch = useAppDispatch();
  const [presentToast] = useIonToast();
  const stopCompletionFormData = useAppSelector(
    (state) => state.deliveryProcess.stopCompletionFormData,
  );
  const stop = useAppSelector((state) => state.deliveryProcess.stop);

  const [showPhotoModal, setShowPhotoModal] = useState(false);

  const photoSlots = useMemo(() => {
    return Array.from({ length: 4 }, (_, index) => {
      const photoFile = stopCompletionFormData.photoFiles?.[index];

      return photoFile?.base64 || null;
    });
  }, [stopCompletionFormData.photoFiles]);

  const comment = stopCompletionFormData.comments || '';

  const setComment = (comment: string) => {
    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        comments: comment,
      }),
    );
  };

  // Handler de capture photo
  const handlePhotoCapture = async (photoData: Base64) => {
    setShowPhotoModal(false);
    const fileName = `photo_${photoSlots.length + 1}.jpg`;
    const id = await fileSystemService.saveFile(photoData, fileName, 'image/jpeg', {
      ttl: 1000 * 60 * 60 * 24 * 7, // 7 jours
      tags: ['photo', 'delivery-process'],
    });

    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        photoFiles: [
          ...(stopCompletionFormData.photoFiles || []),
          {
            filename: fileName,
            mimeType: 'image/jpeg',
            base64: photoData,
            systemFilePath: id,
          },
        ],
      }),
    );
  };

  const handlePhotoClick = (index: number) => {
    // Si la photo est vide, ouvrir la caméra
    if (photoSlots[index] == null) {
      setShowPhotoModal(true);
    }
  };

  const handlePhotoDelete = (index: number) => {
    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        photoFiles: stopCompletionFormData.photoFiles?.filter((_, i) => i !== index) || [],
      }),
    );
    presentToast({
      message: 'Photo supprimée',
      duration: 2000,
      color: 'medium',
    });
  };

  const isFormValid = useMemo(() => {
    if (!stopCompletionFormData.isSecureLocation) {
      return true;
    }

    return (stopCompletionFormData?.photoFiles?.length || 0) > 0;
  }, [stopCompletionFormData]);

  const handleSubmit = () => {
    if (stopCompletionFormData.isSecureLocation) {
      dispatch(deliveryProcessActions.setCurrentStep('success'));
    } else {
      dispatch(deliveryProcessActions.setNextStep());
    }
  };

  return (
    <>
      <div className="pt-6 pb-6">
        {/* Section avec bouton caméra central du design original */}
        <div className="flex justify-center mb-6 pt-6">
          <div
            className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-black rounded-full flex items-center justify-center cursor-pointer"
            onClick={() => setShowPhotoModal(true)}
          >
            <IonIcon icon={camera} className="text-white text-xl sm:text-2xl md:text-3xl" />
          </div>
        </div>

        {/* Section avec les 4 photos du design original */}
        <div className="grid grid-cols-4 gap-2 sm:gap-4 md:gap-6 mb-6 sm:mb-8">
          {photoSlots.map((photo, index) => (
            <div
              key={index}
              className="relative aspect-square border-2 border-gray-300 rounded-lg flex items-center justify-center cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
              onClick={() => handlePhotoClick(index)}
            >
              {photo && photo !== '' ? (
                <>
                  <img
                    src={photo}
                    alt={`Photo ${index + 1}`}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  {/* Pastille rouge de suppression en absolute à droite */}
                  <button
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg z-10 hover:bg-red-600 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation(); // Empêcher le clic sur la photo
                      handlePhotoDelete(index);
                    }}
                  >
                    ×
                  </button>
                </>
              ) : (
                <div className="text-gray-400 text-lg sm:text-2xl md:text-3xl">×</div>
              )}
            </div>
          ))}
        </div>

        {/* Section Commentaire du design original */}
        <div className="mb-8 sm:mb-10 md:mb-12">
          <IonText className="text-sm sm:text-base md:text-lg font-bold text-gray-800 block mb-3 sm:mb-4">
            COMMENTAIRE
          </IonText>
          <IonTextarea
            placeholder="Ajouter des commentaires sur la livraison..."
            value={comment}
            onIonInput={(e) => setComment(e.detail.value || '')}
            rows={4}
            className="border border-gray-300 rounded-lg bg-white text-sm sm:text-base md:text-lg"
            fill="outline"
          />
        </div>

        {/* Modal Photo */}
        <IonModal isOpen={showPhotoModal} onDidDismiss={() => setShowPhotoModal(false)}>
          <PhotoCapture
            onPhotoCapture={handlePhotoCapture}
            onCancel={() => setShowPhotoModal(false)}
          />
        </IonModal>
      </div>

      <StopDeliveryFooter isDisabled={!isFormValid} onClick={handleSubmit} />
    </>
  );
}
