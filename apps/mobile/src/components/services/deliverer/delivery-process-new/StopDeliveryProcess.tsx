import { useEffect } from 'react';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { deliveryProcessActions } from '../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { Loading } from '../../../layout/Loading';
import { AttachmentsStep } from './steps/AttachmentsStep';
import { DeliveryDetailStep } from './steps/DeliveryDetailStep';
import { EquipmentDepositStep } from './steps/EquipmentDepositStep';
import { EquipmentPickupStep } from './steps/EquipmentPickupStep';
import { PaymentStep } from './steps/PaymentStep';
import { SignatureStep } from './steps/SignatureStep';
import { SuccessStep } from './steps/SuccessStep';
import { StopDeliveryHeader } from './StopDeliveryHeader';

export interface StepDeliveryProps {
  stop: IStopEntity;
  tour: ITourEntity;
}

export function StopDeliveryProcess({ stop, tour }: StepDeliveryProps) {
  const dispatch = useAppDispatch();
  const deliveryProcessStop = useAppSelector((state) => state.deliveryProcess.stop);
  const deliveryProcessTour = useAppSelector((state) => state.deliveryProcess.tour);

  useEffect(() => {
    dispatch(deliveryProcessActions.setStop(stop));
    dispatch(deliveryProcessActions.setTour(tour));
  }, [stop, tour]);

  if (!Object.keys(deliveryProcessStop).length || !Object.keys(deliveryProcessTour).length) {
    return <Loading />;
  }

  return (
    <div className="mb-20  w-11/12 mx-auto">
      <StopDeliveryHeader />
      <CurrentStepRenderer />
    </div>
  );
}

export function CurrentStepRenderer() {
  const currentStep = useAppSelector((state) => state.deliveryProcess.currentStep);

  switch (currentStep) {
    case 'delivery-details':
      return <DeliveryDetailStep />;
    case 'payment':
      return <PaymentStep />;
    case 'equipment-deposit':
      return <EquipmentDepositStep />;
    case 'equipment-pickup':
      return <EquipmentPickupStep />;
    case 'attachments':
      return <AttachmentsStep />;
    case 'signature':
      return <SignatureStep />;
    case 'success':
      return <SuccessStep />;
    default:
      return <DeliveryDetailStep />;
  }
}
