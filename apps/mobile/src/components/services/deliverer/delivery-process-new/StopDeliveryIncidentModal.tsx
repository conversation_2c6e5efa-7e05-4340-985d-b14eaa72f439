import { IonButton, IonButtons, IonContent, IonHeader, IonItem, IonLabel, IonModal, IonRadio, IonRadioGroup, IonTitle, IonToolbar } from '@ionic/react';
import { useMemo } from 'react';
import { deliveryProcessActions } from '../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import InlineButtons from '../../../ui/stylized/InlineButtons';

export function StopDeliveryIncidentModal({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  const dispatch = useAppDispatch();
  const incidentTypes = useAppSelector((state) => state.incidentTypes.incidentTypes);
  const stopCompletionFormData = useAppSelector(
    (state) => state.deliveryProcess.stopCompletionFormData,
  );

  const selectedIncidentTypeId = useMemo(() => {
    return stopCompletionFormData.incidentTypeId;
  }, [stopCompletionFormData.incidentTypeId]);

  const handleSelectIncidentType = (incidentTypeId: string) => {
    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        ...stopCompletionFormData,
        incidentTypeId,
      }),
    );
  };

  const handleValidate = () => {
    if (selectedIncidentTypeId) {
      onClose();
    }
  };

  return (
    <IonModal isOpen={isOpen} onDidDismiss={onClose}>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Sélectionner un incident</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={onClose}>Fermer</IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>
      <IonContent>
        <div className="ion-padding">
          {/* Contenu centré */}
          <div className="flex flex-col min-h-96">
            {/* Options radio */}
            <div className="w-full max-w-md space-y-4 sm:space-y-6 mb-8 sm:mb-12">
              <IonRadioGroup
                value={selectedIncidentTypeId}
                onIonChange={(e) => handleSelectIncidentType(e.detail.value)}
              >
                {incidentTypes.map((incidentType) => (
                  <IonItem
                    key={incidentType.id}
                    className="border-none bg-transparent py-2 sm:py-3"
                    lines="none"
                  >
                    <IonRadio slot="start" value={incidentType.id} className="mr-3 sm:mr-4" />
                    <IonLabel>
                      <h3 className="text-base sm:text-lg md:text-xl font-medium text-black">
                        {incidentType.name}
                      </h3>
                    </IonLabel>
                  </IonItem>
                ))}
              </IonRadioGroup>
            </div>

            {/* Boutons d'action */}
            <div className="w-full max-w-sm space-y-3 sm:space-y-4">
              <InlineButtons
                buttons={[
                  {
                    label: 'ANNULER',
                    onClick: onClose,
                    classNames: {
                      button:
                        'primary-button-outline w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                    },
                  },
                  {
                    label: 'VALIDER',
                    onClick: handleValidate,
                    disabled: !selectedIncidentTypeId,
                    classNames: {
                      button:
                        'primary-button w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                    },
                  },
                ]}
              />
            </div>
          </div>
        </div>
      </IonContent>
    </IonModal>
  );
}
