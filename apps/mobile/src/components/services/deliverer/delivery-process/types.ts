import {
  IEquipmentCountDto,
  ILogisticsEquipmentDetailDto,
} from '../../../../interfaces/dto/stop-delivery.dto';
import { ILogisticsEquipmentTypeEntity } from '../../../../interfaces/entity/i-logistics-equipment-type-entity';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { ReturnedEquipmentData } from './steps/StepEquipmentPickup';
import { IncidentData } from './steps/StepIncident';
import { PaymentData } from './steps/StepPayment';
import { SignatureData } from './steps/StepSignature';

export interface DeliveryFormData {
  stop: IStopEntity;
  paymentData?: PaymentData;
  equipmentDepositData?: UnloadedEquipmentData;
  equipmentPickupData?: ReturnedEquipmentData;
  documentationData?: {
    photos: string[];
    comment: string;
  };
  signatureData?: SignatureData;
  incidentData?: IncidentData;
  gpsData?: {
    latitude: number;
    longitude: number;
    timestamp: number;
    accuracy?: number;
  };
}

export interface UnloadedEquipmentData {
  unloadedEquipment: IEquipmentCountDto;
  unloadedEquipmentDetails: ILogisticsEquipmentDetailDto[];
}

export interface ReloadedEquipmentData {
  reloadedEquipment: IEquipmentCountDto;
  reloadedEquipmentDetails: ILogisticsEquipmentDetailDto[];
}

export interface PaymentData {
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  isPaid: boolean;
  hasPaymentData: boolean;
}

export interface ReturnedEquipmentData {
  returnedEquipment: IEquipmentCountDto;
  returnedEquipmentDetails: ILogisticsEquipmentDetailDto[];
}

export interface EquipmentQuantityData {
  equipmentType: ILogisticsEquipmentTypeEntity;
  defaultQuantity: number;
  currentQuantity: number;
}
