import { IonText } from '@ionic/react';
import { IStopEntity } from '../../../../interfaces/entity/i-stop-entity';
import { formatAddress } from '../../utils';

interface ClientInfoProps {
  stop: IStopEntity;
  showDeliveryTime?: boolean;
  variant?: 'large' | 'medium' | 'small';
  className?: string;
}

const ClientInfo: React.FC<ClientInfoProps> = ({
  stop,
  showDeliveryTime = false,
  variant = 'medium',
  className = '',
}) => {
  const { originalClientInfo, deliveryTimeWindow } = stop;

  const getTextSizes = () => {
    switch (variant) {
      case 'large':
        return {
          name: 'text-2xl sm:text-3xl md:text-4xl font-bold primary-color',
          address: 'text-neutral-500 text-sm sm:text-base',
          time: 'text-xs sm:text-sm md:text-base text-gray-500',
        };
      case 'small':
        return {
          name: 'text-base sm:text-lg font-bold text-gray-800',
          address: 'text-xs sm:text-sm text-gray-500',
          time: 'text-xs sm:text-sm text-gray-500',
        };
      case 'medium':
      default:
        return {
          name: 'text-lg sm:text-xl md:text-2xl font-bold',
          address: 'text-sm sm:text-base text-gray-600',
          time: 'text-xs sm:text-sm text-gray-500',
        };
    }
  };

  const textSizes = getTextSizes();

  return (
    <div className={`flex flex-col gap-4 mb-6 ${className}`}>
      <div className={showDeliveryTime ? 'flex justify-between items-start' : ''}>
        <div className="flex-1">
          <IonText className={textSizes.name}>{originalClientInfo?.name ?? 'Client'}</IonText>
          <div className="mt-1">
            <IonText className={textSizes.address}>
              {formatAddress(originalClientInfo?.address || {})}
            </IonText>
          </div>
        </div>

        {showDeliveryTime && (
          <div className="text-right">
            <IonText className={textSizes.time}>{deliveryTimeWindow || '--:--'}</IonText>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientInfo;
