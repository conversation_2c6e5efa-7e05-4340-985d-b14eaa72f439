import { IonItem, IonLabel, IonRadio, IonRadioGroup, IonText, useIonToast } from '@ionic/react';
import { useState } from 'react';
import { DeliveryCompletionType } from '../../../../../interfaces/dto/stop-delivery.dto';
import InlineButtons from '../../../../ui/stylized/InlineButtons';

export interface StepIncidentProps {
  onNext: (incidentData: IncidentData) => void;
  onCancel: () => void;
  onContinueProcess?: (incidentData: IncidentData) => void;
}

export interface IncidentData {
  hasIncident: boolean;
  incidentTypeId?: string;
  deliveryCompletionType?: DeliveryCompletionType;
  comments?: string;
}

// Étapes du processus d'incident
enum IncidentStep {
  SELECT_CAUSE = 'SELECT_CAUSE',
  CONFIRM_CANCELLATION = 'CONFIRM_CANCELLATION',
  CANCELLED = 'CANCELLED',
}

export default function StepIncident({ onNext, onCancel, onContinueProcess }: StepIncidentProps) {
  const [presentToast] = useIonToast();
  const [currentStep, setCurrentStep] = useState<IncidentStep>(IncidentStep.SELECT_CAUSE);
  const [selectedCause, setSelectedCause] = useState<DeliveryCompletionType | null>(null);

  const getCauseLabel = (cause: DeliveryCompletionType): string => {
    switch (cause) {
      case DeliveryCompletionType.PARTIAL:
        return 'Livraison partielle';
      case DeliveryCompletionType.NONE:
        return 'Livraison impossible';
      case DeliveryCompletionType.FULL:
        return 'Refus client';
      default:
        return '';
    }
  };

  const handleSelectCauseNext = () => {
    if (!selectedCause) {
      presentToast({
        message: "Veuillez sélectionner une cause d'incident.",
        duration: 3000,
        color: 'warning',
      });

      return;
    }
    setCurrentStep(IncidentStep.CONFIRM_CANCELLATION);
  };

  const handleConfirmYes = () => {
    // Directement finaliser l'incident après confirmation
    handleIncidentComplete();
  };

  const handleConfirmNo = () => {
    setCurrentStep(IncidentStep.SELECT_CAUSE);
  };

  const handleIncidentComplete = () => {
    if (!selectedCause) {
      return; // Sécurité
    }

    let comments = '';

    switch (selectedCause) {
      case DeliveryCompletionType.PARTIAL:
        comments = 'Livraison partielle due à un incident';
        break;
      case DeliveryCompletionType.NONE:
        comments = 'Livraison impossible due à un incident';
        break;
      case DeliveryCompletionType.FULL:
        comments = 'Livraison refusée par le client';
        break;
    }

    const incidentData: IncidentData = {
      hasIncident: true,
      deliveryCompletionType: selectedCause,
      comments,
    };

    // Pour la livraison partielle, continuer le processus si la fonction est fournie
    if (selectedCause === DeliveryCompletionType.PARTIAL && onContinueProcess) {
      onContinueProcess(incidentData);
    } else {
      // Pour les autres cas, terminer le processus
      onNext(incidentData);
    }
  };

  // Étape 1 : Sélection de la cause
  if (currentStep === IncidentStep.SELECT_CAUSE) {
    return (
      <div className="ion-padding">
        {/* Contenu centré */}
        <div className="flex flex-col  min-h-96">
          {/* Options radio */}
          <div className="w-full max-w-md space-y-4 sm:space-y-6 mb-8 sm:mb-12">
            <IonRadioGroup
              value={selectedCause}
              onIonChange={(e) => setSelectedCause(e.detail.value)}
            >
              {Object.values(DeliveryCompletionType).map((cause) => (
                <IonItem
                  key={cause}
                  className="border-none bg-transparent py-2 sm:py-3"
                  lines="none"
                >
                  <IonRadio slot="start" value={cause} className="mr-3 sm:mr-4" />
                  <IonLabel>
                    <h3 className="text-base sm:text-lg md:text-xl font-medium text-black">
                      {getCauseLabel(cause)}
                    </h3>
                  </IonLabel>
                </IonItem>
              ))}
            </IonRadioGroup>
          </div>

          {/* Boutons d'action */}
          <div className="w-full max-w-sm space-y-3 sm:space-y-4">
            <InlineButtons
              buttons={[
                {
                  label: 'ANNULER',
                  onClick: onCancel,
                  classNames: {
                    button:
                      'primary-button-outline w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                  },
                },
                {
                  label: 'SUIVANT',
                  onClick: handleSelectCauseNext,
                  classNames: {
                    button:
                      'primary-button w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                  },
                },
              ]}
            />
          </div>
        </div>
      </div>
    );
  }

  // Étape 2 : Confirmation d'annulation
  if (currentStep === IncidentStep.CONFIRM_CANCELLATION) {
    const getConfirmationText = () => {
      switch (selectedCause) {
        case DeliveryCompletionType.PARTIAL:
          return 'Confirmer la livraison partielle ?';
        case DeliveryCompletionType.NONE:
          return "Confirmer l'annulation de livraison ?";
        case DeliveryCompletionType.FULL:
          return 'Confirmer le refus client ?';
        default:
          return "Confirmez-vous l'incident ?";
      }
    };

    const getConfirmButtonText = () => {
      return selectedCause === DeliveryCompletionType.PARTIAL ? 'CONTINUER' : 'OUI';
    };

    return (
      <div className="ion-padding">
        {/* Contenu centré */}
        <div className="flex flex-col items-center justify-center min-h-96">
          {/* Question de confirmation */}
          <div className="mb-12 sm:mb-16 text-center">
            <IonText className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-black">
              {getConfirmationText()}
            </IonText>
          </div>

          {/* Boutons de confirmation avec InlineButtons */}
          <div className="w-full max-w-sm">
            <InlineButtons
              buttons={[
                {
                  label: 'NON',
                  onClick: handleConfirmNo,
                  classNames: {
                    button:
                      'primary-button-outline w-full px-8 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                  },
                },
                {
                  label: getConfirmButtonText(),
                  onClick: handleConfirmYes,
                  classNames: {
                    button:
                      'primary-button w-full px-8 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                  },
                },
              ]}
            />
          </div>
        </div>
      </div>
    );
  }

  // Étape 3 : Livraison annulée - Géré par la redirection après onNext
  return null;
}
