import { useIonToast } from '@ionic/react';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import {
  IEquipmentCountDto,
  ILogisticsEquipmentDetailDto,
} from '../../../../../interfaces/dto/stop-delivery.dto';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import { LogisticsEquipmentKind } from '../../../../../interfaces/enum/logistics-equipment-kind.enum';
import { EquipmentValidationRecord } from '../../EquipmentValidator';
import { EquipmentValidationSection } from '../../loading-control/EquipmentValidationSection';

export interface ReturnedEquipmentData {
  returnedEquipment: IEquipmentCountDto;
  returnedEquipmentDetails: ILogisticsEquipmentDetailDto[];
}

interface StepEquipmentPickupProps {
  stop: IStopEntity;
  onNext: (returnedEquipmentData: ReturnedEquipmentData) => void;
  onPrevious?: () => void;
  initialEquipmentData?: ReturnedEquipmentData;
}

export interface StepEquipmentPickupRef {
  submit: () => void;
  isValid: () => boolean;
}

const StepEquipmentPickup = forwardRef<StepEquipmentPickupRef, StepEquipmentPickupProps>(
  ({ stop, onNext, initialEquipmentData }, ref) => {
    const [isFormValid, setIsFormValid] = useState(false);
    const [presentToast] = useIonToast();
    const [equipmentItems, setEquipmentItems] = useState<EquipmentValidationRecord>({
      [LogisticsEquipmentKind.PALLET]: {
        initialQuantity: 0,
        adjustedQuantity: 0,
        isValidated: false,
      },
      [LogisticsEquipmentKind.ROLL]: {
        initialQuantity: 0,
        adjustedQuantity: 0,
        isValidated: false,
      },
      [LogisticsEquipmentKind.PACKAGE]: {
        initialQuantity: 0,
        adjustedQuantity: 0,
        isValidated: false,
      },
    });

    // Convertit les données initiales en EquipmentValidationRecord
    const convertInitialDataToEquipmentItems = (
      initialData: ReturnedEquipmentData,
    ): EquipmentValidationRecord => {
      const { returnedEquipment } = initialData;

      return {
        [LogisticsEquipmentKind.PALLET]: {
          initialQuantity: returnedEquipment.palletCount || 0,
          adjustedQuantity: returnedEquipment.palletCount || 0,
          isValidated: (returnedEquipment.palletCount || 0) > 0,
        },
        [LogisticsEquipmentKind.ROLL]: {
          initialQuantity: returnedEquipment.rollCount || 0,
          adjustedQuantity: returnedEquipment.rollCount || 0,
          isValidated: (returnedEquipment.rollCount || 0) > 0,
        },
        [LogisticsEquipmentKind.PACKAGE]: {
          initialQuantity: returnedEquipment.packageCount || 0,
          adjustedQuantity: returnedEquipment.packageCount || 0,
          isValidated: (returnedEquipment.packageCount || 0) > 0,
        },
      };
    };

    // Initialiser les équipements au montage du composant
    useEffect(() => {
      if (initialEquipmentData) {
        const record = convertInitialDataToEquipmentItems(initialEquipmentData);
        setEquipmentItems(record);
      } else {
        // Créer un record vide pour les types standard
        const record: EquipmentValidationRecord = {
          [LogisticsEquipmentKind.PALLET]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
          [LogisticsEquipmentKind.ROLL]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
          [LogisticsEquipmentKind.PACKAGE]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
        };
        setEquipmentItems(record);
      }
    }, [initialEquipmentData]);

    const handleValidationChange = (isValid: boolean) => {
      setIsFormValid(isValid);
    };

    const handleEquipmentChange = (record: EquipmentValidationRecord) => {
      setEquipmentItems(record);
    };

    const handleToggleAllEquipments = (isChecked: boolean) => {
      const updatedRecord: EquipmentValidationRecord = Object.fromEntries(
        Object.entries(equipmentItems).map(([kind, data]) => [
          kind,
          { ...data, isValidated: isChecked && data.adjustedQuantity >= 0 },
        ]),
      ) as EquipmentValidationRecord;

      setEquipmentItems(updatedRecord);
      setIsFormValid(
        Object.values(updatedRecord).some((item) => item.isValidated && item.adjustedQuantity >= 0),
      );
    };

    const handleNext = () => {
      if (!isFormValid) {
        presentToast({
          message: 'Veuillez valider au moins un équipement avec une quantité.',
          duration: 3000,
          color: 'warning',
        });

        return;
      }

      try {
        // Convertir le Record en ReturnedEquipmentData
        const returnedEquipment: IEquipmentCountDto = {};
        const returnedEquipmentDetails: ILogisticsEquipmentDetailDto[] = [];

        Object.entries(equipmentItems).forEach(([kind, data]) => {
          if (data.isValidated && data.adjustedQuantity >= 0) {
            // Ajouter aux totaux par type
            switch (kind as LogisticsEquipmentKind) {
              case LogisticsEquipmentKind.PALLET:
                returnedEquipment.palletCount =
                  (returnedEquipment.palletCount || 0) + data.adjustedQuantity;
                break;
              case LogisticsEquipmentKind.ROLL:
                returnedEquipment.rollCount =
                  (returnedEquipment.rollCount || 0) + data.adjustedQuantity;
                break;
              case LogisticsEquipmentKind.PACKAGE:
                returnedEquipment.packageCount =
                  (returnedEquipment.packageCount || 0) + data.adjustedQuantity;
                break;
            }
          }
        });

        const returnedEquipmentData: ReturnedEquipmentData = {
          returnedEquipment,
          returnedEquipmentDetails, // Vide pour l'instant
        };

        onNext(returnedEquipmentData);
      } catch (error) {
        console.error('Erreur lors de la conversion des données:', error);
        presentToast({
          message: 'Erreur lors du traitement des données.',
          duration: 3000,
          color: 'danger',
        });
      }
    };

    const isCached =
      initialEquipmentData?.returnedEquipment &&
      Object.keys(initialEquipmentData?.returnedEquipment).length > 0;

    useImperativeHandle(ref, () => ({
      submit: handleNext,
      isValid: () => isCached || isFormValid,
    }));

    return (
      <div className="pt-6 pb-6">
        <EquipmentValidationSection
          stop={stop}
          equipmentItems={equipmentItems}
          onEquipmentChange={handleEquipmentChange}
          onValidationChange={handleValidationChange}
          onToggleAllEquipments={handleToggleAllEquipments}
        />
      </div>
    );
  },
);

export default StepEquipmentPickup;
