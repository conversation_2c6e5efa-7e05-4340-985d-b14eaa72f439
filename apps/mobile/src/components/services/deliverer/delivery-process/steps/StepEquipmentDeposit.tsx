import { useIonToast } from '@ionic/react';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import {
  IEquipmentCountDto,
  ILogisticsEquipmentDetailDto,
} from '../../../../../interfaces/dto/stop-delivery.dto';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import { LogisticsEquipmentKind } from '../../../../../interfaces/enum/logistics-equipment-kind.enum';
import { EquipmentValidationRecord } from '../../EquipmentValidator';
import { EquipmentValidationSection } from '../../loading-control/EquipmentValidationSection';

export interface UnloadedEquipmentData {
  unloadedEquipment: IEquipmentCountDto;
  unloadedEquipmentDetails: ILogisticsEquipmentDetailDto[];
}

interface StepEquipmentDepositProps {
  stop: IStopEntity;
  onNext: (unloadedEquipmentData: UnloadedEquipmentData) => void;
  onPrevious?: () => void;
  initialEquipmentData?: UnloadedEquipmentData;
}

export interface StepEquipmentDepositRef {
  submit: () => void;
  isValid: () => boolean;
}

const StepEquipmentDeposit = forwardRef<StepEquipmentDepositRef, StepEquipmentDepositProps>(
  ({ stop, onNext, initialEquipmentData }, ref) => {
    const [isFormValid, setIsFormValid] = useState(false);
    const [presentToast] = useIonToast();
    const [equipmentItems, setEquipmentItems] = useState<EquipmentValidationRecord>({
      [LogisticsEquipmentKind.PALLET]: {
        initialQuantity: 0,
        adjustedQuantity: 0,
        isValidated: false,
      },
      [LogisticsEquipmentKind.ROLL]: {
        initialQuantity: 0,
        adjustedQuantity: 0,
        isValidated: false,
      },
      [LogisticsEquipmentKind.PACKAGE]: {
        initialQuantity: 0,
        adjustedQuantity: 0,
        isValidated: false,
      },
    });

    // Convertit les données initiales en EquipmentValidationRecord
    const convertInitialDataToEquipmentItems = (
      initialData: UnloadedEquipmentData,
    ): EquipmentValidationRecord => {
      const { unloadedEquipment } = initialData;

      return {
        [LogisticsEquipmentKind.PALLET]: {
          initialQuantity: unloadedEquipment?.palletCount || 0,
          adjustedQuantity: unloadedEquipment?.palletCount || 0,
          isValidated: (unloadedEquipment?.palletCount || 0) > 0,
        },
        [LogisticsEquipmentKind.ROLL]: {
          initialQuantity: unloadedEquipment?.rollCount || 0,
          adjustedQuantity: unloadedEquipment?.rollCount || 0,
          isValidated: (unloadedEquipment?.rollCount || 0) > 0,
        },
        [LogisticsEquipmentKind.PACKAGE]: {
          initialQuantity: unloadedEquipment?.packageCount || 0,
          adjustedQuantity: unloadedEquipment?.packageCount || 0,
          isValidated: (unloadedEquipment?.packageCount || 0) > 0,
        },
      };
    };

    // Initialiser les équipements au montage du composant
    useEffect(() => {
      if (initialEquipmentData) {
        const record = convertInitialDataToEquipmentItems(initialEquipmentData);
        setEquipmentItems(record);
      } else {
        // Créer un record vide pour les types standard
        const record: EquipmentValidationRecord = {
          [LogisticsEquipmentKind.PALLET]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
          [LogisticsEquipmentKind.ROLL]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
          [LogisticsEquipmentKind.PACKAGE]: {
            initialQuantity: 0,
            adjustedQuantity: 0,
            isValidated: false,
          },
        };
        setEquipmentItems(record);
      }
    }, [initialEquipmentData]);

    const handleValidationChange = (isValid: boolean) => {
      setIsFormValid(isValid);
    };

    const handleEquipmentChange = (record: EquipmentValidationRecord) => {
      setEquipmentItems(record);
    };

    const handleToggleAllEquipments = (isChecked: boolean) => {
      const updatedRecord: EquipmentValidationRecord = Object.fromEntries(
        Object.entries(equipmentItems).map(([kind, data]) => [
          kind,
          { ...data, isValidated: isChecked && data.adjustedQuantity >= 0 },
        ]),
      ) as EquipmentValidationRecord;

      setEquipmentItems(updatedRecord);
      setIsFormValid(
        Object.values(updatedRecord).some((item) => item.isValidated && item.adjustedQuantity >= 0),
      );
    };

    const handleNext = () => {
      if (!isFormValid) {
        presentToast({
          message: 'Veuillez valider au moins un équipement avec une quantité.',
          duration: 3000,
          color: 'warning',
        });

        return;
      }

      try {
        // Convertir le Record en UnloadedEquipmentData
        const unloadedEquipment: IEquipmentCountDto = {};
        const unloadedEquipmentDetails: ILogisticsEquipmentDetailDto[] = [];

        Object.entries(equipmentItems).forEach(([kind, data]) => {
          if (data.isValidated && data.adjustedQuantity >= 0) {
            // Ajouter aux totaux par type
            switch (kind as LogisticsEquipmentKind) {
              case LogisticsEquipmentKind.PALLET:
                unloadedEquipment.palletCount =
                  (unloadedEquipment?.palletCount || 0) + data.adjustedQuantity;
                break;
              case LogisticsEquipmentKind.ROLL:
                unloadedEquipment.rollCount =
                  (unloadedEquipment?.rollCount || 0) + data.adjustedQuantity;
                break;
              case LogisticsEquipmentKind.PACKAGE:
                unloadedEquipment.packageCount =
                  (unloadedEquipment?.packageCount || 0) + data.adjustedQuantity;
                break;
            }
          }
        });

        const unloadedEquipmentData: UnloadedEquipmentData = {
          unloadedEquipment,
          unloadedEquipmentDetails, // Vide pour l'instant
        };

        onNext(unloadedEquipmentData);
      } catch (error) {
        console.error('Erreur lors de la conversion des données:', error);
        presentToast({
          message: 'Erreur lors du traitement des données.',
          duration: 3000,
          color: 'danger',
        });
      }
    };
    const isCached =
      initialEquipmentData?.unloadedEquipment &&
      Object.keys(initialEquipmentData?.unloadedEquipment).length > 0;

    useImperativeHandle(ref, () => ({
      submit: handleNext,
      isValid: () => isCached || isFormValid,
    }));

    return (
      <div className="pt-6 pb-6">
        <EquipmentValidationSection
          stop={stop}
          equipmentItems={equipmentItems}
          onEquipmentChange={handleEquipmentChange}
          onValidationChange={handleValidationChange}
          onToggleAllEquipments={handleToggleAllEquipments}
        />
      </div>
    );
  },
);

export default StepEquipmentDeposit;
