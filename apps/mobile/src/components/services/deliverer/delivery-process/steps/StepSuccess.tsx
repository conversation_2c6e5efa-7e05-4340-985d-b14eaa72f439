import { IonContent, IonIcon, IonPage, IonSpinner, IonText, useIonToast } from '@ionic/react';
import { checkmarkCircle, warning } from 'ionicons/icons';
import { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { PaymentData } from '..';
import { PATHS } from '../../../../../config/routes';
import { ICompleteStopDeliveryDto } from '../../../../../interfaces/dto/stop-delivery.dto';
import { IStopEntity } from '../../../../../interfaces/entity/i-stop-entity';
import { addEventToQueue } from '../../../../../stores/eventQueueSlice';
import { formatDate } from '../../../../../utils/dateUtils';
import { useAppDispatch } from '../../../../../utils/redux';
import InlineButtons from '../../../../ui/stylized/InlineButtons';
import { UnloadedEquipmentData } from './StepEquipmentDeposit';
import { ReturnedEquipmentData } from './StepEquipmentPickup';
import { IncidentData } from './StepIncident';
import { SignatureData } from './StepSignature';

interface DeliveryFormData {
  stop: IStopEntity;
  paymentData?: PaymentData;
  equipmentDepositData?: UnloadedEquipmentData;
  equipmentPickupData?: ReturnedEquipmentData;
  documentationData?: {
    photos: string[];
    comment: string;
  };
  signatureData?: SignatureData;
  incidentData?: IncidentData;
}

interface StepSuccessProps {
  deliveryId?: string;
  formData?: DeliveryFormData;
  onViewDeliveryNote?: () => void;
  onFinish?: () => void;
}

const StepSuccess: React.FC<StepSuccessProps> = ({
  deliveryId,
  formData,
  onViewDeliveryNote,
  onFinish,
}) => {
  const history = useHistory();
  const dispatch = useAppDispatch();
  const [presentToast] = useIonToast();

  // États de soumission
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  // Protection contre les soumissions multiples
  const hasSubmittedRef = useRef(false);

  // Déclencher la soumission automatiquement si formData est fourni (une seule fois)
  useEffect(() => {
    if (formData && !submitted && !submitting && !hasSubmittedRef.current) {
      handleSubmitDelivery();
    }
  }, [formData]);

  // Consolider toutes les données en format backend DTO
  const prepareCompletionData = (): Partial<ICompleteStopDeliveryDto> => {
    if (!formData) {
      return {};
    }

    const completionData: Partial<ICompleteStopDeliveryDto> = {};

    if (formData.signatureData?.signatureFile?.base64) {
      completionData.signatureFile = formData.signatureData.signatureFile;
    }

    if (formData.documentationData?.photos && formData.documentationData.photos.length > 0) {
      completionData.photoFiles = formData.documentationData.photos.map((photo, index) => {
        const cleanBase64 = photo.replace(/^data:image\/[a-z]+;base64,/, '');

        return {
          base64: cleanBase64,
          filename: `proof-photo-${index}.jpg`,
          mimeType: 'image/jpeg',
        };
      });
    }

    // 3. Données d'incident
    if (formData.incidentData?.hasIncident) {
      completionData.incidentTypeId = formData.incidentData.incidentTypeId;
      completionData.deliveryCompletionType = formData.incidentData.deliveryCompletionType;

      if (formData.incidentData.comments?.trim()) {
        completionData.comments = formData.incidentData.comments.trim();
      }
    }

    // 4. Commentaires de documentation (si pas d'incident)
    if (!formData.incidentData?.hasIncident && formData.documentationData?.comment?.trim()) {
      completionData.comments = formData.documentationData.comment.trim();
    }

    // 5. Équipements déchargés (unloaded)
    if (formData.equipmentDepositData) {
      const hasUnloadedEquipment =
        Object.keys(formData.equipmentDepositData.unloadedEquipment).length > 0;

      if (hasUnloadedEquipment) {
        completionData.unloadedEquipment = formData.equipmentDepositData.unloadedEquipment;
        completionData.unloadedEquipmentDetails =
          formData.equipmentDepositData.unloadedEquipmentDetails;
      }
    }

    // 6. Équipements récupérés (returned)
    if (formData.equipmentPickupData) {
      const hasReturnedEquipment =
        Object.keys(formData.equipmentPickupData.returnedEquipment).length > 0;

      if (hasReturnedEquipment) {
        completionData.returnedEquipment = formData.equipmentPickupData.returnedEquipment;
        completionData.returnedEquipmentDetails =
          formData.equipmentPickupData.returnedEquipmentDetails;
      }
    }

    // 7. Lieu sûr (détection automatique basée sur code client)
    const clientCode = formData.stop.originalClientInfo?.code || '';

    if (clientCode.includes('SECURE')) {
      //TODO: clientCode.includes('SECURE') ne peut jamais retourner true, aucun client code ne contient SECURE
      completionData.isSecureLocation = true;
    }

    // 8. Données de localisation GPS
    if (formData.gpsData) {
      completionData.latitude = formData.gpsData.latitude;
      completionData.longitude = formData.gpsData.longitude;
      completionData.precision = formData.gpsData.accuracy;
    }

    return completionData;
  };

  const validateCompletionData = (
    completionData: Partial<ICompleteStopDeliveryDto>,
  ): string | null => {
    const hasPhotos = completionData.photoFiles && completionData.photoFiles.length > 0;

    // Pour lieu sûr, photo obligatoire
    if (completionData.isSecureLocation && !hasPhotos) {
      return 'Une photo est obligatoire pour les livraisons en lieu sûr';
    }

    // Au moins signature OU photo requise
    if (!completionData.signatureFile && !hasPhotos) {
      return 'Au moins une preuve (signature ou photo) est requise';
    }

    // Si incident signalé, type d'incident obligatoire
    if (formData?.incidentData?.hasIncident && !completionData.incidentTypeId) {
      return "Veuillez sélectionner un type d'incident";
    }

    // Si incident signalé, type de livraison obligatoire
    if (completionData.incidentTypeId && !completionData.deliveryCompletionType) {
      return "Le type de livraison doit être spécifié lors d'un incident";
    }

    return null;
  };

  // Soumission finale au backend
  const handleSubmitDelivery = async () => {
    // Protection supplémentaire contre les appels multiples
    if (submitted || submitting || hasSubmittedRef.current) {
      console.log('Soumission déjà en cours ou terminée, ignorée');

      return;
    }

    if (!formData?.stop) {
      setSubmissionError('Données de livraison manquantes');

      return;
    }

    // Marquer comme en cours pour éviter les appels multiples
    hasSubmittedRef.current = true;
    console.log('Début de soumission de livraison pour stop:', formData.stop.id);
    setSubmitting(true);
    setSubmissionError(null);

    try {
      // Préparer les données de complétion
      const completionData = prepareCompletionData();

      // Valider les données
      const validationError = validateCompletionData(completionData);

      if (validationError) {
        throw new Error(validationError);
      }

      // Ajouter à la file d'attente offline
      await dispatch(
        addEventToQueue({
          eventType: 'stop-completion',
          payload: {
            stopId: formData.stop.id,
            ...completionData,
          },
        }),
      );

      setSubmitted(true);
      console.log('Livraison soumise avec succès pour stop:', formData.stop.id);

      presentToast({
        message: 'Livraison enregistrée avec succès',
        duration: 3000,
        color: 'success',
      });
    } catch (error) {
      console.error('Erreur lors de la soumission de la livraison:', error);
      const errorMessage =
        error instanceof Error ? error.message : "Erreur lors de l'enregistrement";
      setSubmissionError(errorMessage);

      // Reset la protection en cas d'erreur pour permettre un retry
      hasSubmittedRef.current = false;

      presentToast({
        message: errorMessage,
        duration: 4000,
        color: 'danger',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const _handleViewDeliveryNote = () => {
    if (onViewDeliveryNote) {
      onViewDeliveryNote();
    } else {
      // TODO: Ouvrir/télécharger le bon de livraison
      console.log('Voir bon de livraison pour:', deliveryId || formData?.stop.id);
    }
  };

  const handleFinish = () => {
    if (onFinish) {
      onFinish();
    } else {
      // Retourner au détail de la tournée
      const tourId = formData?.stop.tourId;

      if (tourId) {
        history.replace(PATHS.TOUR_DETAIL.replace(':id', tourId));
      } else {
        history.replace('/admin/tours');
      }
    }
  };

  // Affichage pendant la soumission
  if (submitting) {
    return (
      <IonPage className="pt-10 sm:pt-12 md:pt-14 lg:pt-16">
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center min-h-96">
            <IonSpinner
              name="crescent"
              className="w-12 sm:w-14 md:w-16 h-12 sm:h-14 md:h-16 mb-4 sm:mb-6"
              color="primary"
            />
            <IonText className="text-lg sm:text-xl md:text-2xl font-medium text-center mb-3 sm:mb-4">
              Enregistrement de la livraison...
            </IonText>
            <IonText color="medium" className="text-center text-sm sm:text-base">
              Préparation des données pour synchronisation
            </IonText>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  // Affichage en cas d'erreur
  if (submissionError) {
    return (
      <IonPage className="pt-10 sm:pt-12 md:pt-14 lg:pt-16">
        <IonContent className="ion-padding">
          <div className="text-right mb-6 sm:mb-8">
            <IonText color="medium" className="text-xs sm:text-sm md:text-base">
              Le {formatDate()}
            </IonText>
          </div>

          <div className="flex flex-col items-center justify-center min-h-96">
            {/* Icône d'erreur */}
            <div className="mb-6 sm:mb-8">
              <div className="w-16 sm:w-18 md:w-20 h-16 sm:h-18 md:h-20 bg-red-500 rounded-full flex items-center justify-center">
                <IonIcon icon={warning} className="text-white text-2xl sm:text-3xl md:text-4xl" />
              </div>
            </div>

            {/* Message d'erreur */}
            <div className="mb-6 sm:mb-8 text-center">
              <IonText className="text-xl sm:text-2xl md:text-3xl font-bold text-red-600 mb-3 sm:mb-4 block">
                ERREUR D'ENREGISTREMENT
              </IonText>
              <IonText className="text-base sm:text-lg md:text-xl text-gray-600">
                {submissionError}
              </IonText>
            </div>

            {/* Boutons d'action */}
            <div className="w-full max-w-sm space-y-3 sm:space-y-4">
              <InlineButtons
                buttons={[
                  {
                    label: 'RÉESSAYER',
                    onClick: handleSubmitDelivery,
                    classNames: {
                      button:
                        'primary-button w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                    },
                  },
                  {
                    label: 'ANNULER',
                    onClick: handleFinish,
                    classNames: {
                      button:
                        'secondary-button w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                    },
                  },
                ]}
              />
            </div>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  // Affichage de succès (après soumission réussie)
  return (
    <IonPage className="pt-10 sm:pt-12 md:pt-14 lg:pt-16">
      <IonContent className="ion-padding">
        {/* Date en haut */}
        <div className="text-right mb-6 sm:mb-8">
          <IonText color="medium" className="text-xs sm:text-sm md:text-base">
            Le {formatDate()}
          </IonText>
        </div>

        {/* Contenu centré */}
        <div className="flex flex-col items-center justify-center min-h-96">
          {/* Icône de succès verte */}
          <div className="mb-6 sm:mb-8">
            <div className="w-16 sm:w-18 md:w-20 h-16 sm:h-18 md:h-20 bg-green-500 rounded-full flex items-center justify-center">
              <IonIcon
                icon={checkmarkCircle}
                className="text-white text-2xl sm:text-3xl md:text-4xl"
              />
            </div>
          </div>

          {/* Message de succès */}
          <div className="mb-6 sm:mb-8 text-center">
            <IonText className="text-xl sm:text-2xl md:text-3xl font-bold text-green-600 mb-2 sm:mb-3 block">
              LIVRAISON TERMINÉE !
            </IonText>
            {formData?.stop && (
              <IonText className="text-base sm:text-lg md:text-xl text-gray-600">
                {formData.stop.originalClientInfo?.name || 'Client'}
              </IonText>
            )}
          </div>

          {/* Résumé rapide */}
          {submitted && formData && (
            <div className="mb-6 sm:mb-8 p-3 sm:p-4 bg-green-50 rounded-lg border border-green-200 w-full max-w-sm">
              <IonText className="text-xs sm:text-sm md:text-base text-green-800 text-center block">
                ✓ Livraison enregistrée avec succès
              </IonText>
            </div>
          )}

          {/* Bouton terminer */}
          <div className="w-full max-w-xs">
            <InlineButtons
              buttons={[
                {
                  label: 'TERMINER',
                  onClick: handleFinish,
                  classNames: {
                    button:
                      'primary-button w-full px-12 sm:px-16 py-3 sm:py-4 text-base sm:text-lg font-semibold',
                  },
                },
              ]}
            />
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default StepSuccess;
