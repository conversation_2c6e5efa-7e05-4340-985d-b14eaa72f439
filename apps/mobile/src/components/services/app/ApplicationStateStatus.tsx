import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonModal,
  IonProgressBar,
  IonTitle,
  IonToolbar,
} from '@ionic/react';
import { createSelector } from '@reduxjs/toolkit';
import {
  closeOutline,
  cloudDownloadOutline,
  cloudOfflineOutline,
  syncOutline,
  warningOutline,
} from 'ionicons/icons';
import { useState } from 'react';
import { RootState } from '../../../stores/store';
import { useAppSelector } from '../../../utils/redux';

type ApplicationStatusType = 'offline' | 'loading' | 'error' | 'healthy';

interface StatusConfig {
  icon: string;
  bgColor: string;
  message: string;
  shouldAnimate: boolean;
  title: string;
  description: string;
}

const statusConfigMap: Record<ApplicationStatusType, StatusConfig> = {
  offline: {
    icon: cloudOfflineOutline,
    bgColor: 'bg-red-500',
    message: 'Ho<PERSON> ligne',
    shouldAnimate: false,
    title: 'Connexion Internet indisponible',
    description:
      "Votre appareil n'est pas connecté à Internet. Certaines fonctionnalités peuvent être limitées. Les données locales restent disponibles.",
  },
  loading: {
    icon: syncOutline,
    bgColor: 'bg-blue-500',
    message: 'Synchronisation...',
    shouldAnimate: true,
    title: 'Synchronisation en cours',
    description:
      "L'application synchronise vos données avec le serveur. Veuillez patienter pendant que nous récupérons les dernières informations.",
  },
  error: {
    icon: warningOutline,
    bgColor: 'bg-orange-500',
    message: 'Erreur de sync',
    shouldAnimate: false,
    title: 'Erreur de synchronisation',
    description:
      "Une erreur s'est produite lors de la synchronisation des données. Vérifiez votre connexion Internet et réessayez.",
  },
  healthy: {
    icon: '',
    bgColor: '',
    message: '',
    shouldAnimate: false,
    title: '',
    description: '',
  },
};

function getApplicationStatus(
  hasInternet: boolean,
  isDataResolved: boolean,
  isDataLoading: boolean,
  hasError: boolean,
): ApplicationStatusType {
  if (hasInternet && isDataResolved && !isDataLoading && !hasError) {
    return 'healthy';
  }

  if (!hasInternet) {
    return 'offline';
  }

  if (isDataLoading) {
    return 'loading';
  }

  if (hasError || !isDataResolved) {
    return 'error';
  }

  return 'healthy';
}

const isLoadingSelector = createSelector(
  (state: RootState) => state.deliveryNotes.isProcessing,
  (state: RootState) => state.tour.loading,
  (isProcessing, isLoading) => isProcessing || isLoading,
);

const downloadStatsSelector = (state: RootState) => {
  const queue = state.deliveryNotes.downloadQueue;
  const total = queue.length;
  const completed = queue.filter((item) => item.status === 'completed').length;
  const failed = queue.filter((item) => item.status === 'failed' && item.retryCount >= 3).length;
  const downloading = queue.filter((item) => item.status === 'downloading').length;
  const pending = queue.filter((item) => item.status === 'pending').length;

  return {
    total,
    completed,
    failed,
    downloading,
    pending,
    hasActiveDownloads: downloading > 0 || pending > 0,
    progress: total > 0 ? completed / total : 0,
  };
};

const eventQueueStatsSelector = (state: RootState) => {
  const queue = state.eventQueue.queue;
  const total = queue.length;
  const completed = queue.filter((event) => event.completionStatus.status === 'completed').length;
  const failed = queue.filter((event) => event.completionStatus.status === 'failed').length;
  const syncing = queue.filter((event) => event.completionStatus.status === 'syncing').length;
  const pending = queue.filter((event) => event.completionStatus.status === 'pending').length;

  return {
    total,
    completed,
    failed,
    syncing,
    pending,
    hasActiveSync: syncing > 0 || pending > 0,
    progress: total > 0 ? completed / total : 0,
  };
};

export function ApplicationStateStatus() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const hasInternet = useAppSelector((state) => state.applicationState.hasInternet);
  const isDataResolved = useAppSelector((state) => state.tour.isDataResolved);
  const isDataLoading = useAppSelector(isLoadingSelector);
  const hasError = useAppSelector((state) => state.tour.error);
  const downloadStats = useAppSelector(downloadStatsSelector);
  const eventQueueStats = useAppSelector(eventQueueStatsSelector);

  const status = getApplicationStatus(hasInternet, isDataResolved, isDataLoading, !!hasError);

  // Show download progress if there are active downloads
  const showDownloadProgress = downloadStats.total > 0 && downloadStats.hasActiveDownloads;

  // Show sync progress if there are active sync operations
  const showSyncProgress = eventQueueStats.total > 0 && eventQueueStats.hasActiveSync;

  // Show any progress indicator
  const showProgress = showDownloadProgress || showSyncProgress;

  if (status === 'healthy' && !showProgress) {
    return null;
  }

  const config = statusConfigMap[status];

  const handleStatusClick = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      {/* Show status indicator */}
      {(status !== 'healthy' || showProgress) && (
        <div className="fixed bottom-18 right-0 z-50 transition-transform duration-300 hover:translate-x-0 translate-x-27">
          <div
            className={`flex items-center gap-2 ${
              showProgress && status === 'healthy' ? 'bg-blue-500' : config.bgColor
            } text-white pl-3 py-2 rounded-l-full shadow-lg pr-6 cursor-pointer`}
            onClick={handleStatusClick}
          >
            <IonIcon
              icon={
                showProgress && status === 'healthy'
                  ? showSyncProgress
                    ? syncOutline
                    : cloudDownloadOutline
                  : config.icon
              }
              className={`w-5 h-5 flex-shrink-0 ${
                config.shouldAnimate || (showProgress && status === 'healthy')
                  ? 'animate-pulse'
                  : ''
              }`}
            />
            <p className="text-sm font-medium whitespace-nowrap w-20">
              {showProgress && status === 'healthy'
                ? showSyncProgress && showDownloadProgress
                  ? 'Sync & DL...'
                  : showSyncProgress
                    ? 'Synchronisation...'
                    : 'Téléchargement...'
                : config.message}
            </p>
          </div>
        </div>
      )}

      <IonModal isOpen={isModalOpen} onDidDismiss={() => setIsModalOpen(false)}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>
              {showProgress && status === 'healthy'
                ? showSyncProgress && showDownloadProgress
                  ? 'Synchronisation & Téléchargement'
                  : showSyncProgress
                    ? 'Synchronisation des données'
                    : 'Téléchargement des documents'
                : config.title}
            </IonTitle>
            <IonButtons slot="end">
              <IonButton fill="clear" onClick={() => setIsModalOpen(false)}>
                <IonIcon icon={closeOutline} />
              </IonButton>
            </IonButtons>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center text-center space-y-4">
            {/* Show progress as main content when only downloading/syncing */}
            {showProgress && status === 'healthy' ? (
              <>
                <div className="p-4 rounded-full bg-blue-100">
                  <IonIcon
                    icon={showSyncProgress ? syncOutline : cloudDownloadOutline}
                    className="w-12 h-12 text-blue-500 animate-pulse"
                  />
                </div>
                <h2 className="text-xl font-semibold text-gray-800">
                  {showSyncProgress && showDownloadProgress
                    ? 'Synchronisation & Téléchargement'
                    : showSyncProgress
                      ? 'Synchronisation en cours'
                      : 'Téléchargement en cours'}
                </h2>
                <p className="text-gray-600 leading-relaxed max-w-sm">
                  {showSyncProgress && showDownloadProgress
                    ? 'Synchronisation des données et téléchargement des documents en cours.'
                    : showSyncProgress
                      ? 'Vos données sont en cours de synchronisation avec le serveur.'
                      : 'Les documents PDF de vos tournées sont en cours de téléchargement pour une consultation hors ligne.'}
                </p>
                <div className="w-full max-w-md space-y-4">
                  {/* Sync Progress Section */}
                  {showSyncProgress && (
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <div className="flex items-center gap-2 mb-4">
                        <IonIcon icon={syncOutline} className="w-5 h-5 text-blue-600" />
                        <h3 className="font-semibold text-gray-800">Synchronisation</h3>
                      </div>
                      <div className="mb-4">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-lg font-semibold text-gray-800">
                            {Math.round(eventQueueStats.progress * 100)}%
                          </span>
                          <span className="text-sm text-gray-600">
                            {eventQueueStats.completed} / {eventQueueStats.total} événements
                          </span>
                        </div>
                        <IonProgressBar
                          value={eventQueueStats.progress}
                          color={eventQueueStats.failed > 0 ? 'warning' : 'primary'}
                          style={{ height: '10px', borderRadius: '5px' }}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="text-left">
                          <p className="text-gray-500">En cours</p>
                          <p className="font-semibold text-gray-800">{eventQueueStats.syncing}</p>
                        </div>
                        <div className="text-left">
                          <p className="text-gray-500">En attente</p>
                          <p className="font-semibold text-gray-800">{eventQueueStats.pending}</p>
                        </div>
                        <div className="text-left">
                          <p className="text-gray-500">Complétés</p>
                          <p className="font-semibold text-green-600">
                            {eventQueueStats.completed}
                          </p>
                        </div>
                        <div className="text-left">
                          <p className="text-gray-500">Échecs</p>
                          <p className="font-semibold text-orange-600">{eventQueueStats.failed}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Download Progress Section */}
                  {showDownloadProgress && (
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <div className="flex items-center gap-2 mb-4">
                        <IonIcon icon={cloudDownloadOutline} className="w-5 h-5 text-blue-600" />
                        <h3 className="font-semibold text-gray-800">Téléchargement</h3>
                      </div>
                      <div className="mb-4">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-lg font-semibold text-gray-800">
                            {Math.round(downloadStats.progress * 100)}%
                          </span>
                          <span className="text-sm text-gray-600">
                            {downloadStats.completed} / {downloadStats.total} documents
                          </span>
                        </div>
                        <IonProgressBar
                          value={downloadStats.progress}
                          color={downloadStats.failed > 0 ? 'warning' : 'primary'}
                          style={{ height: '10px', borderRadius: '5px' }}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="text-left">
                          <p className="text-gray-500">En cours</p>
                          <p className="font-semibold text-gray-800">{downloadStats.downloading}</p>
                        </div>
                        <div className="text-left">
                          <p className="text-gray-500">En attente</p>
                          <p className="font-semibold text-gray-800">{downloadStats.pending}</p>
                        </div>
                        <div className="text-left">
                          <p className="text-gray-500">Complétés</p>
                          <p className="font-semibold text-green-600">{downloadStats.completed}</p>
                        </div>
                        <div className="text-left">
                          <p className="text-gray-500">Échecs</p>
                          <p className="font-semibold text-orange-600">{downloadStats.failed}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              /* Show regular status when not downloading or when there are other issues */
              <>
                <div
                  className={`p-4 rounded-full ${config.bgColor.replace('bg-', 'bg-opacity-20 bg-')}`}
                >
                  <IonIcon
                    icon={config.icon}
                    className={`w-12 h-12 ${config.bgColor.replace('bg-', 'text-')} ${config.shouldAnimate ? 'animate-spin' : ''}`}
                  />
                </div>
                <h2 className="text-xl font-semibold text-gray-800">{config.title}</h2>
                <p className="text-gray-600 leading-relaxed max-w-sm">{config.description}</p>

                {/* Progress Stats in Modal when there are also other issues */}
                <div className="mt-4 w-full max-w-sm space-y-3">
                  {eventQueueStats.total > 0 && (
                    <div className="p-4 bg-gray-100 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <IonIcon icon={syncOutline} className="w-5 h-5 text-gray-600" />
                        <h3 className="font-semibold text-gray-800">Synchronisation des données</h3>
                      </div>
                      <IonProgressBar
                        value={eventQueueStats.progress}
                        color={eventQueueStats.failed > 0 ? 'warning' : 'primary'}
                        style={{ height: '6px', marginBottom: '8px' }}
                      />
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>
                          • {eventQueueStats.completed} synchronisés sur {eventQueueStats.total}
                        </p>
                        {eventQueueStats.syncing > 0 && <p>• {eventQueueStats.syncing} en cours</p>}
                        {eventQueueStats.pending > 0 && (
                          <p>• {eventQueueStats.pending} en attente</p>
                        )}
                        {eventQueueStats.failed > 0 && (
                          <p className="text-orange-600">
                            • {eventQueueStats.failed} échec
                            {eventQueueStats.failed > 1 ? 's' : ''}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {downloadStats.total > 0 && (
                    <div className="p-4 bg-gray-100 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <IonIcon icon={cloudDownloadOutline} className="w-5 h-5 text-gray-600" />
                        <h3 className="font-semibold text-gray-800">
                          Téléchargement des documents
                        </h3>
                      </div>
                      <IonProgressBar
                        value={downloadStats.progress}
                        color={downloadStats.failed > 0 ? 'warning' : 'primary'}
                        style={{ height: '6px', marginBottom: '8px' }}
                      />
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>
                          • {downloadStats.completed} téléchargés sur {downloadStats.total}
                        </p>
                        {downloadStats.downloading > 0 && (
                          <p>• {downloadStats.downloading} en cours</p>
                        )}
                        {downloadStats.pending > 0 && <p>• {downloadStats.pending} en attente</p>}
                        {downloadStats.failed > 0 && (
                          <p className="text-orange-600">
                            • {downloadStats.failed} échec
                            {downloadStats.failed > 1 ? 's' : ''}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </IonContent>
      </IonModal>
    </>
  );
}
