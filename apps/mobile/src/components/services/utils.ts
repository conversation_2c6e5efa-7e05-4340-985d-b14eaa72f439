import { IAddress } from '../../interfaces/entity/i-address';
import { IStopEntity } from '../../interfaces/entity/i-stop-entity';
import { DeliveryStatus } from '../../interfaces/enum/stop-completion.enum';

export const formatAddress = (address: IAddress | string): string => {
  if (typeof address === 'string') {
    return address;
  }

  if (!address) {
    return '';
  }

  const parts = [address.line1, address.line2, address.line3, address.line4].filter(Boolean);

  return parts.join(', ');
};

/**
 * Vérifie si un stop est terminé (livraison complétée)
 */
export const isStopCompleted = (stop: IStopEntity): boolean => {
  return stop.completion?.deliveryStatus === DeliveryStatus.COMPLETED;
};
