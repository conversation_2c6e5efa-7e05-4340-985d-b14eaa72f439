import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { App } from './App';
import './global.css';
import { store } from './stores/store';

const container = document.getElementById('root');
const root = createRoot(container!);

root.render(
  (
    <React.StrictMode>
      <Provider store={store}>
        <App />
      </Provider>
    </React.StrictMode>
  ) as any,
);
