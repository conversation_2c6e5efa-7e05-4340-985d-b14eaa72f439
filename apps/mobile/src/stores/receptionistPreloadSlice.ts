import { createAsyncThunk, createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  EquipmentValidationRecord,
  getEmptyEquipmentValidationRecord,
} from '../components/services/deliverer/EquipmentValidator';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { StopDeliverService } from '../services/StopService';
import { addEventToQueue } from './eventQueueSlice';
import { RootState } from './store';

type StopId = string;

interface ReceptionistPreloadInterface {
  tours: ITourEntity[];
  searchTerm: string;
  preloadEquipmentValidationByStop: Record<StopId, EquipmentValidationRecord>;
}

const initialState: ReceptionistPreloadInterface = {
  tours: [],
  searchTerm: '',
  preloadEquipmentValidationByStop: {},
};

export const receptionistPreloadSlice = createSlice({
  name: 'receptionistPreload',
  initialState,
  reducers: {
    setTours: (state, action: PayloadAction<ITourEntity[]>) => {
      state.tours = action.payload;
      const existingStopsInPreload = Object.keys(state.preloadEquipmentValidationByStop);
      state.preloadEquipmentValidationByStop = action.payload.reduce(
        (acc, tour) => {
          tour.stops.forEach((stop) => {
            if (existingStopsInPreload.includes(stop.id)) {
              acc[stop.id] = state.preloadEquipmentValidationByStop[stop.id];
            } else {
              acc[stop.id] = getEmptyEquipmentValidationRecord();

              if (stop.completion?.preloadedEquipmentCount?.packageCount) {
                acc[stop.id].PACKAGE.adjustedQuantity =
                  stop.completion.preloadedEquipmentCount.packageCount;
              }

              if (stop.completion?.preloadedEquipmentCount?.palletCount) {
                acc[stop.id].PALLET.adjustedQuantity =
                  stop.completion.preloadedEquipmentCount.palletCount;
              }

              if (stop.completion?.preloadedEquipmentCount?.rollCount) {
                acc[stop.id].ROLL.adjustedQuantity =
                  stop.completion.preloadedEquipmentCount.rollCount;
              }
            }
          });

          return acc;
        },
        {} as Record<StopId, EquipmentValidationRecord>,
      );
    },
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
    },
    removeTour: (state, action: PayloadAction<string>) => {
      state.tours = state.tours.filter((tour) => tour.id !== action.payload);
    },
    clearSearchTerm: (state) => {
      state.searchTerm = '';
    },
    updateEquipmentQuantity: (
      state,
      action: PayloadAction<{
        stopId: StopId;
        equipmentKind: LogisticsEquipmentKind;
        quantity: number;
      }>,
    ) => {
      const { stopId, equipmentKind, quantity } = action.payload;

      if (state.preloadEquipmentValidationByStop[stopId]?.[equipmentKind]) {
        state.preloadEquipmentValidationByStop[stopId][equipmentKind] = {
          ...state.preloadEquipmentValidationByStop[stopId][equipmentKind],
          adjustedQuantity: quantity,
        };
      }
    },
    updateEquipmentValidation: (
      state,
      action: PayloadAction<{
        stopId: StopId;
        equipmentKind: LogisticsEquipmentKind;
        isValidated: boolean;
      }>,
    ) => {
      const { stopId, equipmentKind, isValidated } = action.payload;

      if (state.preloadEquipmentValidationByStop[stopId]?.[equipmentKind]) {
        state.preloadEquipmentValidationByStop[stopId][equipmentKind] = {
          ...state.preloadEquipmentValidationByStop[stopId][equipmentKind],
          isValidated,
        };
      }
    },
    updateEquipmentValidationOnStop: (
      state,
      action: PayloadAction<{ stopId: StopId; isValidated: boolean }>,
    ) => {
      const { stopId, isValidated } = action.payload;

      state.preloadEquipmentValidationByStop[stopId] = Object.values(LogisticsEquipmentKind).reduce(
        (acc, kind) => ({
          ...acc,
          [kind]: {
            ...state.preloadEquipmentValidationByStop[stopId]?.[kind],
            isValidated,
          },
        }),
        {} as EquipmentValidationRecord,
      );
    },
  },
});

// Sélecteurs
const selectReceptionistPreload = (state: RootState) => state.receptionistPreload;

// Sélecteur pour les preload stop DTOs
const selectPreloadStopEquipmentValidation = createSelector(
  [selectReceptionistPreload],
  (preload) => preload.preloadEquipmentValidationByStop,
);

// Sélecteur pour les tournées filtrées par recherche et par le fait qu'elles aient été traitées
const selectFilteredTours = createSelector([selectReceptionistPreload], (preload) => {
  let tours = preload.tours;

  // Filtrer par terme de recherche
  if (preload.searchTerm.trim()) {
    const searchLower = preload.searchTerm.toLowerCase().trim();
    tours = tours.filter((tour: ITourEntity) => {
      const tourOriginalNumber = tour.tourIdentifier.originalNumber?.toLowerCase() || '';

      return tourOriginalNumber.includes(searchLower);
    });
  }

  // Filtrer par le fait qu'elles aient été traitées, si tout les stop sont préchargés alors on ne les affiche pas
  tours = tours.filter((tour: ITourEntity) =>
    tour.stops.some((stop) => !StopDeliverService.isStopPreloaded(stop)),
  );

  return tours;
});

/** pour un stopId donné prends la liste des équipements préchargés et les envois à la queue pour etre persisté dans le backend */
export const processEquipmentToPreloadForTourId = createAsyncThunk(
  'receptionistPreload/processEquipmentToPreloadForTourId',
  async (tourId: string, { getState, dispatch }) => {
    const state = getState() as RootState;
    const tour = state.receptionistPreload.tours.find((tour) => tour.id === tourId);

    if (!tour) {
      throw new Error(`No tour found for tourId ${tourId}`);
    }

    const allStopIds = tour.stops.map((stop) => stop.id);

    for (const stopId of allStopIds) {
      const equipments = state.receptionistPreload.preloadEquipmentValidationByStop[stopId];

      dispatch(
        addEventToQueue({
          eventType: 'stop-preload',
          payload: {
            stopId,
            equipmentCount: {
              packageCount: equipments.PACKAGE.adjustedQuantity,
              palletCount: equipments.PALLET.adjustedQuantity,
              rollCount: equipments.ROLL.adjustedQuantity,
            },
          },
        }),
      );
    }
  },
);

const {
  setTours,
  setSearchTerm,
  removeTour,
  clearSearchTerm,
  updateEquipmentQuantity,
  updateEquipmentValidation,
  updateEquipmentValidationOnStop,
} = receptionistPreloadSlice.actions;

export const receptionistPreloadActions = {
  setTours,
  setSearchTerm,
  removeTour,
  clearSearchTerm,
  updateEquipmentQuantity,
  updateEquipmentValidation,
  updateEquipmentValidationOnStop,
};

export const receptionistPreloadSelectors = {
  selectPreloadStopEquipmentValidation,
  selectFilteredTours,
};

export default receptionistPreloadSlice.reducer;
