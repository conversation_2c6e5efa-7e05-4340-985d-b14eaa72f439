import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { IUserEntity } from '../interfaces/entity/i-user-entity';
import { UserRole } from '../interfaces/enum/user-role.enum';
import { keycloakService } from '../plugin/keycloak/keycloak-service';
import { userService } from '../services';
import { capacitorPreferencesStorage } from '../utils/capacitor-preferences-storage';

interface CurrentUserState {
  user: IUserEntity | null;
  activeRole: UserRole | null;
  loading: boolean;
  error: string | null;
  isInitialized: boolean;
}

const initialState: CurrentUserState = {
  user: null,
  activeRole: null,
  loading: false,
  error: null,
  isInitialized: false,
};

export const initializeCurrentUserStore = createAsyncThunk('currentUser/initialize', async () => {
  const userStringifiedFromStorage = await capacitorPreferencesStorage.getItem('currentUser');

  if (!userStringifiedFromStorage) {
    return { user: null, activeRole: null };
  }

  const user = JSON.parse(userStringifiedFromStorage);

  // Récupérer l'activeRole stocké séparément
  const activeRoleFromStorage = await capacitorPreferencesStorage.getItem('activeRole');
  const activeRole = activeRoleFromStorage ? JSON.parse(activeRoleFromStorage) : null;

  return { user: user ?? null, activeRole };
});

export const updateCurrentUser = createAsyncThunk('currentUser/update', async () => {
  const userFromApi = await userService.getCurrentUser();

  await capacitorPreferencesStorage.setItem('currentUser', JSON.stringify(userFromApi));

  return { user: userFromApi, activeRole: null }; // Reset activeRole lors du refresh user
});

export const setActiveRole = createAsyncThunk(
  'currentUser/setActiveRole',
  async (role: UserRole) => {
    // Persister l'activeRole dans le stockage local
    await capacitorPreferencesStorage.setItem('activeRole', JSON.stringify(role));

    return role;
  },
);

export const signOut = createAsyncThunk('currentUser/signOut', async () => {
  await keycloakService.logout();

  await capacitorPreferencesStorage.removeItem('currentUser');
  await capacitorPreferencesStorage.removeItem('activeRole');

  return null;
});

const currentUserSlice = createSlice({
  name: 'currentUser',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(initializeCurrentUserStore.fulfilled, (state, action) => {
      if (action.payload) {
        state.user = action.payload.user;
        state.activeRole = action.payload.activeRole;
      }
      state.isInitialized = true;
    });
    builder.addCase(initializeCurrentUserStore.rejected, (state, action) => {
      state.error = action.error.message ?? null;
    });

    builder.addCase(updateCurrentUser.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(updateCurrentUser.fulfilled, (state, action) => {
      if (action.payload) {
        state.user = action.payload.user;
        // On ne reset pas l'activeRole ici pour le conserver entre les refresh
      }
      state.loading = false;
    });
    builder.addCase(updateCurrentUser.rejected, (state, action) => {
      state.error = action.error.message ?? null;
      state.loading = false;
    });

    builder.addCase(setActiveRole.fulfilled, (state, action) => {
      state.activeRole = action.payload;
    });

    builder.addCase(signOut.fulfilled, (state) => {
      state.user = null;
      state.activeRole = null;
    });
  },
});

export const {} = currentUserSlice.actions;
export default currentUserSlice.reducer;
