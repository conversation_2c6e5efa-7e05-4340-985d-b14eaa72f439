import { createAsyncThunk, createSlice, Middleware, nanoid, PayloadAction } from '@reduxjs/toolkit';
import { ILoadStopDto, IPreloadStopDto } from '../interfaces/dto/load-stop.dto';
import { ICompleteStopDeliveryDto } from '../interfaces/dto/stop-delivery.dto';
import { stopDeliverService } from '../services/StopService';
import { indexedDBPreferencesStorage } from '../utils/indexeddb-preferences-storage';

export interface EventCompletionStatus {
  status: 'pending' | 'completed' | 'syncing';
}

export interface EventCompletionStatusFailed {
  status: 'failed';
  error: string;
}

type StopCompletionEvent = {
  eventType: 'stop-completion';
  payload: ICompleteStopDeliveryDto & { stopId: string };
};

type StopLoadEvent = {
  eventType: 'stop-load';
  payload: ILoadStopDto & { stopId: string };
};

type StopPreloadEvent = {
  eventType: 'stop-preload';
  payload: IPreloadStopDto & { stopId: string };
};

export type QueuedEventInterface = (StopCompletionEvent | StopLoadEvent | StopPreloadEvent) & {
  id: string;
  completionStatus: EventCompletionStatus | EventCompletionStatusFailed;
  createdAt: string;
};

export interface QueueEventStateInterface {
  queue: QueuedEventInterface[];
  isInitialized: boolean;
}

const initialState: QueueEventStateInterface = {
  queue: [],
  isInitialized: false,
};

const STORAGE_KEY = 'eventQueue';

export const initializeEventQueueState = createAsyncThunk('eventQueue/initialize', async () => {
  const storedQueue = await indexedDBPreferencesStorage.getItem(STORAGE_KEY);

  return storedQueue ? JSON.parse(storedQueue) : [];
});

export const processEvent = createAsyncThunk(
  'eventQueue/processEvent',
  async (event: QueuedEventInterface) => {
    console.log('🔍 [EventQueue.processEvent] Processing event:', {
      eventType: event.eventType,
      eventId: event.id,
      payload: JSON.stringify(event.payload, null, 2),
    });

    const payload = event.payload as any;

    try {
      let result;

      switch (event.eventType) {
        case 'stop-completion':
          console.log('🔍 [EventQueue.processEvent] Calling completeStopDelivery...');
          result = await stopDeliverService.completeStopDelivery(payload.stopId, payload);
          break;
        case 'stop-load':
          console.log('🔍 [EventQueue.processEvent] Calling loadStop...');
          result = await stopDeliverService.loadStop(payload.stopId, payload);
          break;
        case 'stop-preload':
          console.log('🔍 [EventQueue.processEvent] Calling preloadStop...');
          result = await stopDeliverService.preloadStop(payload.stopId, payload);
          break;
        default:
          // @ts-expect-error
          throw new Error(`Unknown event type: ${event.eventType}`);
      }

      console.log('✅ [EventQueue.processEvent] Event processed successfully:', {
        eventType: event.eventType,
        eventId: event.id,
        result: result ? 'Success' : 'No result',
      });

      return result;
    } catch (error) {
      console.error('❌ [EventQueue.processEvent] Error processing event:', {
        eventType: event.eventType,
        eventId: event.id,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  },
);

const eventQueueSlice = createSlice({
  name: 'eventQueue',
  initialState,
  reducers: {
    addEventToQueue: (
      state,
      action: PayloadAction<StopCompletionEvent | StopLoadEvent | StopPreloadEvent>,
    ) => {
      state.queue.push({
        id: nanoid(),
        ...action.payload,
        completionStatus: { status: 'pending' },
        createdAt: new Date().toISOString(),
      });
    },
    clearCompletedEvents: (state) => {
      state.queue = state.queue.filter((event) => event.completionStatus.status !== 'completed');
    },
    retryFailedEvent: (state, action: PayloadAction<string>) => {
      state.queue = state.queue.map((event) =>
        event.id === action.payload ? { ...event, completionStatus: { status: 'pending' } } : event,
      );
    },
  },
  extraReducers: (builder) => {
    builder.addCase(initializeEventQueueState.fulfilled, (state, action) => {
      state.queue = action.payload;
      state.isInitialized = true;
    });
    builder.addCase(processEvent.pending, (state, action) => {
      state.queue = state.queue.map((event) =>
        event.id === action.meta.arg.id
          ? { ...event, completionStatus: { status: 'syncing' } }
          : event,
      );
    });
    builder.addCase(processEvent.fulfilled, (state, action) => {
      state.queue = state.queue.map((event) =>
        event.id === action.meta.arg.id
          ? { ...event, completionStatus: { status: 'completed' } }
          : event,
      );
    });
    builder.addCase(processEvent.rejected, (state, action) => {
      state.queue = state.queue.map((event) =>
        event.id === action.meta.arg.id
          ? {
              ...event,
              completionStatus: {
                status: 'failed',
                error: action.error.message ?? 'Unknown error',
              },
            }
          : event,
      );
    });
  },
});

export const eventQueuePersistMiddleware: Middleware<
  unknown,
  { eventQueue: QueueEventStateInterface }
> = (store) => (next) => (action: any) => {
  const result = next(action);

  if (action.type?.startsWith('eventQueue/')) {
    const state = store.getState().eventQueue;

    if (state?.isInitialized) {
      indexedDBPreferencesStorage.setItem(STORAGE_KEY, JSON.stringify(state.queue));
    }
  }

  return result;
};

export const { addEventToQueue, clearCompletedEvents, retryFailedEvent } = eventQueueSlice.actions;
export default eventQueueSlice.reducer;
