import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ApplicationState {
  isInitialized: boolean;
  hasInternet: boolean;
  headerSize: number;
}

const initialState: ApplicationState = {
  isInitialized: false,
  hasInternet: false,
  headerSize: 0,
};

const applicationStateSlice = createSlice({
  name: 'applicationState',
  initialState,
  reducers: {
    setApplicationStoreInitialized: (state, action: PayloadAction<boolean>) => {
      state.isInitialized = action.payload;
    },
    setHasInternet: (state, action: PayloadAction<boolean>) => {
      state.hasInternet = action.payload;
    },
    setHeaderSize: (state, action: PayloadAction<number>) => {
      state.headerSize = action.payload;
    },
  },
});

export const { setApplicationStoreInitialized, setHasInternet, setHeaderSize } =
  applicationStateSlice.actions;
export default applicationStateSlice.reducer;
