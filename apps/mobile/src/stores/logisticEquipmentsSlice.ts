import { createAsyncThunk, createSelector, createSlice } from '@reduxjs/toolkit';
import { ILogisticsEquipmentTypeEntity } from '../interfaces/entity/i-logistics-equipment-type-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { IPaginationParams } from '../interfaces/pagination';
import { logisticsEquipmentTypeService } from '../services/LogisticsEquipmentTypeService';

export const logisticEquipmentTypesByKindSelector = createSelector(
  (state) => state.logisticEquipments.equipmentTypes,
  (equipmentTypes: ILogisticsEquipmentTypeEntity[]) => {
    return equipmentTypes.reduce(
      (acc, equipmentType) => {
        if (!acc[equipmentType.kind]) {
          acc[equipmentType.kind] = [];
        }
        acc[equipmentType.kind].push(equipmentType);

        return acc;
      },
      {} as Record<LogisticsEquipmentKind, ILogisticsEquipmentTypeEntity[]>,
    );
  },
);

export interface LogisticEquipmentsState {
  equipmentTypes: ILogisticsEquipmentTypeEntity[];
}

const getLogisticEquipmentTypesFromLocalStorage = () => {
  const logisticEquipmentTypes = localStorage.getItem('logisticEquipmentTypes');

  if (!logisticEquipmentTypes) {
    return [];
  }

  return JSON.parse(logisticEquipmentTypes);
};

const initialState: LogisticEquipmentsState = {
  equipmentTypes: getLogisticEquipmentTypesFromLocalStorage(),
};

export const updateLogisticEquipmentTypes = createAsyncThunk(
  'logisticEquipments/updateLogisticEquipmentTypes',
  async () => {
    const paginationParams: IPaginationParams = {
      page: 1,
      limit: 100,
    };
    const logisticEquipmentTypes = await logisticsEquipmentTypeService.findAll(paginationParams);

    const items = logisticEquipmentTypes.items;

    localStorage.setItem('logisticEquipmentTypes', JSON.stringify(items));

    return items;
  },
);

export const logisticEquipmentsSlice = createSlice({
  name: 'logisticEquipments',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(updateLogisticEquipmentTypes.fulfilled, (state, action) => {
      state.equipmentTypes = action.payload;
    });
  },
});

export const {} = logisticEquipmentsSlice.actions;

export default logisticEquipmentsSlice.reducer;
