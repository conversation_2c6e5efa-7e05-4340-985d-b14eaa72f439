import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { IIncidentTypeEntity } from '../interfaces/entity/i-incident-type-entity';
import { IPaginationParams } from '../interfaces/pagination';
import { incidentTypeService } from '../services/IncidentTypeService';

export interface IncidentTypeState {
  incidentTypes: IIncidentTypeEntity[];
}

const getIncidentTypesFromLocalStorage = () => {
  const incidentTypes = localStorage.getItem('incidentTypes');

  if (!incidentTypes) {
    return [];
  }

  return JSON.parse(incidentTypes);
};

const initialState: IncidentTypeState = {
  incidentTypes: getIncidentTypesFromLocalStorage(),
};

export const updateIncidentTypes = createAsyncThunk(
  'incidentTypes/updateIncidentTypes',
  async () => {
    const paginationParams: IPaginationParams = {
      page: 1,
      limit: 100,
    };
    const incidentTypes = await incidentTypeService.findAll(paginationParams);

    const items = incidentTypes.items;

    localStorage.setItem('incidentTypes', JSON.stringify(items));

    return items;
  },
);

export const incidentTypesSlice = createSlice({
  name: 'incidentTypes',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(updateIncidentTypes.fulfilled, (state, action) => {
      state.incidentTypes = action.payload;
    });
  },
});

export const {} = incidentTypesSlice.actions;

export default incidentTypesSlice.reducer;
