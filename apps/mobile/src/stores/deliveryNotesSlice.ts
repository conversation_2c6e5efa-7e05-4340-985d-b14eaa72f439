import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IDeliveryNoteEntity } from '../interfaces/entity/i-delivery-note-entity';
import { tourDeliverService } from '../services';
import { deliveryNotesStorageService } from '../services/OfflineStorageService';
import { capacitorPreferencesStorage } from '../utils/capacitor-preferences-storage';

export interface DeliveryNoteDownloadItem {
  deliveryNoteId: string;
  fileId: string;
  filename: string;
  status: 'pending' | 'downloading' | 'completed' | 'failed';
  localPath?: string;
  error?: string;
  retryCount: number;
  tourId: string;
  stopId: string;
}

export interface DeliveryNotesState {
  downloadQueue: DeliveryNoteDownloadItem[];
  isProcessing: boolean;
  offlineDeliveryNotes: Record<string, string>; // deliveryNoteId -> localPath
  error: string | null;
  isInitialized: boolean;
}

const initialState: DeliveryNotesState = {
  downloadQueue: [],
  isProcessing: false,
  offlineDeliveryNotes: {},
  error: null,
  isInitialized: false,
};

const STORAGE_KEYS = {
  DOWNLOAD_QUEUE: 'deliveryNotes_downloadQueue',
  OFFLINE_NOTES: 'deliveryNotes_offline',
};

export const loadPersistedState = createAsyncThunk('deliveryNotes/loadPersistedState', async () => {
  const [queueData, offlineData] = await Promise.all([
    capacitorPreferencesStorage.getItem(STORAGE_KEYS.DOWNLOAD_QUEUE),
    capacitorPreferencesStorage.getItem(STORAGE_KEYS.OFFLINE_NOTES),
  ]);

  let downloadQueue: DeliveryNoteDownloadItem[] = queueData ? JSON.parse(queueData) : [];

  // Reset any items that were downloading back to pending (app was closed/crashed)
  downloadQueue = downloadQueue.map((item) => {
    if (item.status === 'downloading') {
      console.log('[LoadPersistedState] Resetting downloading item to pending:', item.filename);

      return { ...item, status: 'pending' as const };
    }

    return item;
  });

  return {
    downloadQueue,
    offlineDeliveryNotes: offlineData ? JSON.parse(offlineData) : {},
  };
});

export const queueDeliveryNotesForDownload = createAsyncThunk(
  'deliveryNotes/queueForDownload',
  async (deliveryNotes: IDeliveryNoteEntity[], { getState }) => {
    const state = getState() as { deliveryNotes: DeliveryNotesState };
    const existingQueue = state.deliveryNotes.downloadQueue;
    const offlineNotes = state.deliveryNotes.offlineDeliveryNotes;

    const existingIds = new Set(existingQueue.map((item) => item.deliveryNoteId));
    const alreadyDownloadedIds = new Set(Object.keys(offlineNotes));

    const newItems: DeliveryNoteDownloadItem[] = deliveryNotes
      .filter(
        (note) => note.file && !existingIds.has(note.id) && !alreadyDownloadedIds.has(note.id),
      )
      .map((note) => ({
        deliveryNoteId: note.id,
        fileId: note.file!.id,
        filename: note.filename,
        status: 'pending' as const,
        retryCount: 0,
        tourId: (note as any).tourId || note.stop?.tourId,
        stopId: note.stopId,
      }));

    // Combine existing queue with new items
    const updatedQueue = [...existingQueue, ...newItems];

    // Persist the updated queue to capacitor preferences
    await capacitorPreferencesStorage.setItem(
      STORAGE_KEYS.DOWNLOAD_QUEUE,
      JSON.stringify(updatedQueue),
    );

    return updatedQueue;
  },
);

export const processNextDeliveryNote = createAsyncThunk(
  'deliveryNotes/processNextDeliveryNote',
  async (_, { getState, dispatch }) => {
    const state = getState() as { deliveryNotes: DeliveryNotesState };
    const queue = state.deliveryNotes.downloadQueue;

    const downloadingCount = queue.filter((item) => item.status === 'downloading').length;

    console.log('[ProcessNext] Queue status:', {
      total: queue.length,
      pending: queue.filter((item) => item.status === 'pending').length,
      downloading: downloadingCount,
      completed: queue.filter((item) => item.status === 'completed').length,
      failed: queue.filter((item) => item.status === 'failed').length,
    });

    // Limit concurrent downloads to 3
    if (downloadingCount >= 3) {
      console.log('[ProcessNext] Max concurrent downloads reached (3)');

      return;
    }

    const nextDeliveryNote = queue.find((item) => item.status === 'pending');

    if (nextDeliveryNote) {
      console.log('[ProcessNext] Found pending item:', nextDeliveryNote.filename);
      await dispatch(processDeliveryNoteDownload(nextDeliveryNote));
    } else {
      console.log('[ProcessNext] No pending items found');
    }
  },
);

export const processDeliveryNoteDownload = createAsyncThunk(
  'deliveryNotes/processDeliveryNoteDownload',
  async (deliveryNote: DeliveryNoteDownloadItem, { dispatch }) => {
    try {
      console.log('[ProcessDownload] Starting download for:', deliveryNote.filename);

      // Update status to downloading
      dispatch(
        updateDownloadItemStatus({
          deliveryNoteId: deliveryNote.deliveryNoteId,
          status: 'downloading',
        }),
      );

      const isFileExists = await deliveryNotesStorageService.doesFileExist(deliveryNote.filename);

      if (isFileExists) {
        console.log('[ProcessDownload] File already exists locally:', deliveryNote.filename);
        dispatch(
          updateDownloadItemStatus({
            deliveryNoteId: deliveryNote.deliveryNoteId,
            status: 'completed',
          }),
        );

        return {
          deliveryNoteId: deliveryNote.deliveryNoteId,
          localPath: deliveryNote.localPath,
        };
      }

      // Download the PDF blob with timeout
      const downloadPromise = tourDeliverService.downloadDeliveryNote(
        deliveryNote.tourId,
        deliveryNote.deliveryNoteId,
      );

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Download timeout after 30 seconds')), 30000);
      });

      const blob = await Promise.race([downloadPromise, timeoutPromise]);

      // Convert blob to base64 for storage
      const reader = new FileReader();
      const base64Data = await new Promise<string>((resolve, reject) => {
        reader.onloadend = () => {
          if (reader.result) {
            // Remove the data URL prefix to get just the base64 data
            const base64 = (reader.result as string).split(',')[1];
            resolve(base64);
          } else {
            reject(new Error('Failed to read blob'));
          }
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      // Save the file using the storage service
      await deliveryNotesStorageService.writeFile(deliveryNote.filename, base64Data);

      // Create the local path
      const localPath = `deliveryNotes/${new Date().toISOString().split('T')[0]}/${deliveryNote.filename}`;

      console.log(
        '[ProcessDownload] Download completed for:',
        deliveryNote.filename,
        'Local path:',
        localPath,
      );

      // Update status to completed and save local path
      dispatch(
        updateDownloadItemStatus({
          deliveryNoteId: deliveryNote.deliveryNoteId,
          status: 'completed',
          localPath,
        }),
      );

      // Persist offline notes
      await dispatch(persistOfflineNotes()).unwrap();

      return { deliveryNoteId: deliveryNote.deliveryNoteId, localPath };
    } catch (error) {
      console.error('[ProcessDownload] Error downloading:', deliveryNote.filename, error);

      // Check if it's a 404 error (file not yet imported)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const is404 =
        errorMessage.includes('404') || errorMessage.toLowerCase().includes('not found');

      if (is404) {
        // File not yet imported, this is expected - mark as failed but don't retry
        dispatch(
          updateDownloadItemStatus({
            deliveryNoteId: deliveryNote.deliveryNoteId,
            status: 'failed',
            error: 'File not yet imported',
            shouldRetry: false,
          }),
        );
      } else {
        // Other error, increment retry count
        const newRetryCount = deliveryNote.retryCount + 1;
        const shouldRetry = newRetryCount < 3;

        dispatch(
          updateDownloadItemStatus({
            deliveryNoteId: deliveryNote.deliveryNoteId,
            status: 'failed',
            error: errorMessage,
            retryCount: newRetryCount,
            shouldRetry,
          }),
        );
      }

      throw error;
    }
  },
);

// Helper thunk to persist offline notes to storage
export const persistOfflineNotes = createAsyncThunk(
  'deliveryNotes/persistOfflineNotes',
  async (_, { getState }) => {
    const state = getState() as { deliveryNotes: DeliveryNotesState };
    const { offlineDeliveryNotes, downloadQueue } = state.deliveryNotes;

    await Promise.all([
      capacitorPreferencesStorage.setItem(
        STORAGE_KEYS.OFFLINE_NOTES,
        JSON.stringify(offlineDeliveryNotes),
      ),
      capacitorPreferencesStorage.setItem(
        STORAGE_KEYS.DOWNLOAD_QUEUE,
        JSON.stringify(downloadQueue),
      ),
    ]);
  },
);

const deliveryNotesSlice = createSlice({
  name: 'deliveryNotes',
  initialState,
  reducers: {
    setIsProcessing: (state, action: PayloadAction<boolean>) => {
      state.isProcessing = action.payload;
    },
    updateDownloadItemStatus: (
      state,
      action: PayloadAction<{
        deliveryNoteId: string;
        status: DeliveryNoteDownloadItem['status'];
        localPath?: string;
        error?: string;
        retryCount?: number;
        shouldRetry?: boolean;
      }>,
    ) => {
      const { deliveryNoteId, status, localPath, error, retryCount, shouldRetry } = action.payload;
      const item = state.downloadQueue.find((item) => item.deliveryNoteId === deliveryNoteId);

      if (item) {
        item.status = status;

        if (localPath !== undefined) {
          item.localPath = localPath;
        }

        if (error !== undefined) {
          item.error = error;
        }

        if (retryCount !== undefined) {
          item.retryCount = retryCount;
        }

        // If completed, add to offline notes and remove from queue
        if (status === 'completed' && localPath) {
          state.offlineDeliveryNotes[deliveryNoteId] = localPath;
          // Remove completed items from queue to keep it clean
          state.downloadQueue = state.downloadQueue.filter(
            (qItem) => qItem.deliveryNoteId !== deliveryNoteId,
          );
        }

        // If failed and shouldn't retry, remove from queue
        if (status === 'failed' && shouldRetry === false) {
          state.downloadQueue = state.downloadQueue.filter(
            (qItem) => qItem.deliveryNoteId !== deliveryNoteId,
          );
        }
      }
    },
    removeFromQueue: (state, action: PayloadAction<string>) => {
      state.downloadQueue = state.downloadQueue.filter(
        (item) => item.deliveryNoteId !== action.payload,
      );
    },
    clearFailedDownloads: (state) => {
      state.downloadQueue = state.downloadQueue.filter((item) => item.status !== 'failed');
    },
    resetStuckDownloads: (state) => {
      // Reset any stuck downloads back to pending
      state.downloadQueue = state.downloadQueue.map((item) => {
        if (item.status === 'downloading') {
          console.log('[ResetStuckDownloads] Resetting stuck download:', item.filename);

          return { ...item, status: 'pending' as const };
        }

        return item;
      });
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadPersistedState.fulfilled, (state, action) => {
        state.downloadQueue = action.payload.downloadQueue;
        state.offlineDeliveryNotes = action.payload.offlineDeliveryNotes;
        state.isInitialized = true;
      })
      .addCase(queueDeliveryNotesForDownload.fulfilled, (state, action) => {
        state.downloadQueue = action.payload;
      });
  },
});

export const {
  setIsProcessing,
  updateDownloadItemStatus,
  removeFromQueue,
  clearFailedDownloads,
  resetStuckDownloads,
} = deliveryNotesSlice.actions;

export const deliveryNotesActions = {
  loadPersistedState,
  queueDeliveryNotesForDownload,
  processDeliveryNoteDownload,
  persistOfflineNotes,
  setIsProcessing,
  updateDownloadItemStatus,
  removeFromQueue,
  clearFailedDownloads,
};

export default deliveryNotesSlice.reducer;
