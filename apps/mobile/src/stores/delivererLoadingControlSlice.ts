import { createAsyncThunk, createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { keyBy } from 'lodash';
import {
  EquipmentValidationRecord,
  getEmptyEquipmentValidationRecord,
} from '../components/services/deliverer/EquipmentValidator';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { TourType } from '../interfaces/enum/tour.enums';
import { TourDeliverService } from '../services';
import { addEventToQueue } from './eventQueueSlice';
import { RootState } from './store';

// Alias pour nommer les types
type TourId = string;
type StopId = string;

interface DelivererLoadingControlState {
  tours: Record<TourId, ITourEntity>;
  currentTourId: TourId | null;
  loadEquipmentValidationByStop: Record<StopId, EquipmentValidationRecord>;
  loadingStep: 'frozen' | 'regular';
}

const initialState: DelivererLoadingControlState = {
  tours: {},
  currentTourId: null,
  loadEquipmentValidationByStop: {},
  loadingStep: 'frozen',
};

export const delivererLoadingControlSlice = createSlice({
  name: 'delivererLoadingControl',
  initialState,
  reducers: {
    setTours: (state, action: PayloadAction<ITourEntity[]>) => {
      state.tours = keyBy(action.payload, 'id');

      const allStops = action.payload.flatMap((tour) => tour.stops);

      // Initialise les états des équipements pour chaque stop, si un existe déjà on ne le modifie pas, sinon on initialise avec les valeurs présentes dans la completion du stop (loaded ou preloaded)
      state.loadEquipmentValidationByStop = allStops.reduce(
        (acc, stop) => {
          if (state.loadEquipmentValidationByStop[stop.id]) {
            acc[stop.id] = state.loadEquipmentValidationByStop[stop.id];
          } else {
            const palletCount =
              stop.completion?.loadedEquipmentCount?.palletCount ??
              stop.completion?.preloadedEquipmentCount?.palletCount ??
              0;

            const rollCount =
              stop.completion?.loadedEquipmentCount?.rollCount ??
              stop.completion?.preloadedEquipmentCount?.rollCount ??
              0;

            const packageCount =
              stop.completion?.loadedEquipmentCount?.packageCount ??
              stop.completion?.preloadedEquipmentCount?.packageCount ??
              0;
            acc[stop.id] = getEmptyEquipmentValidationRecord(palletCount, rollCount, packageCount);
          }

          return acc;
        },
        {} as Record<StopId, EquipmentValidationRecord>,
      );
    },
    setCurrentTourId: (state, action: PayloadAction<TourId>) => {
      state.currentTourId = action.payload;
    },
    setLoadingStep: (state, action: PayloadAction<'frozen' | 'regular'>) => {
      state.loadingStep = action.payload;
    },
    updateEquipmentQuantity: (
      state,
      action: PayloadAction<{
        stopId: StopId;
        equipmentKind: LogisticsEquipmentKind;
        quantity: number;
      }>,
    ) => {
      state.loadEquipmentValidationByStop[action.payload.stopId][
        action.payload.equipmentKind
      ].adjustedQuantity = action.payload.quantity;
    },
    updateEquipmentValidation: (
      state,
      action: PayloadAction<{
        stopId: StopId;
        equipmentKind: LogisticsEquipmentKind;
        isValidated: boolean;
      }>,
    ) => {
      state.loadEquipmentValidationByStop[action.payload.stopId][
        action.payload.equipmentKind
      ].isValidated = action.payload.isValidated;
    },
    updateEquipmentValidationOnStop: (
      state,
      action: PayloadAction<{ stopId: StopId; isValidated: boolean }>,
    ) => {
      Object.values(state.loadEquipmentValidationByStop[action.payload.stopId]).forEach(
        (equipment) => {
          equipment.isValidated = action.payload.isValidated;
        },
      );
    },
  },
});

export const processEquipmentToLoadForAllTours = createAsyncThunk(
  'delivererLoadingControl/processEquipmentToLoadForTourId',
  async (_, { getState, dispatch }) => {
    const state = getState() as RootState;
    const tours = Object.values(state.delivererLoadingControl.tours);

    for (const tour of tours) {
      for (const stop of tour.stops) {
        const equipments = state.delivererLoadingControl.loadEquipmentValidationByStop[stop.id];
        dispatch(
          addEventToQueue({
            eventType: 'stop-load',
            payload: {
              stopId: stop.id,
              equipmentCount: {
                packageCount: equipments.PACKAGE.adjustedQuantity,
                palletCount: equipments.PALLET.adjustedQuantity,
                rollCount: equipments.ROLL.adjustedQuantity,
              },
            },
          }),
        );
      }
    }
  },
);

export const delivererLoadingControlActions = {
  setTours: delivererLoadingControlSlice.actions.setTours,
  setCurrentTourId: delivererLoadingControlSlice.actions.setCurrentTourId,
  setLoadingStep: delivererLoadingControlSlice.actions.setLoadingStep,
  updateEquipmentQuantity: delivererLoadingControlSlice.actions.updateEquipmentQuantity,
  updateEquipmentValidation: delivererLoadingControlSlice.actions.updateEquipmentValidation,
  updateEquipmentValidationOnStop:
    delivererLoadingControlSlice.actions.updateEquipmentValidationOnStop,
};

const toursEligibleToLoad = createSelector(
  (state: RootState) => state.delivererLoadingControl.tours,
  (tours) => {
    return Object.values(tours).filter((tour) => TourDeliverService.isTourEligibleToLoad(tour));
  },
);

const toursForCurrentStepSelector = createSelector(
  (state: RootState) => toursEligibleToLoad(state),
  (state: RootState) => state.delivererLoadingControl.loadingStep,
  (tours, loadingStep) => {
    if (loadingStep === 'frozen') {
      return Object.values(tours).filter((tour) => tour.tourIdentifier.type === TourType.Frozen);
    } else {
      return Object.values(tours).filter((tour) => tour.tourIdentifier.type === TourType.Normal);
    }
  },
);

export const delivererLoadingControlSelectors = {
  toursForCurrentStep: toursForCurrentStepSelector,
  areAllStopsValidatedForCurrentTour: createSelector(
    (state: RootState) => state.delivererLoadingControl.loadEquipmentValidationByStop,
    (state: RootState) =>
      state.delivererLoadingControl.currentTourId
        ? state.delivererLoadingControl.tours[state.delivererLoadingControl.currentTourId]
        : null,
    (loadEquipmentValidationByStop, currentTour) => {
      if (!currentTour) {
        return false;
      }

      return currentTour.stops.every((stop) =>
        Object.values(loadEquipmentValidationByStop[stop.id]).every(
          (validation) => validation.isValidated,
        ),
      );
    },
  ),
  areAllStopsValidatedForCurrentStep: createSelector(
    (state: RootState) => toursForCurrentStepSelector(state),
    (state: RootState) => state.delivererLoadingControl.loadEquipmentValidationByStop,
    (toursForCurrentStep, loadEquipmentValidationByStop) => {
      return toursForCurrentStep.every((tour) =>
        tour.stops.every((stop) =>
          Object.values(loadEquipmentValidationByStop[stop.id]).every(
            (validation) => validation.isValidated,
          ),
        ),
      );
    },
  ),

  toursEligibleToLoad,
};

export default delivererLoadingControlSlice.reducer;
