import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  DeliveryCompletionType,
  ICompleteStopDeliveryDto,
} from '../interfaces/dto/stop-delivery.dto';
import { IStopEntity } from '../interfaces/entity/i-stop-entity';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { addEventToQueue } from './eventQueueSlice';
import { RootState } from './store';

export const DELIVERY_PROCESS_STEP = [
  'delivery-details',
  'payment',
  'equipment-deposit',
  'equipment-pickup',
  'attachments',
  'signature',
  'success',
] as const;

interface DeliveryProcessState {
  stop: IStopEntity;
  tour: ITourEntity;
  stopCompletionFormData: Partial<ICompleteStopDeliveryDto>;
  currentStep: (typeof DELIVERY_PROCESS_STEP)[number];
  stepHistory: (typeof DELIVERY_PROCESS_STEP)[number][];
  hasBeenSubmitted: boolean;
}

const initialState: DeliveryProcessState = {
  stop: {} as IStopEntity,
  tour: {} as ITourEntity,
  stopCompletionFormData: {
    returnedEquipment: {
      palletCount: 0,
      rollCount: 0,
      packageCount: 0,
    },
    unloadedEquipment: {
      palletCount: 0,
      rollCount: 0,
      packageCount: 0,
    },
    deliveryCompletionType: DeliveryCompletionType.FULL,
  },
  currentStep: 'delivery-details',
  stepHistory: [],
  hasBeenSubmitted: false,
};

export const deliveryProcessSlice = createSlice({
  name: 'deliveryProcess',
  initialState,
  reducers: {
    setStop: (state, action: PayloadAction<IStopEntity>) => {
      state.stop = action.payload;
    },
    setTour: (state, action: PayloadAction<ITourEntity>) => {
      state.tour = action.payload;
    },
    setStopCompletionFormData: (
      state,
      action: PayloadAction<Partial<ICompleteStopDeliveryDto>>,
    ) => {
      state.stopCompletionFormData = {
        ...state.stopCompletionFormData,
        ...action.payload,
      };
    },
    setCurrentStep: (state, action: PayloadAction<(typeof DELIVERY_PROCESS_STEP)[number]>) => {
      state.currentStep = action.payload;
      state.stepHistory.push(action.payload);
    },
    goBack: (state) => {
      state.stepHistory.pop();
      const lastStep = state.stepHistory[state.stepHistory.length - 1];

      if (lastStep) {
        state.currentStep = lastStep;
      } else {
        const currentStepIndex = DELIVERY_PROCESS_STEP.indexOf(state.currentStep);
        const previousStep = DELIVERY_PROCESS_STEP[currentStepIndex - 1];
        state.currentStep = previousStep;
        state.stepHistory.push(previousStep);
      }
    },
    setNextStep: (state) => {
      const currentStepIndex = DELIVERY_PROCESS_STEP.indexOf(state.currentStep);
      const nextStep = DELIVERY_PROCESS_STEP[currentStepIndex + 1];
      state.currentStep = nextStep;
      state.stepHistory.push(nextStep);
    },
    reset: (state) => {
      state.stop = initialState.stop;
      state.tour = initialState.tour;
      state.stopCompletionFormData = initialState.stopCompletionFormData;
      state.currentStep = initialState.currentStep;
      state.stepHistory = initialState.stepHistory;
      state.hasBeenSubmitted = false;
    },
    setHasBeenSubmitted: (state, action: PayloadAction<boolean>) => {
      state.hasBeenSubmitted = action.payload;
    },
  },
});

export const deliveryProcessSelectors = {};

const isStopCompletionFormDataValid = (
  stopCompletionFormData: Partial<ICompleteStopDeliveryDto>,
): stopCompletionFormData is ICompleteStopDeliveryDto => {
  return true;
};

export const completeStopDelivery = createAsyncThunk(
  'deliveryProcess/completeStopDelivery',
  async (_, { dispatch, getState }) => {
    const stopCompletionFormData = (getState() as RootState).deliveryProcess.stopCompletionFormData;
    const stopId = (getState() as RootState).deliveryProcess.stop.id;
    const hasBeenSubmitted = (getState() as RootState).deliveryProcess.hasBeenSubmitted;

    if (hasBeenSubmitted) {
      return;
    }

    if (!isStopCompletionFormDataValid(stopCompletionFormData)) {
      throw new Error('Stop completion form data is not valid');
    }

    dispatch(
      addEventToQueue({
        eventType: 'stop-completion',
        payload: {
          stopId,
          ...stopCompletionFormData,
        },
      }),
    );

    dispatch(deliveryProcessSlice.actions.setHasBeenSubmitted(true));
  },
);

export const deliveryProcessActions = {
  setStop: deliveryProcessSlice.actions.setStop,
  setTour: deliveryProcessSlice.actions.setTour,
  setStopCompletionFormData: deliveryProcessSlice.actions.setStopCompletionFormData,
  setCurrentStep: deliveryProcessSlice.actions.setCurrentStep,
  goBack: deliveryProcessSlice.actions.goBack,
  setNextStep: deliveryProcessSlice.actions.setNextStep,
  reset: deliveryProcessSlice.actions.reset,
};

export default deliveryProcessSlice.reducer;
