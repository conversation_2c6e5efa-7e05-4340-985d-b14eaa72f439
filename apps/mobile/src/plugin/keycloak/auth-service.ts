import { ConnectionStatus, Network } from '@capacitor/network';
import { Preferences } from '@capacitor/preferences';
import mitt from 'mitt';
import { Logger } from '../../utils/logger';
import { KeycloakIdTokenParsed, keycloakService, KeycloakService } from './keycloak-service';

type AuthServiceEvent = {
  onAuthStateChange: void;
};

interface Tokens {
  accessToken: string;
  refreshToken: string;
  idToken: string;
  idTokenParsed: KeycloakIdTokenParsed;
  refreshTokenExpiresAt: number;
}

export class AuthService {
  private TOKEN_STORAGE_KEYS = {
    TOKENS: 'tokens',
  };
  private isDeviceOnline = false;
  private logger = new Logger('AuthService');
  private isInitialized = false;

  public eventEmitter = mitt<AuthServiceEvent>();

  constructor(private readonly keycloakService: KeycloakService) {}

  public async init(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    this.setupListeners();
    await this.loadTokensIfExists();

    const networkStatus = await Network.getStatus().catch((error) => {
      this.logger.error('Error getting network status', error);

      return null;
    });

    if (networkStatus) {
      await this.handleConnectionStatusChange(networkStatus);
    }

    this.isInitialized = true;
  }

  public async saveTokens(): Promise<void> {
    this.logger.info('Saving tokens');

    const tokens = {
      accessToken: this.keycloakService.keycloak.token,
      refreshToken: this.keycloakService.keycloak.refreshToken,
      idToken: this.keycloakService.keycloak.idToken,
      idTokenParsed: this.keycloakService.keycloak.idTokenParsed,
      refreshTokenExpiresAt: this.keycloakService.keycloak.refreshTokenParsed?.expires_at,
    };

    if (!tokens.accessToken || !tokens.refreshToken) {
      this.logger.error('No tokens to save');

      return;
    }

    await Preferences.set({
      key: this.TOKEN_STORAGE_KEYS.TOKENS,
      value: JSON.stringify(tokens),
    });

    this.eventEmitter.emit('onAuthStateChange');
  }

  public async getValidToken(): Promise<string | null> {
    if (!this.keycloakService.initialized) {
      try {
        await this.keycloakService.init();
      } catch (error) {
        this.logger.error('Failed to initialize Keycloak in getValidToken', error);

        return null;
      }
    }

    return this.keycloakService.keycloak.token ?? null;
  }

  public async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await this.getValidToken();

    if (token) {
      return {
        Authorization: `Bearer ${token}`,
      };
    }

    return {};
  }

  public async getTokens(): Promise<Tokens | null> {
    const { value } = await Preferences.get({
      key: this.TOKEN_STORAGE_KEYS.TOKENS,
    });

    return value ? JSON.parse(value) : null;
  }

  public async clearTokens(): Promise<void> {
    this.logger.info('Clearing tokens');
    await Preferences.remove({ key: this.TOKEN_STORAGE_KEYS.TOKENS });

    this.eventEmitter.emit('onAuthStateChange');
  }

  /**
   * Vérifie si l'utilisateur est authentifié,
   * on part du principe que si le refresh token est valide, l'utilisateur est authentifié, en effet il pourra query un nouveau token d'auth si celui-ci expire
   */
  public async isAuthenticated(): Promise<boolean> {
    const tokens = await this.getTokens();

    if (!tokens) {
      return false;
    }

    if (
      new Date().getTime() > tokens.refreshTokenExpiresAt &&
      tokens.refreshTokenExpiresAt !== 0 // Si refresh token a un expiration de 0, c'est que le token est infini
    ) {
      return false;
    }

    return true;
  }

  private async loadTokensIfExists(): Promise<void> {
    const tokens = await this.getTokens();

    if (tokens) {
      this.keycloakService.setToken(tokens.accessToken, tokens.refreshToken, tokens.idToken);
    }
  }

  private async handleConnectionStatusChange(status: ConnectionStatus): Promise<void> {
    this.isDeviceOnline = status.connected;
    this.logger.info('Network status changed', status.connected);

    if (!this.keycloakService.initialized) {
      await this.keycloakService.init();

      return;
    }

    if (this.isDeviceOnline) {
      this.logger.info('Network online, starting refresh token check.');
      this.keycloakService.startRefreshTokenCheck();
    } else {
      this.logger.info('Network offline, stopping refresh token check.');
      this.keycloakService.stopRefreshTokenCheck();
    }
  }

  private setupListeners(): void {
    Network.addListener('networkStatusChange', this.handleConnectionStatusChange.bind(this));

    this.keycloakService.eventEmitter.on('onReady', (authenticated: boolean) => {
      if (authenticated) {
        this.saveTokens();
      } else {
        this.clearTokens();
      }
    });

    this.keycloakService.eventEmitter.on('onAuthSuccess', () => {
      this.saveTokens();
    });

    this.keycloakService.eventEmitter.on('onAuthError', () => {
      this.clearTokens();
    });

    this.keycloakService.eventEmitter.on('onTokenExpired', () => {
      this.clearTokens();
    });

    this.keycloakService.eventEmitter.on('onAuthRefreshSuccess', () => {
      this.saveTokens();
    });

    this.keycloakService.eventEmitter.on('onAuthRefreshError', () => {
      this.clearTokens();
    });

    this.keycloakService.eventEmitter.on('onAuthLogout', () => {
      this.clearTokens();
    });

    this.keycloakService.eventEmitter.on('onActionUpdate', () => {
      this.clearTokens();
    });
  }
}

export const authService = new AuthService(keycloakService);
