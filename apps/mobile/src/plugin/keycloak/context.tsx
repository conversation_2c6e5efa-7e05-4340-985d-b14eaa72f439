import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import LoadingIndicator from '../../components/ui/LoadingIndicator';
import { initializeCurrentUserStore, updateCurrentUser } from '../../stores/currentUserSlice';
import { useAppDispatch, useAppSelector } from '../../utils/redux';
import { authService } from './auth-service';

interface AuthContextProps {
  initialized: boolean;
  isAuthenticated: boolean;
}

export const AuthContext = createContext<AuthContextProps>({
  initialized: false,
  isAuthenticated: false,
});

export const AuthContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [keycloakInitialized, setKeycloakInitialized] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const dispatch = useAppDispatch();

  const isCurrentUserStoreInitialized = useAppSelector((state) => state.currentUser.isInitialized);

  const isInitialized = useMemo(() => {
    return keycloakInitialized && isCurrentUserStoreInitialized;
  }, [keycloakInitialized, isCurrentUserStoreInitialized]);

  useEffect(() => {
    authService.init().then(() => {
      setKeycloakInitialized(true);
    });
  }, []);

  useEffect(() => {
    if (!isCurrentUserStoreInitialized) {
      dispatch(initializeCurrentUserStore());
    }
  }, [isCurrentUserStoreInitialized]);

  useEffect(() => {
    authService
      .isAuthenticated()
      .then((isAuthenticated) => {
        setIsAuthenticated(isAuthenticated);
      })
      .finally(() => {
        authService.eventEmitter.on('onAuthStateChange', async () => {
          const newAuthState = await authService.isAuthenticated();

          if (isAuthenticated !== newAuthState) {
            setIsAuthenticated(newAuthState);
          }
        });
      });

    return () => {
      authService.eventEmitter.off('onAuthStateChange');
    };
  }, [isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated && keycloakInitialized) {
      dispatch(updateCurrentUser());
    }
  }, [isAuthenticated, keycloakInitialized]);

  if (!isInitialized && !keycloakInitialized) {
    return <LoadingIndicator text="Connexion..." />;
  }

  return (
    <AuthContext.Provider value={{ initialized: keycloakInitialized, isAuthenticated }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const { initialized, isAuthenticated } = useContext(AuthContext);

  if (!initialized) {
    throw new Error('Keycloak service is not initialized');
  }

  return {
    isAuthenticated,
  } as {
    isAuthenticated: boolean;
  };
};
