import { Capacitor } from '@capacitor/core';
import Keyclo<PERSON>, {
  type KeycloakConfig,
  type KeycloakError,
  type KeycloakInitOptions,
} from 'keycloak-js';
import mitt from 'mitt';
import APP_CONFIG from '../../config/app.config';
import { CapacitorAdapter } from '../../utils/keycloak-adapter';
import { Logger } from '../../utils/logger';

type KeycloakEvent = {
  onReady: boolean;
  onAuthSuccess: boolean;
  onAuthError: KeycloakError;
  onAuthRefreshSuccess: boolean;
  onAuthRefreshError: boolean;
  onAuthLogout: boolean;
  onTokenExpired: boolean;
  onActionUpdate: {
    status: 'success' | 'cancelled' | 'error';
    action?: string;
  };
};

export type KeycloakIdTokenParsed = {
  /** Should not take care of it because offline mode */
  exp: number;
  iat: number;
  auth_time: number;
  jti: string;
  iss: string;
  aud: string;
  sub: string;
  typ: string;
  azp: string;
  sid: string;
  at_hash: string;
  acr: string;
  email_verified: boolean;
  name?: string;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
  email?: string;
};

export class KeycloakService {
  public readonly keycloak: Keycloak;
  public initialized = false;
  private initPromise: Promise<boolean> | null = null;
  public eventEmitter = mitt<KeycloakEvent>();
  private logger = new Logger('keycloakService');
  private refreshTokenCheckInterval: any = null;

  constructor(
    private readonly keycloakConfig: KeycloakConfig,
    private readonly keycloakInitOptions: KeycloakInitOptions,
  ) {
    this.keycloak = new Keycloak(this.keycloakConfig);
    this.logger.info('KeycloakService constructed');
    this.setupEventListeners();
  }

  public async init(): Promise<boolean> {
    if (this.initialized) {
      this.logger.info('KeycloakService already initialized');

      return true;
    }

    if (this.initPromise) {
      this.logger.info('KeycloakService initialization already in progress');

      return this.initPromise;
    }

    this.logger.info('Initializing Keycloak...');

    if (
      this.keycloakInitOptions.adapter &&
      typeof this.keycloakInitOptions.adapter === 'string' &&
      this.keycloakInitOptions.adapter !== 'default'
    ) {
      this.logger.info('Using CapacitorAdapter');
      this.keycloakInitOptions.adapter = new CapacitorAdapter(this.keycloak) as any;
    }

    this.initPromise = this.keycloak
      .init(this.keycloakInitOptions)
      .then((authenticated) => {
        this.logger.info('Keycloak initialized', { authenticated });
        this.startRefreshTokenCheck();
        this.initialized = true;

        return authenticated;
      })
      .catch((err) => {
        this.logger.error('Keycloak initialization failed', err);
        throw err;
      });

    return this.initPromise;
  }

  public startRefreshTokenCheck(): void {
    if (this.refreshTokenCheckInterval) {
      this.logger.info('Refresh token interval already running');

      return;
    }
    this.logger.info('Starting refresh token interval');
    this.refreshTokenCheckInterval = setInterval(
      () => {
        this.updateToken();
      },
      1000 * 60 * 1,
    );
  }

  public stopRefreshTokenCheck(): void {
    if (this.refreshTokenCheckInterval) {
      clearInterval(this.refreshTokenCheckInterval);
      this.refreshTokenCheckInterval = null;
      this.logger.info('Stopped refresh token interval');
    }
  }

  public setToken(token: string, refreshToken: string, idToken: string): void {
    this.keycloak.setToken(token, refreshToken, idToken);
    this.logger.info('Tokens set', {
      token: !!token,
      refreshToken: !!refreshToken,
      idToken: !!idToken,
    });
    this.keycloakInitOptions.token = token;
    this.keycloakInitOptions.refreshToken = refreshToken;
    this.keycloakInitOptions.idToken = idToken;
  }

  public async login(): Promise<void> {
    try {
      await this.keycloak.login({
        scope: 'openid profile email offline_access',
      });
      this.logger.info('Login successful');
    } catch (err) {
      this.logger.error('Login failed', err);
      throw err;
    }
  }

  public async logout(): Promise<void> {
    try {
      await this.keycloak.logout();
      this.keycloak.clearToken();
      this.logger.info('Logout successful');
    } catch (err) {
      this.logger.error('Logout failed', err);
      throw err;
    }
  }

  public getRoles(): string[] {
    const realmRoles = this.keycloak.realmAccess?.roles || [];
    const resourceRoles = this.keycloak.resourceAccess?.[this.keycloakConfig.clientId]?.roles || [];
    const allRoles = [...realmRoles, ...resourceRoles];
    this.logger.info('getRoles', { realmRoles, resourceRoles, allRoles });

    return allRoles;
  }

  public getTokenRemainingValidity(): number {
    const exp = this.keycloak.tokenParsed?.exp;
    const timeSkew = this.keycloak.timeSkew;

    if (!exp || !timeSkew) {
      this.logger.warn('No exp or timeSkew for token');

      return 0;
    }

    const currentTime = new Date().getTime() / 1000;
    const validity = exp ? Math.round(exp - currentTime - timeSkew) : 0;
    this.logger.info('Token remaining validity', { validity });

    return validity;
  }

  private async updateToken(): Promise<boolean> {
    if (!this.keycloak.refreshToken) {
      this.logger.warn('No refreshToken to update');

      return false;
    }
    try {
      const refreshed = await this.keycloak.updateToken(70);
      this.logger.info('Token updated', { refreshed });

      return refreshed;
    } catch (err) {
      this.logger.error('Token update failed', err);

      return false;
    }
  }

  private setupEventListeners(): void {
    this.keycloak.onReady = (authenticated) => {
      this.logger.info('onReady event', { authenticated });
      this.eventEmitter.emit('onReady', authenticated || false);
    };

    this.keycloak.onAuthSuccess = (): void => {
      this.logger.info('onAuthSuccess event');
      this.eventEmitter.emit('onAuthSuccess', true);
    };

    this.keycloak.onAuthError = (errorData): void => {
      this.logger.error('onAuthError event', errorData);
      this.eventEmitter.emit('onAuthError', errorData);
    };

    this.keycloak.onAuthRefreshSuccess = (): void => {
      this.logger.info('onAuthRefreshSuccess event');
      this.eventEmitter.emit('onAuthRefreshSuccess', true);
    };

    this.keycloak.onAuthRefreshError = (): void => {
      this.logger.warn('onAuthRefreshError event');
      this.eventEmitter.emit('onAuthRefreshError', true);
    };

    this.keycloak.onAuthLogout = (): void => {
      this.logger.info('onAuthLogout event');
      this.eventEmitter.emit('onAuthLogout', true);
    };

    this.keycloak.onTokenExpired = (): void => {
      this.logger.warn('onTokenExpired event');
      this.eventEmitter.emit('onTokenExpired', true);
    };

    this.keycloak.onActionUpdate = (status, action): void => {
      this.logger.info('onActionUpdate event', { status, action });
      this.eventEmitter.emit('onActionUpdate', { status, action });
    };
  }
}

const isMobile: boolean = Capacitor.isNativePlatform();

// Configuration adaptée selon la plateforme
const redirectUri: string = isMobile
  ? `${APP_CONFIG.ENV.APP_IDENTIFIER}://callback`
  : window.location.origin;

const silentCheckSsoRedirectUri: string = `${redirectUri}/silent-check-sso.html`;

const logger = new Logger('keycloakService');
logger.log('silentCheckSsoRedirectUri', silentCheckSsoRedirectUri);
logger.log('redirectUri', redirectUri);
logger.log('isMobile', isMobile);

export const keycloakService: KeycloakService = new KeycloakService(
  {
    url: APP_CONFIG.ENV.KEYCLOAK_URL,
    realm: APP_CONFIG.ENV.KEYCLOAK_REALM,
    clientId: APP_CONFIG.ENV.KEYCLOAK_CLIENT_ID,
  },
  {
    adapter: isMobile ? 'cordova-native' : 'default', // Utiliser cordova-native pour les plateformes mobiles
    checkLoginIframe: false,
    pkceMethod: 'S256', // Utiliser PKCE pour plus de sécurité
    onLoad: !isMobile ? 'check-sso' : undefined,
    silentCheckSsoRedirectUri: silentCheckSsoRedirectUri,
    redirectUri: redirectUri,
    enableLogging: true,
  },
);
