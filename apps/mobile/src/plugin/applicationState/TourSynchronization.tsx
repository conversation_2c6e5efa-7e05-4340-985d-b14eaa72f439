import { memo, useEffect } from 'react';
import { deliveryNoteCleanupService } from '../../services/DeliveryNoteCleanupService';
import { useAppDispatch } from '../../utils/redux';
import { useDeliveryNoteSynchronization } from './useDeliveryNoteSynchronization';
import { useTourSynchronization } from './useTourSynchronization';

export const TourSynchronization = memo(() => {
  const dispatch = useAppDispatch();

  useTourSynchronization();
  useDeliveryNoteSynchronization();

  // Run cleanup on startup if needed
  useEffect(() => {
    const runCleanup = async () => {
      try {
        const shouldRun = await deliveryNoteCleanupService.shouldRunCleanup();

        if (shouldRun) {
          console.log('[TourSynchronization] Running delivery note cleanup...');
          const stats = await deliveryNoteCleanupService.cleanupOldDeliveryNotes();

          if (stats.deletedFolders.length > 0) {
            console.log(
              `[TourSynchronization] Cleanup completed: ${stats.deletedFolders.length} folders, ${stats.deletedFiles} files deleted`,
            );

            // Clean up Redux state to remove references to deleted files
            await deliveryNoteCleanupService.cleanupReduxState(dispatch);
          }
        }
      } catch (error) {
        console.error('[TourSynchronization] Error during cleanup:', error);
      }
    };

    runCleanup();
  }, [dispatch]);

  return null;
});
