import { createSelector } from '@reduxjs/toolkit';
import { useEffect } from 'react';
import {
  loadPersistedState,
  processNextDeliveryNote,
  setIsProcessing,
} from '../../stores/deliveryNotesSlice';
import { RootState } from '../../stores/store';
import { useAppDispatch, useAppSelector } from '../../utils/redux';

const hasActiveDownloadsSelector = createSelector(
  (state: RootState) => state.deliveryNotes.downloadQueue,
  (downloadQueue) =>
    downloadQueue.some((item) => item.status === 'pending' || item.status === 'downloading'),
);

export function useDeliveryNoteSynchronization() {
  const isInitialized = useAppSelector((state) => state.deliveryNotes.isInitialized);
  const isProcessing = useAppSelector((state) => state.deliveryNotes.isProcessing);
  const hasInternet = useAppSelector((state) => state.applicationState.hasInternet);
  const hasActiveDownloads = useAppSelector(hasActiveDownloadsSelector);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (!isInitialized) {
      dispatch(loadPersistedState());

      return;
    }

    if (!hasActiveDownloads) {
      if (isProcessing) {
        dispatch(setIsProcessing(false));
      }

      return;
    }

    // Si des téléchargements actifs et internet disponible, on démarre le traitement
    if (hasInternet && !isProcessing) {
      dispatch(setIsProcessing(true));
    }

    // Si des téléchargements actifs et internet non disponible, on stoppe le traitement
    if (!hasInternet && isProcessing) {
      dispatch(setIsProcessing(false));
    }
  }, [isProcessing, isInitialized, hasInternet, hasActiveDownloads, dispatch]);

  useEffect(() => {
    if (!isProcessing) {
      return;
    }

    console.log('[DeliveryNoteSync] Starting download processing...');

    const fetchNextDeliveryNoteInterval = setInterval(() => {
      console.log('[DeliveryNoteSync] Processing next delivery note...');
      dispatch(processNextDeliveryNote());
    }, 200);

    return () => {
      console.log('[DeliveryNoteSync] Stopping download processing...');
      clearInterval(fetchNextDeliveryNoteInterval);
    };
  }, [isProcessing, dispatch]);
}
