import { Network } from '@capacitor/network';
import React, { useEffect } from 'react';
import { setApplicationStoreInitialized, setHasInternet } from '../../stores/applicationStateSlice';
import { RootState } from '../../stores/store';
import { useAppDispatch, useAppSelector } from '../../utils/redux';

const isInitializedSelector = (state: RootState) => {
  return state.applicationState.isInitialized;
};

export function ApplicationStateProvider({ children }: { children: React.ReactNode }) {
  const isInitialized = useAppSelector(isInitializedSelector);
  const dispatch = useAppDispatch();

  const checkInternetConnectivity = async () => {
    const status = await Network.getStatus();
    dispatch(setHasInternet(status.connected));
  };

  useEffect(() => {
    Promise.all([checkInternetConnectivity()]).finally(() => {
      dispatch(setApplicationStoreInitialized(true));
    });
  }, [dispatch]);

  useEffect(() => {
    Network.addListener('networkStatusChange', (status) => {
      dispatch(setHasInternet(status.connected));
    });

    return () => {
      Network.removeAllListeners();
    };
  }, [dispatch]);

  if (!isInitialized) {
    return null;
  }

  return <>{children}</>;
}
