import { useContext, useEffect } from 'react';
import { useCurrentRouteAudience } from '../../hooks/useCurrentRouteAudience';
import { updateIncidentTypes } from '../../stores/incidentTypeSlice';
import { updateLogisticEquipmentTypes } from '../../stores/logisticEquipmentsSlice';
import { getToursForToday } from '../../stores/tourSlice';
import { useAppDispatch, useAppSelector } from '../../utils/redux';
import { AuthContext } from '../keycloak/context';

export function useTourSynchronization() {
  const dispatch = useAppDispatch();
  const hasInternet = useAppSelector((state) => state.applicationState.hasInternet);
  const isDataResolved = useAppSelector((state) => state.tour.isDataResolved);
  const isDeliveryNotesInitialized = useAppSelector((state) => state.deliveryNotes.isInitialized);
  const userRole = useCurrentRouteAudience();
  const { isAuthenticated } = useContext(AuthContext);
  const isApplicationStoreInitialized = useAppSelector(
    (state) => state.applicationState.isInitialized,
  );
  const loading = useAppSelector((state) => state.tour.loading);

  useEffect(() => {
    if (
      isDataResolved ||
      !isApplicationStoreInitialized ||
      loading ||
      !isDeliveryNotesInitialized ||
      !isAuthenticated
    ) {
      return;
    }

    const timeoutId = setTimeout(() => {
      dispatch(
        getToursForToday({
          hasInternet,
          userRole,
        }),
      );
      dispatch(updateLogisticEquipmentTypes());
      dispatch(updateIncidentTypes());
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [
    dispatch,
    hasInternet,
    isDataResolved,
    isApplicationStoreInitialized,
    loading,
    isDeliveryNotesInitialized,
    isAuthenticated,
    userRole,
  ]);
}
