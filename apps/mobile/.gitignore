# Build
dist/
build/
www/

# Dependencies
node_modules/

# Environment variables - ignore local overrides
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Testing
coverage/
/test-results/


# Cache
.cache/
.parcel-cache/
.temp/
.tmp/

# Editor directories and files
.idea/
.vscode/*
!.vscode/extensions.json

# OS files
.DS_Store

# Don't ignore example files
!.env.example

.tsbuildinfo


# Specifies intentionally untracked files to ignore when using Git
# http://git-scm.com/docs/gitignore

# Ionic
.ionic/
.sass-cache/
.sourcemaps/
.versions/
coverage/
dist/
node_modules/
tmp/
temp/
www/
platforms/
plugins/
hooks/

# Keep hooks from react
!src/hooks
!src/**/hooks

# Capacitor
.capacitor/

# Compiled output
/www
/out-tsc

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Dependencies
/bower_components

# IDEs and editors
/.idea
/.vscode
*.sublime-project
*.sublime-workspace
.project
.classpath
.c9/
*.launch
.settings/
*.tmproj
*.esproj
.factorypath
nbproject/
.vs/

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings
.angular/

# System Files
.DS_Store
Thumbs.db

# Android
/android/app/release/
/android/app/debug/
/android/app/build/
/android/build/
/android/.gradle/
/android/local.properties
*.keystore
*.jks

# iOS
/ios/App/App/build/
/ios/App/App/Pods/
/ios/App/Podfile.lock
xcuserdata/
*.xcworkspace/
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.moved-aside
DerivedData/
.idea/
*.hmap
*.ipa
*.dSYM.zip
*.dSYM
/*.gcno
**/xcshareddata/WorkspaceSettings.xcsettings

# Environment
.env
.env.*
!.env.example

# Build
/build/