# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Mobile App Development Commands

### Core Development
```bash
pnpm dev                # Vite dev server (port 8001)
pnpm build              # Production build
pnpm lint               # ESLint with auto-fix
pnpm typecheck          # TypeScript validation
pnpm tidy               # Format + lint combined
```

### Capacitor/Native Commands
```bash
pnpm sync               # Sync web assets to native platforms
pnpm open:ios           # Open iOS project in Xcode
pnpm live:ios           # Run iOS with live reload
npx cap add android     # Add Android platform (if needed)
npx cap run ios         # Run on iOS simulator/device
npx cap run android     # Run on Android emulator/device
```

## Mobile-Specific Architecture

### Technology Stack
- **Framework**: Ionic React v8.5.0 with Capacitor v7.2.0
- **State Management**: Redux Toolkit + custom ApplicationStateProvider
- **Navigation**: Ionic Router v5 with tabs
- **Authentication**: Keycloak integration shared with web apps
- **Forms**: React Hook Form + Zod validation
- **Native Features**: Network status, preferences storage, haptics

### App Structure
```
src/
├── components/         # Ionic + custom UI components
│   └── ui/            # Custom form components
├── pages/             # Route components (tabs)
├── plugin/            # Native integrations & providers
│   ├── applicationState/   # Connectivity & sync state
│   └── keycloak/          # Auth service & context
├── services/          # API clients matching backend structure
├── stores/            # Redux slices
└── config/            # Routes & constants
```

### Key Patterns
- **API Proxy**: Development server proxies `/api` to `http://localhost:3000`
- **State Sync**: `useTourSynchronisation` hook manages offline/online data sync
- **Storage**: Capacitor Preferences for persistent local storage
- **Network Status**: ApplicationStateProvider tracks connectivity
- **Shared Types**: Uses same DTOs/entities as backend via imports

### Native Platform Configuration
- **iOS**: `ios/App/` - Xcode project with Swift AppDelegate
- **Android**: `android/` - Gradle project with MainActivity
- **Capacitor Config**: `capacitor.config.ts` - App ID: `fr.artdigit.lrg`
- **Icons/Splash**: Located in platform-specific resource directories

### Development Notes
- Mobile app shares domain entities with backend
- API service methods mirror backend controllers
- Keycloak adapter uses custom storage for mobile
- Redux store persists selected state to Preferences
- Network plugin monitors connectivity for offline capability

## Offline Functionality

### Delivery Notes Offline Storage

The app supports offline access to delivery notes (PDFs) through:

1. **OfflineStorageService** (`src/services/OfflineStorageService.ts`):
   - Stores PDFs using Capacitor Filesystem plugin
   - Manages metadata in Capacitor Preferences
   - Provides download queue management
   - Supports retry logic for failed downloads (max 3 attempts)

2. **DownloadManagerService** (`src/services/DownloadManagerService.ts`):
   - Processes download queue in background
   - Monitors network connectivity via Redux state
   - Handles automatic retries
   - Pauses when offline, resumes when online

3. **UI Components**:
   - `DeliveryNoteButton`: Shows download/offline status, handles PDF viewing
   - `DownloadStatus`: Displays global download progress
   - Settings page includes offline storage management

### Offline Architecture

- PDFs stored in app's Documents directory under `delivery_notes/`
- Metadata stored in Capacitor Preferences (JSON)
- Automatic download triggered when tours are fetched
- Network state monitored via Capacitor Network plugin
- File opening handled by @capacitor-community/file-opener on native platforms
- Web fallback opens PDFs in new browser tab

### Queue Management
- Queue items have statuses: pending, downloading, completed, failed
- Failed downloads retry up to 3 times
- Queue persists across app restarts
- Network changes automatically start/stop processing