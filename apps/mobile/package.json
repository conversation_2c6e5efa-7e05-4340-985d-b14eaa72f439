{"name": "@lrg/mobile", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint --fix ./src", "format": "prettier --write \"./src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "tidy": "pnpm format && pnpm lint", "preview": "vite preview", "ios": "cap add ios", "sync": "cap sync", "open:ios": "cap open ios", "live:ios": "cap run ios -l --external", "typecheck": "tsc --noEmit"}, "dependencies": {"@capacitor-community/file-opener": "^7.0.1", "@capacitor-community/sqlite": "^7.0.1", "@capacitor/android": "^7.0.1", "@capacitor/app": "7.0.1", "@capacitor/browser": "7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/core": "7.2.0", "@capacitor/filesystem": "^7.0.1", "@capacitor/geolocation": "^7.1.4", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "^7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@hookform/resolvers": "^5.0.1", "@ionic/react": "^8.5.0", "@ionic/react-router": "^8.5.0", "@reduxjs/toolkit": "^2.8.1", "@types/luxon": "^3.6.2", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "axios": "^1.8.4", "dexie": "^4.0.11", "globals": "^16.0.0", "immer": "^10.1.1", "ionicons": "^7.4.0", "jeep-sqlite": "^2.8.0", "jwt-decode": "^4.0.0", "keycloak-js": "26.1.5", "lodash": "4.17.21", "lucide-react": "^0.509.0", "luxon": "^3.6.1", "mitt": "^3.0.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.54.2", "react-redux": "^9.2.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "sql.js": "^1.13.0", "zod": "^3.24.2"}, "devDependencies": {"@capacitor/cli": "7.2.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.0.3", "@tailwindcss/vite": "^4.0.6", "@testing-library/dom": ">=7.21.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.4.3", "@types/lodash": "^4.17.20", "@types/node": "22.15.17", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-react": "^4.0.1", "cypress": "^13.5.0", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^22.1.0", "postcss": "^8.5.1", "prettier": "^3.5.3", "tailwindcss": "^4.0.3", "terser": "^5.4.0", "typescript": "5.8.3", "typescript-eslint": "^8.24.0", "vite": "~5.2.0", "vitest": "^0.34.6"}}