#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Script pour dupliquer un dossier de données avec modification des dates
 * Usage: node scripts/duplicate-data.js <date-source> <date-cible>
 * Exemple: node scripts/duplicate-data.js 2025-05-02 2025-05-03
 */

function log(message) {
  console.log(`[DUPLICATE-DATA] ${message}`);
}

function error(message) {
  console.error(`[ERROR] ${message}`);
}

function validateDate(dateString) {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) {
    return false;
  }
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
}

function formatDateForXml(dateString) {
  // Convertit YYYY-MM-DD en YYYYMMDD
  return dateString.replace(/-/g, '');
}

function replaceFileNameDate(fileName, sourceDate, targetDate) {
  // Formats de dates possibles dans les noms de fichiers
  const sourceDateFormats = [
    sourceDate,                      // YYYY-MM-DD
    formatDateForXml(sourceDate),    // YYYYMMDD
  ];
  
  const targetDateFormats = [
    targetDate,                      // YYYY-MM-DD
    formatDateForXml(targetDate),    // YYYYMMDD
  ];
  
  let newFileName = fileName;
  
  // Remplacer chaque format de date source par le format cible correspondant
  for (let i = 0; i < sourceDateFormats.length; i++) {
    const sourceFormat = sourceDateFormats[i];
    const targetFormat = targetDateFormats[i];
    
    if (newFileName.includes(sourceFormat)) {
      newFileName = newFileName.replace(new RegExp(sourceFormat, 'g'), targetFormat);
    }
  }
  
  return newFileName;
}

function modifyXmlContent(xmlContent, sourceDate, targetDate) {
  const sourceDateXml = formatDateForXml(sourceDate); // 20250502
  const targetDateXml = formatDateForXml(targetDate); // 20250503
  
  // Remplacer DATE_LIVR dans le contenu XML
  let modifiedContent = xmlContent.replace(
    new RegExp(`<DATE_LIVR>${sourceDateXml}</DATE_LIVR>`, 'g'),
    `<DATE_LIVR>${targetDateXml}</DATE_LIVR>`
  );
  
  // Remplacer aussi les références de dates dans les noms de fichiers BL_NOM
  modifiedContent = replaceFileNameDate(modifiedContent, sourceDate, targetDate);
  
  return modifiedContent;
}

function duplicateXmlFile(sourceFolderPath, targetFolderPath, xmlFileName, sourceDate, targetDate) {
  const sourceFilePath = path.join(sourceFolderPath, xmlFileName);
  
  // Générer le nouveau nom de fichier XML avec la date cible
  const newXmlFileName = replaceFileNameDate(xmlFileName, sourceDate, targetDate);
  const targetFilePath = path.join(targetFolderPath, newXmlFileName);
  
  // Lire et modifier le contenu XML
  const xmlContent = fs.readFileSync(sourceFilePath, 'utf-8');
  const modifiedXmlContent = modifyXmlContent(xmlContent, sourceDate, targetDate);
  
  // Écrire le fichier XML modifié
  fs.writeFileSync(targetFilePath, modifiedXmlContent, 'utf-8');
  
  log(`XML: ${xmlFileName} -> ${newXmlFileName}`);
}

function duplicatePdfFile(sourceFolderPath, targetFolderPath, pdfFileName, sourceDate, targetDate) {
  const sourceFilePath = path.join(sourceFolderPath, pdfFileName);
  
  // Générer le nouveau nom de fichier PDF avec la date cible
  const newPdfFileName = replaceFileNameDate(pdfFileName, sourceDate, targetDate);
  const targetFilePath = path.join(targetFolderPath, newPdfFileName);
  
  // Copier le fichier PDF (copie binaire)
  fs.copyFileSync(sourceFilePath, targetFilePath);
  
  log(`PDF: ${pdfFileName} -> ${newPdfFileName}`);
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length !== 2) {
    error('Usage: node scripts/duplicate-data.js <date-source> <date-cible>');
    error('Exemple: node scripts/duplicate-data.js 2025-05-02 2025-05-03');
    process.exit(1);
  }
  
  const [sourceDate, targetDate] = args;
  
  // Valider les formats de dates
  if (!validateDate(sourceDate)) {
    error(`Format de date source invalide: ${sourceDate}. Utilisez le format YYYY-MM-DD.`);
    process.exit(1);
  }
  
  if (!validateDate(targetDate)) {
    error(`Format de date cible invalide: ${targetDate}. Utilisez le format YYYY-MM-DD.`);
    process.exit(1);
  }
  
  const dataDir = path.join(__dirname, '../data');
  const sourceFolderPath = path.join(dataDir, sourceDate);
  const targetFolderPath = path.join(dataDir, targetDate);
  
  // Vérifier que le dossier source existe
  if (!fs.existsSync(sourceFolderPath)) {
    error(`Le dossier source n'existe pas: ${sourceFolderPath}`);
    process.exit(1);
  }
  
  // Vérifier que le dossier cible n'existe pas déjà
  if (fs.existsSync(targetFolderPath)) {
    error(`Le dossier cible existe déjà: ${targetFolderPath}`);
    process.exit(1);
  }
  
  log(`Duplication des données de ${sourceDate} vers ${targetDate}`);
  
  try {
    // Créer le répertoire cible
    fs.mkdirSync(targetFolderPath, { recursive: true });
    
    // Lire tous les fichiers du répertoire source
    const files = fs.readdirSync(sourceFolderPath);
    const xmlFiles = files.filter(file => file.endsWith('.xml'));
    const pdfFiles = files.filter(file => file.endsWith('.pdf'));
    
    log(`Trouvé ${xmlFiles.length} fichiers XML et ${pdfFiles.length} fichiers PDF à dupliquer`);
    
    // Traiter les fichiers XML
    for (const xmlFile of xmlFiles) {
      duplicateXmlFile(sourceFolderPath, targetFolderPath, xmlFile, sourceDate, targetDate);
    }
    
    // Traiter les fichiers PDF
    for (const pdfFile of pdfFiles) {
      duplicatePdfFile(sourceFolderPath, targetFolderPath, pdfFile, sourceDate, targetDate);
    }
    
    log(`Duplication réussie de ${xmlFiles.length + pdfFiles.length} fichiers vers ${targetDate}`);
    
  } catch (error) {
    error(`Erreur pendant la duplication: ${error.message}`);
    
    // Nettoyer le dossier cible en cas d'erreur
    if (fs.existsSync(targetFolderPath)) {
      fs.rmSync(targetFolderPath, { recursive: true, force: true });
      log('Dossier cible nettoyé après erreur');
    }
    
    process.exit(1);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  main();
}

module.exports = {
  replaceFileNameDate,
  modifyXmlContent,
  validateDate,
  formatDateForXml
};