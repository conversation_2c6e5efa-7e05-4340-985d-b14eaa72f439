# Scripts de développement

## duplicate-data.js

Script Node.js standalone pour dupliquer un dossier de données avec modification automatique des dates dans les fichiers XML et les noms de fichiers.

### Utilisation

```bash
# Depuis le dossier backend
node scripts/duplicate-data.js <date-source> <date-cible>

# Ou avec npm script
pnpm duplicate-data <date-source> <date-cible>
```

### Exemples

```bash
# Dupliquer les données du 2025-05-02 vers le 2025-05-03
node scripts/duplicate-data.js 2025-05-02 2025-05-03

# Avec le script npm
pnpm duplicate-data 2025-05-02 2025-05-03
```

### Fonctionnalités

Le script effectue les opérations suivantes :

1. **Validation des dates** : Vérifie que les dates sont au format YYYY-MM-DD et sont valides
2. **Vérification des dossiers** :
   - Le dossier source doit exister
   - Le dossier cible ne doit pas exister
3. **Duplication des fichiers XML** :
   - Copie et renomme les fichiers XML
   - Modifie le contenu XML pour changer `<DATE_LIVR>` 
   - Met à jour les références aux fichiers PDF dans les balises `<BL_NOM>`
4. **Duplication des fichiers PDF** :
   - Copie et renomme les fichiers PDF
   - Remplace les dates dans les noms de fichiers

### Exemples de transformations

#### Fichiers XML

- `Tour_4012_DateLivr_20250502.xml` → `Tour_4012_DateLivr_20250503.xml`
- Contenu : `<DATE_LIVR>20250502</DATE_LIVR>` → `<DATE_LIVR>20250503</DATE_LIVR>`
- Contenu : `<BL_NOM>BL_4012_20250502_xxx.pdf</BL_NOM>` → `<BL_NOM>BL_4012_20250503_xxx.pdf</BL_NOM>`

#### Fichiers PDF

- `BL_4012_20250502_0004876078.pdf` → `BL_4012_20250503_0004876078.pdf`

### Gestion d'erreurs

- **Dates invalides** : Validation du format YYYY-MM-DD
- **Dossier source inexistant** : Arrêt avec message d'erreur
- **Dossier cible existant** : Arrêt pour éviter l'écrasement
- **Erreur pendant la copie** : Nettoyage automatique du dossier cible partiellement créé

### Structure attendue

Le script attend une structure de dossiers comme suit :

```
backend/
├── data/
│   ├── 2025-05-02/           # Dossier source
│   │   ├── Tour_*.xml
│   │   └── BL_*.pdf
│   └── 2025-05-03/           # Dossier cible (créé par le script)
│       ├── Tour_*.xml        # Fichiers XML modifiés
│       └── BL_*.pdf          # Fichiers PDF copiés
└── scripts/
    └── duplicate-data.js
```

### Cas d'usage

Ce script est particulièrement utile pour :

- **Tests de développement** : Créer des jeux de données pour différentes dates
- **Démonstrations** : Simuler des livraisons sur plusieurs jours
- **Tests de régression** : Reproduire des scénarios avec des dates spécifiques
- **Migration de données** : Dupliquer des données existantes pour de nouvelles dates

### Limitations

- Les dates doivent être au format YYYY-MM-DD
- Seuls les fichiers .xml et .pdf sont traités
- Le script ne gère pas les sous-dossiers
- La modification XML est basée sur des patterns simples (pas de parsing XML complet)