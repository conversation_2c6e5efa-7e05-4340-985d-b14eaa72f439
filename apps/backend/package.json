{"name": "@lrg/backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"./src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "tidy": "pnpm format && pnpm lint", "typecheck": "tsc --noEmit", "test": "jest --config ./jest.config.js", "test:watch": "jest --watch --config ./jest.config.js", "test:cov": "jest --coverage --config ./jest.config.js", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand --config ./jest.config.js", "test:e2e": "jest --config ./test/jest-e2e.json", "command-entrypoint-prod": "node dist/command-entrypoint.js", "command-entrypoint-dev": "ts-node src/command-entrypoint.ts", "duplicate-data": "node scripts/duplicate-data.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.797.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^2.0.3", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@s3pweb/keycloak-admin-client-cjs": "^26.1.4", "axios": "^1.8.4", "basic-ftp": "^5.0.5", "cache-manager": "^6.4.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotenv": "16.5.0", "handlebars": "^4.7.8", "inquirer": "8.2.6", "keycloak-connect": "^26.1.1", "lodash": "4.17.21", "luxon": "^3.6.1", "nanoid": "^3.3.11", "nest-commander": "^3.17.0", "nest-keycloak-connect": "^1.10.1", "nodemailer": "^7.0.5", "pg": "^8.14.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ssh2-sftp-client": "^12.0.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22", "typeorm-naming-strategies": "^4.1.0", "typeorm-transactional": "^0.5.0", "wkx": "^0.5.0", "xml2js": "^0.6.2", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/inquirer": "^9.0.8", "@types/jest": "^29.5.14", "@types/luxon": "^3.6.2", "@types/multer": "^1.4.13", "@types/node": "22.15.17", "@types/nodemailer": "^6.4.17", "@types/ssh2-sftp-client": "^9.0.4", "@types/supertest": "^6.0.2", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "express-basic-auth": "^1.2.1", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.8.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}