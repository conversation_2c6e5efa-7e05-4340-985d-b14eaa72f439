
## Backend Architectural Notes

- sur le backend préfère injecter les repository en passant par les services d'infra

### Controller Pattern

Controllers must follow this pattern:

```typescript
@ApiTags('manager/resource-name')
@Controller('manager/resource-name')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class ResourceManagerController {
  // Use @Roles from nest-keycloak-connect, NOT @UseGuards
  // Always include ApiTags and Swagger documentation
  // Use ParseUUIDPipe to validate UUIDs
  // Return entities directly (ClassSerializerInterceptor handles serialization)
}
```

### Repository Pattern

Repositories must extend TypeORM Repository class directly:

```typescript
@Injectable()
export class EntityRepository extends Repository<Entity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(Entity, entityManager || dataSource.createEntityManager());
  }
}
```

- No custom methods in repository - use TypeORM methods directly in services
- Repository only handles data access, business logic goes in services


