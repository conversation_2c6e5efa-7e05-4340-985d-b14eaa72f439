#  database connection
DB_CONNECTION_ADDRESS=localhost
DB_CONNECTION_LOGIN=postgres
DB_CONNECTION_PASSWORD=postgres
DB_CONNECTION_DATABASE=lrg_db
DB_CONNECTION_PORT=8432


KEYCLOAK_SERVER_URL=http://localhost:11080
KEYCLOAK_REALM=lrg
KEYCLOAK_CLIENT_ID=lrg-back
KE<PERSON>CLOAK_CLIENT_SECRET=sWMcVR5rXQxn8zrPAFxDUhTJGlOlrAtg


AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=your-bucket
# Optionnel pour Minio/self-hosted S3
AWS_S3_ENDPOINT=http://localhost:9000
AWS_S3_FORCE_PATH_STYLE=true

# SFTP import configuration
SFTP_HOST=sftp.example.com
SFTP_PORT=22
SFTP_USER=sftpuser
SFTP_PASSWORD=sftppassword
# Optionnel pour authentification par clé privée
SFTP_PRIVATE_KEY_PATH=/path/to/private/key
SFTP_PASSPHRASE=private-key-passphrase
# Racine des imports sur le serveur SFTP
SFTP_IMPORT_ROOT_PATH=/

# SMTP Email configuration
SMTP_HOST=portainer.artdigit.fr
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER=aaaa
SMTP_PASS=bbbb
SMTP_FROM=LRG Livraisons <<EMAIL>>

# Application configuration
PORT=3000


HTML_TO_PDF_API_URL=http://portainer.artdigit.fr:61222
HTML_TO_PDF_CREDENTIALS=SYMunj2AEtFUvfD4rQ:npVfwgzCt4PRLckj3S2rYxFXEev59

SWAGGER_USER=admin
SWAGGER_PASSWORD=admin