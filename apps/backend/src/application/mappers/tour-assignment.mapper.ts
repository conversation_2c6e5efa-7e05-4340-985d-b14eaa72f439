import { Injectable, NotFoundException } from '@nestjs/common';
import { TourAssignmentEntity } from '../../domain/entity/tour-assignment.entity';
import { TourIdentifier } from '../../domain/entity/tour-identifier';
import { UserRepository } from '../../infrastructure/repository/user.repository';
import { CreateTourAssignmentDto, UpdateTourAssignmentDto } from '../dto/tour-assignment/tour-assignment.dto';

@Injectable()
export class TourAssignmentMapper {
  constructor(private readonly userRepository: UserRepository) {}

  async toEntity(
    dto: CreateTourAssignmentDto | UpdateTourAssignmentDto,
    existingEntity?: TourAssignmentEntity,
  ): Promise<TourAssignmentEntity> {
    const entity = existingEntity || new TourAssignmentEntity();

    // Only set tourId on create
    if ('tourIdentifier' in dto && dto.tourIdentifier && !existingEntity) {
      entity.tourIdentifier = TourIdentifier.fromDto(dto.tourIdentifier);
    }

    if ('fromDate' in dto && dto.fromDate !== undefined) {
      entity.fromDate = dto.fromDate;
    }

    if ('toDate' in dto) {
      entity.toDate = dto.toDate === null ? null : dto.toDate;
    }

    if ('notes' in dto) {
      entity.notes = dto.notes;
    }

    // Set userId on create or update
    if ('userId' in dto && dto.userId) {
      const user = await this.userRepository.findOneBy({ id: dto.userId });

      if (!user) {
        throw new NotFoundException(`Cannot map tour assignment, user with id ${dto.userId} not found`);
      }

      entity.userId = user.id;
      entity.user = user;
    }

    return entity;
  }
}
