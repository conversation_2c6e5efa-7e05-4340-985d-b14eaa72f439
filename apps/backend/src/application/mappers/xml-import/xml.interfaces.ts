export interface XmlTourDocument {
  Document: {
    TOURNEE: string[];
    DATE_LIVR: string[];
    ARRETS: XmlArretWrapper[];
  };
}

export interface XmlArretWrapper {
  ARRET: XmlArret[];
}

export interface XmlArret {
  CodeTriArret: string[];
  HeuresLivraison: string[];
  ClientCode: string[];
  ClientNom: string[];
  ClientADR1: string[];
  ClientADR2: string[];
  ClientADR3: string[];
  ClientADR4: string[];
  ClientMAIL: string[];
  LIGNES_EXPEDITEURS: XmlLignesExpediteursWrapper[];
  BL_NOMS: XmlBlNomsWrapper[];
}

export interface XmlLignesExpediteursWrapper {
  LIGNE_EXPEDITEUR: XmlLigneExpediteur[];
}

export interface XmlLigneExpediteur {
  Operation: string[];
  Surgele: string[]; // "0" or "1"
  Expediteur: string[];
  Poids?: string[];
  NbPalettes?: string[];
  NbRolls?: string[];
  NbColis?: string[];
  Montant?: string[];
}

export interface XmlBlNomsWrapper {
  BL_NOM: string[];
}
