import { Injectable } from '@nestjs/common';
import { createHash } from 'crypto';
import { DateTime } from 'luxon';
import { parseStringPromise as parseXmlString } from 'xml2js';
import { z } from 'zod';
import { Address } from '../../../domain/entity/address';
import { ClientEntity } from '../../../domain/entity/client.entity';
import { DeliveryNoteEntity } from '../../../domain/entity/delivery-note.entity';
import { ShipmentLineEntity } from '../../../domain/entity/shipment-line.entity';
import { OriginalClientInfo, StopEntity } from '../../../domain/entity/stop.entity';
import { TourIdentifier } from '../../../domain/entity/tour-identifier';
import { TourEntity } from '../../../domain/entity/tour.entity';
import { ImportSourceType } from '../../../domain/enum/import-source-type.enum';
import { ShipmentOperationType } from '../../../domain/enum/shipment-line.enums';
import { TourStatus } from '../../../domain/enum/tour.enums';
import { ClientRepository } from '../../../infrastructure/repository/client.repository';
import { DeliveryNoteRepository } from '../../../infrastructure/repository/delivery-note.repository';
import { ShipmentLineRepository } from '../../../infrastructure/repository/shipment-line.repository';
import { StopRepository } from '../../../infrastructure/repository/stop.repository';
import { TourRepository } from '../../../infrastructure/repository/tour.repository';
import { DeliveryNoteFileService } from '../../service/delivery-note-file.service';
import { SkipTourImportException } from './skip-tour-import.exception';
import { XMLMapperUtils } from './utils';
import { XmlArret, XmlLigneExpediteur, XmlTourDocument } from './xml.interfaces';

@Injectable()
export class XmlTourImportService {
  constructor(
    private readonly tourRepository: TourRepository,
    private readonly stopRepository: StopRepository,
    private readonly shipmentLineRepository: ShipmentLineRepository,
    private readonly deliveryNoteRepository: DeliveryNoteRepository,
    private readonly clientRepository: ClientRepository,
    private readonly deliveryNoteFileService: DeliveryNoteFileService,
  ) {}

  async importTour(
    xmlFileContent: string,
    fileName: string,
    importContext?: {
      importDate: string;
      importSourceType: ImportSourceType;
      importEventEntityId: string;
    },
  ): Promise<TourEntity> {
    const parsedXml = await this.parseXml(xmlFileContent);
    const fileHash = await this.computeFileHash(xmlFileContent);

    const tour = await this.importTourIfNotExists(parsedXml, fileName, fileHash, importContext);

    if (parsedXml.Document.ARRETS && parsedXml.Document.ARRETS.length > 0 && parsedXml.Document.ARRETS[0].ARRET) {
      for (const [index, arret] of parsedXml.Document.ARRETS[0].ARRET.entries()) {
        await this.importStopIfNotExists(arret, tour, index, importContext);
      }
    }

    return tour;
  }

  private async computeFileHash(fileContent: string): Promise<string> {
    return createHash('sha256').update(fileContent).digest('hex');
  }

  private async importTourIfNotExists(
    parsedXml: XmlTourDocument,
    fileName: string,
    fileHash: string,
    importContext?: {
      importDate: string;
      importSourceType: ImportSourceType;
      importEventEntityId: string;
    },
  ): Promise<TourEntity> {
    const tourIdentifier = this.parseTourIdentifierFromTourNumber(
      XMLMapperUtils.safeGetString(parsedXml.Document.TOURNEE),
    );
    const deliveryDate = this.parseDateFromXmlString(XMLMapperUtils.safeGetString(parsedXml.Document.DATE_LIVR));

    const existingTour = await this.tourRepository.findByTourIdentifierForDate(tourIdentifier, deliveryDate);

    if (existingTour) {
      if (existingTour.status !== TourStatus.Planned) {
        throw new SkipTourImportException('Tour already exists and is not planned, so we cannot import it again');
      }

      if (existingTour.providerFileHash === fileHash) {
        throw new SkipTourImportException(
          'Tour already exists and has the same file hash, so we cannot import it again',
        );
      }
    }

    let tour = existingTour ?? new TourEntity();

    tour.tourIdentifier = tourIdentifier;
    tour.deliveryDate = deliveryDate;

    tour.providerFileName = fileName;
    tour.providerFileHash = fileHash;
    tour.status = TourStatus.Planned;
    tour.importBatchId = importContext?.importEventEntityId;

    tour = await this.tourRepository.save(tour);

    return tour;
  }

  private async importStopIfNotExists(
    arret: XmlArret,
    tour: TourEntity,
    index: number,
    importContext?: { importDate: string; importSourceType: ImportSourceType },
  ): Promise<StopEntity> {
    let stop: StopEntity =
      (await this.stopRepository.findOne({
        where: {
          tourId: tour.id,
          sequenceInTour: index,
        },
      })) ?? new StopEntity();

    stop.tour = tour;
    stop.tourId = tour.id;
    stop.sequenceInTour = index;
    stop.deliveryTimeWindow = XMLMapperUtils.safeGetString(arret.HeuresLivraison);
    stop.sortingCode = XMLMapperUtils.safeGetString(arret.CodeTriArret);

    stop.originalClientInfo = new OriginalClientInfo();
    stop.originalClientInfo.code = XMLMapperUtils.safeGetString(arret.ClientCode);
    stop.originalClientInfo.name = XMLMapperUtils.safeGetString(arret.ClientNom);
    stop.originalClientInfo.email = XMLMapperUtils.safeGetString(arret.ClientMAIL);
    stop.client = await this.importClientIfNotExists(stop.originalClientInfo);

    const address = new Address();
    address.line1 = XMLMapperUtils.safeGetString(arret.ClientADR1);
    address.line2 = XMLMapperUtils.safeGetString(arret.ClientADR2);
    address.line3 = XMLMapperUtils.safeGetString(arret.ClientADR3);
    address.line4 = XMLMapperUtils.safeGetString(arret.ClientADR4);
    stop.originalClientInfo.address = address;

    stop = await this.stopRepository.save(stop);

    if (
      arret.LIGNES_EXPEDITEURS &&
      arret.LIGNES_EXPEDITEURS.length > 0 &&
      arret.LIGNES_EXPEDITEURS[0].LIGNE_EXPEDITEUR
    ) {
      for (const [index, shipmentLine] of arret.LIGNES_EXPEDITEURS[0].LIGNE_EXPEDITEUR.entries()) {
        await this.importShipmentLineIfNotExists(shipmentLine, stop, index);
      }
    }

    if (arret.BL_NOMS && arret.BL_NOMS.length > 0 && arret.BL_NOMS[0].BL_NOM) {
      for (const [index, blNom] of arret.BL_NOMS[0].BL_NOM.entries()) {
        await this.importDeliveryNoteIfNotExists(blNom, stop, index, importContext);
      }
    }

    return stop;
  }

  private async importClientIfNotExists(originalClientInfo: OriginalClientInfo): Promise<ClientEntity> {
    if (!originalClientInfo.code || originalClientInfo.code === '') {
      return null;
    }

    const existingClient = await this.clientRepository.findOne({
      where: { code: originalClientInfo.code },
    });

    if (existingClient) {
      return existingClient;
    }

    const client = new ClientEntity();

    client.code = originalClientInfo.code;
    client.name = originalClientInfo.name;
    client.address = originalClientInfo.address;
    client.email = originalClientInfo.email;

    return this.clientRepository.save(client);
  }

  private async importDeliveryNoteIfNotExists(
    blNom: string,
    stop: StopEntity,
    index: number,
    importContext?: { importDate: string; importSourceType: ImportSourceType },
  ): Promise<DeliveryNoteEntity> {
    let deliveryNoteEntity: DeliveryNoteEntity =
      (await this.deliveryNoteRepository.findOne({
        where: {
          filename: blNom,
        },
      })) ?? new DeliveryNoteEntity();

    deliveryNoteEntity.stop = stop;
    deliveryNoteEntity.stopId = stop.id;
    deliveryNoteEntity.sequenceInStop = index;
    deliveryNoteEntity.filename = blNom;

    deliveryNoteEntity = await this.deliveryNoteRepository.save(deliveryNoteEntity);

    // Upload PDF file if context is provided and file doesn't exist
    if (importContext && !deliveryNoteEntity.fileId) {
      const fileEntity = await this.deliveryNoteFileService.uploadDeliveryNotePdf(blNom, importContext);

      if (fileEntity) {
        deliveryNoteEntity.fileId = fileEntity.id;
        deliveryNoteEntity.file = fileEntity;
        await this.deliveryNoteRepository.save(deliveryNoteEntity);
      }
    }

    return deliveryNoteEntity;
  }

  private async importShipmentLineIfNotExists(
    shipmentLine: XmlLigneExpediteur,
    stop: StopEntity,
    index: number,
  ): Promise<ShipmentLineEntity> {
    let shipmentLineEntity: ShipmentLineEntity =
      (await this.shipmentLineRepository.findOne({
        where: {
          stopId: stop.id,
          sequenceInStop: index,
        },
      })) ?? new ShipmentLineEntity();

    shipmentLineEntity.stop = stop;
    shipmentLineEntity.stopId = stop.id;
    shipmentLineEntity.sequenceInStop = index;

    switch (XMLMapperUtils.safeGetString(shipmentLine.Operation)) {
      case 'Livrer pour':
        shipmentLineEntity.operation = ShipmentOperationType.Unload;
        break;
      case 'Charger pour':
        shipmentLineEntity.operation = ShipmentOperationType.Load;
        break;
      default:
        throw new Error(`Unknown operation: ${XMLMapperUtils.safeGetString(shipmentLine.Operation)}`);
    }

    shipmentLineEntity.isFrozen = XMLMapperUtils.safeGetString(shipmentLine.Surgele) === '1';
    shipmentLineEntity.shipperName = XMLMapperUtils.safeGetString(shipmentLine.Expediteur);
    shipmentLineEntity.weightKg = XMLMapperUtils.safeGetInteger(shipmentLine.Poids);
    shipmentLineEntity.palletCount = XMLMapperUtils.safeGetInteger(shipmentLine.NbPalettes);
    shipmentLineEntity.rollCount = XMLMapperUtils.safeGetInteger(shipmentLine.NbRolls);
    shipmentLineEntity.packageCount = XMLMapperUtils.safeGetInteger(shipmentLine.NbColis);
    shipmentLineEntity.amount = XMLMapperUtils.safeGetInteger(shipmentLine.Montant);

    shipmentLineEntity = await this.shipmentLineRepository.save(shipmentLineEntity);

    return shipmentLineEntity;
  }

  private parseTourIdentifierFromTourNumber(rawXmlTourNumber: string): TourIdentifier {
    const xmlTourNumber = z.coerce.string().min(1).parse(rawXmlTourNumber);

    const tourIdentifier = TourIdentifier.fromOriginalNumber(xmlTourNumber);

    return tourIdentifier;
  }

  /** xml Date is in format AAAAMMDD need to validate and convert to YYYY-MM-DD */
  private parseDateFromXmlString(rawXmlDate: string): string {
    const xmlDate = z.coerce.string().length(8).parse(rawXmlDate);
    const date = DateTime.fromFormat(xmlDate, 'yyyyMMdd');

    if (!date.isValid) {
      throw new Error('Invalid date format');
    }

    return date.toFormat('yyyy-MM-dd');
  }

  private async parseXml(xmlFileContent: string): Promise<XmlTourDocument> {
    const sanitizedXmlFileContent = XMLMapperUtils.sanitizeXmlString(xmlFileContent);

    const parsedXml = (await parseXmlString(sanitizedXmlFileContent, {
      explicitArray: true,
      trim: true,
      charkey: '_',
      // Potentially add other xml2js options here if needed, e.g. for entities
    })) as XmlTourDocument;

    return parsedXml;
  }
}
