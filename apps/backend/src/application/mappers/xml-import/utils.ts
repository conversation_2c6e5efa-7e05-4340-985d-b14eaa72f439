export class XMLMapperUtils {
  static safeGetString(arr: string[] | undefined): string | undefined {
    const value = arr && arr.length > 0 ? arr[0]?.trim() : undefined;

    if (!value || value === '') {
      return undefined;
    }

    return value;
  }

  static safeGetInteger(arr: string[] | undefined): number | undefined {
    const value = this.safeGetString(arr);

    if (!value || value === '') {
      return undefined;
    }

    return parseInt(value);
  }

  static sanitizeXmlString(str: string): string {
    return str.replace(/&(?!(?:amp|lt|gt|quot|apos);)/g, '&amp;');
  }
}
