import { IsString, <PERSON>NotEmpty, <PERSON>Optional, Matches } from 'class-validator';

export class FileUploadDto {
  @IsString()
  @IsNotEmpty()
  base64: string;

  @IsString()
  @IsNotEmpty()
  filename: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^image\/|^application\/pdf$/, {
    message: 'MIME type must be an image or PDF',
  })
  mimeType: string;

  @IsOptional()
  metadata?: Record<string, unknown>;
}
