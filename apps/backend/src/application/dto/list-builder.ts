import { plainToInstance } from 'class-transformer';
import { PaginatedResponseDto, PaginationMetaDto } from './pagination.dto';

/**
 * ListBuilder permet de construire une réponse paginée conforme aux DTO de pagination du projet.
 * Utilisation fluide :
 *
 *   const paginated = new ListBuilder(Entity, EntityDto)
 *     .items(arrayOfEntities)
 *     .meta({ page, limit, totalItems, ... })
 *     .build();
 *
 * - Le premier paramètre du constructeur est la classe DTO cible pour la sérialisation des items.
 * - .items() prend le tableau d'entités à transformer.
 * - .meta() prend la pagination (page, limit, totalItems, totalPages, hasNextPage, hasPreviousPage).
 * - .build() retourne un PaginatedResponseDto<DTO> prêt à être retourné par un contrôleur.
 */
export class ListBuilder<T, DTO> {
  private _items: T[] = [];
  private _dtoClass: new (...args: unknown[]) => DTO;
  private _meta: PaginationMetaDto;

  constructor(dtoClass: new (...args: unknown[]) => DTO) {
    this._dtoClass = dtoClass;
    this._meta = {
      page: 1,
      limit: 10,
      totalItems: 0,
      totalPages: 1,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  items(items: T[]): this {
    this._items = items;

    return this;
  }

  meta(meta: Partial<PaginationMetaDto>): this {
    this._meta = { ...this._meta, ...meta };

    return this;
  }

  build(): PaginatedResponseDto<DTO> {
    const items = plainToInstance(this._dtoClass, this._items, {
      excludeExtraneousValues: true,
    });

    return plainToInstance(
      PaginatedResponseDto,
      {
        items,
        meta: this._meta,
      },
      { excludeExtraneousValues: true },
    ) as PaginatedResponseDto<DTO>;
  }
}
