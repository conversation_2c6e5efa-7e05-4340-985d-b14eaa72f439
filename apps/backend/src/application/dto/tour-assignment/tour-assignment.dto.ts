import { Type } from 'class-transformer';
import { IsDateString, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { TourIdentifierDto } from '../tour-identifier.dto';

export class CreateTourAssignmentDto {
  @ValidateNested()
  @Type(() => TourIdentifierDto)
  tourIdentifier: TourIdentifierDto;

  @IsDateString()
  fromDate: string;

  @IsOptional()
  @IsDateString()
  toDate?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsUUID()
  userId: string;
}

export class UpdateTourAssignmentDto {
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @IsOptional()
  @IsDateString()
  toDate?: string | null; // null pour supprimer la date de fin

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsUUID()
  userId?: string;
}

export class TourAssignmentParams {
  @IsUUID()
  id: string;
}
