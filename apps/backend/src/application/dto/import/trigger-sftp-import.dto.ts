import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty } from 'class-validator';

export class TriggerSftpImportDto {
  @ApiProperty({
    description: 'Date for which to import SFTP files',
    example: '2024-12-01',
    pattern: 'YYYY-MM-DD',
  })
  @IsDateString({}, { message: 'Date must be in YYYY-MM-DD format' })
  @IsNotEmpty({ message: 'Date is required' })
  date: string;
}
