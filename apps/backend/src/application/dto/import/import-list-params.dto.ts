import { Expose } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ImportStatus } from '../../../domain/enum/import-status.enum';
import { QueryParamsDto } from '../pagination.dto';

export class ImportListParamsDto extends QueryParamsDto {
  @IsOptional()
  @IsEnum(ImportStatus)
  @Expose()
  status?: ImportStatus;

  @IsOptional()
  @IsString()
  @Expose()
  startDate?: string;

  @IsOptional()
  @IsString()
  @Expose()
  endDate?: string;
}
