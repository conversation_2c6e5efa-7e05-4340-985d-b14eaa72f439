import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { EquipmentCountDto } from '../stop/complete-stop-delivery.dto';
import { LogisticsEquipmentDetailDto } from '../stop/logistics-equipment-detail.dto';

export class ControlTourEquipmentDto {
  @ApiProperty({
    description: 'Equipment count summary for the tour control',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EquipmentCountDto)
  equipmentCount?: EquipmentCountDto;

  @ApiProperty({
    description: 'Detailed list of equipment controlled on the tour',
    type: [LogisticsEquipmentDetailDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LogisticsEquipmentDetailDto)
  equipmentDetails?: LogisticsEquipmentDetailDto[];
}
