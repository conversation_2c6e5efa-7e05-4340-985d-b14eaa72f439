import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IncidentType } from '../../../domain/entity/incident-type.entity';
import { DeliveryCompletionType } from '../../../domain/entity/stop-completion.entity';

/**
 * DTO pour représenter les incidents des stops d'une tournée spécifique
 *
 * Cette classe DTO regroupe les informations sur les incidents qui se sont produits
 * durant les arrêts (stops) d'une tournée. Les incidents ne sont pas des entités
 * standalone mais sont liés aux completions des stops.
 *
 * Utilisé par l'endpoint GET /manager/tours/:id/incidents pour retourner
 * tous les incidents survenus dans une tournée donnée.
 */
export class TourIncidentDto {
  @ApiProperty({ description: "ID du stop où l'incident s'est produit" })
  @Expose()
  stopId: string;

  @ApiProperty({ description: 'Numéro de séquence du stop dans la tournée' })
  @Expose()
  stopSequence: number;

  @ApiProperty({ description: 'Nom du client' })
  @Expose()
  clientName: string;

  @ApiProperty({ description: 'Code du client' })
  @Expose()
  clientCode: string;

  @ApiProperty({ description: "Type d'incident" })
  @Expose()
  incidentType: IncidentType;

  @ApiProperty({
    description: "Type de completion de livraison suite à l'incident",
    enum: DeliveryCompletionType,
  })
  @Expose()
  deliveryCompletionType: DeliveryCompletionType | null;

  @ApiProperty({ description: "Commentaires sur l'incident", nullable: true })
  @Expose()
  comments: string | null;

  @ApiProperty({
    description: 'Date et heure de completion du stop',
    nullable: true,
  })
  @Expose()
  completedAt: Date | null;
}
