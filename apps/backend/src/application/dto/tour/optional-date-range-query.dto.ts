import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsDateString, IsOptional } from 'class-validator';

export class OptionalDateRangeQueryDto {
  @ApiProperty({
    description: 'Date de début de la plage (format ISO date: YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsDateString()
  @Expose()
  startDate?: string;

  @ApiProperty({
    description: 'Date de fin de la plage (format ISO date: YYYY-MM-DD)',
    example: '2024-01-31',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsDateString()
  @Expose()
  endDate?: string;
}
