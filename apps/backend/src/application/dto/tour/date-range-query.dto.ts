import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsDateString, IsNotEmpty } from 'class-validator';

export class DateRangeQueryDto {
  @ApiProperty({
    description: 'Date de début de la plage (format ISO date: YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
  })
  @IsNotEmpty()
  @IsDateString()
  @Expose()
  startDate: string;

  @ApiProperty({
    description: 'Date de fin de la plage (format ISO date: YYYY-MM-DD)',
    example: '2024-01-31',
    type: String,
  })
  @IsNotEmpty()
  @IsDateString()
  @Expose()
  endDate: string;
}
