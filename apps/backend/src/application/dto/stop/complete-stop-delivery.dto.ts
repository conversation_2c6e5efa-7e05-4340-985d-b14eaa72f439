import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { DeliveryCompletionType } from '../../../domain/entity/stop-completion.entity';
import { FileUploadDto } from '../file/file-upload.dto';
import { LogisticsEquipmentDetailDto } from './logistics-equipment-detail.dto';

export class EquipmentCountDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  palletCount?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  rollCount?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  packageCount?: number;
}

export class CompleteStopDeliveryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => FileUploadDto)
  signatureFile?: FileUploadDto;

  @IsOptional()
  @IsString()
  signatureFirstName?: string;

  @IsOptional()
  @IsString()
  signatureLastName?: string;

  @IsOptional()
  @IsString()
  signatureEmail?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileUploadDto)
  @ApiProperty({
    description: 'Array of proof photos for the delivery',
    type: [FileUploadDto],
    required: false,
  })
  photoFiles?: FileUploadDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => EquipmentCountDto)
  returnedEquipment?: EquipmentCountDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => EquipmentCountDto)
  unloadedEquipment?: EquipmentCountDto;

  @ApiProperty({
    description: 'Detailed returned equipment by specific type',
    type: [LogisticsEquipmentDetailDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LogisticsEquipmentDetailDto)
  returnedEquipmentDetails?: LogisticsEquipmentDetailDto[];

  @ApiProperty({
    description: 'Detailed unloaded equipment by specific type',
    type: [LogisticsEquipmentDetailDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LogisticsEquipmentDetailDto)
  unloadedEquipmentDetails?: LogisticsEquipmentDetailDto[];

  // Incident
  @IsOptional()
  @IsUUID()
  incidentTypeId?: string;

  @IsOptional()
  @IsEnum(DeliveryCompletionType)
  @ValidateIf((o) => o.incidentTypeId !== undefined)
  deliveryCompletionType?: DeliveryCompletionType;

  @IsOptional()
  @IsString()
  comments?: string;

  // Location data
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @IsOptional()
  @IsNumber()
  longitude?: number;

  @IsOptional()
  @IsNumber()
  precision?: number;

  @IsOptional()
  @IsBoolean()
  isSecureLocation?: boolean;
}
