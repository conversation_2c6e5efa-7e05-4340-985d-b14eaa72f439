import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsPositive, IsUUID } from 'class-validator';

export class LogisticsEquipmentDetailDto {
  @ApiProperty({
    description: 'The ID of the logistics equipment type',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  logisticsEquipmentTypeId: string;

  @ApiProperty({
    description: 'The quantity of this equipment type',
    example: 5,
  })
  @IsInt()
  @IsPositive()
  quantity: number;
}
