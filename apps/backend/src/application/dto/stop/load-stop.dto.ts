import { Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';
import { EquipmentCountDto } from './complete-stop-delivery.dto';
import { LogisticsEquipmentDetailDto } from './logistics-equipment-detail.dto';

/**
 * Dto pour charger un arrêt, le même que pour effectuer un préchargement
 */
export class LoadStopDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => EquipmentCountDto)
  equipmentCount?: EquipmentCountDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LogisticsEquipmentDetailDto)
  equipmentDetails?: LogisticsEquipmentDetailDto[];
}

export class PreloadStopDto extends LoadStopDto {}
