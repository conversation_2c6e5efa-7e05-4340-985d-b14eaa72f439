import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

export class CreateVehicleDto {
  @ApiProperty({
    description: "Plaque d'immatriculation du véhicule",
    example: 'AB-123-CD',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  licensePlate: string;

  @ApiProperty({
    description: 'Modèle du véhicule',
    example: 'Renault Master',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  model?: string;

  @ApiProperty({
    description: "Numéro d'assurance du véhicule",
    example: 'ASS123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  insuranceNumber?: string;

  @ApiProperty({
    description: 'Date de fin de validité du contrôle technique',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  technicalControlExpiryDate?: string;
}
