import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, MinLength } from 'class-validator';

export class ResetPasswordRequest {
  @ApiProperty({
    description: 'New password for the user',
    example: 'NewSecurePassword123!',
    minLength: 8,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({
    description: 'Whether the password is temporary and must be changed on next login',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  temporary?: boolean = false;
}
