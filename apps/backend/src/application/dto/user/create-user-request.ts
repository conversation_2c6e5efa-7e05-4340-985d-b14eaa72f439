import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  MinLength,
  ValidateIf,
} from 'class-validator';
import { UserRole } from '../../../domain/enum/user-role.enum';

export class CreateUserRequest {
  @ApiProperty({
    description: 'Username for the user',
    example: 'john.doe',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  username: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    maxLength: 255,
  })
  @IsEmail()
  @MaxLength(255)
  email: string;

  @ApiPropertyOptional({
    description: 'First name of the user',
    example: 'John',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  firstName?: string;

  @ApiPropertyOptional({
    description: 'Last name of the user',
    example: 'Doe',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  lastName?: string;

  @ApiPropertyOptional({
    description: 'User locale preference',
    example: 'fr',
    maxLength: 5,
    default: 'fr',
  })
  @IsOptional()
  @IsString()
  @MaxLength(5)
  locale?: string = 'fr';

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.Manager,
  })
  @IsEnum(UserRole)
  role: UserRole;

  @ApiPropertyOptional({
    description: 'User color in hex format',
    example: '#FF5733',
    pattern: '^#[0-9A-Fa-f]{6}$',
  })
  @IsOptional()
  @IsString()
  @Matches(/^#[0-9A-Fa-f]{6}$/, {
    message: 'Color must be a valid hex color format (#RRGGBB)',
  })
  color?: string;

  @ApiPropertyOptional({
    description: 'Whether to create the user in Keycloak. Set to false for external SSO users.',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  createInKeycloak?: boolean = true;

  @ApiPropertyOptional({
    description: 'Password for the user. Required if createInKeycloak is true.',
    example: 'SecurePassword123!',
    minLength: 8,
  })
  @ValidateIf((o) => o.createInKeycloak === true)
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  password?: string;
}
