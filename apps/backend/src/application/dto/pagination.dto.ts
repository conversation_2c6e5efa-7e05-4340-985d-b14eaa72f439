import { Expose, Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional, IsPositive, IsString } from 'class-validator';

export class QueryParamsDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @IsPositive()
  @Expose()
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @IsPositive()
  @Expose()
  limit?: number = 10;

  @IsOptional()
  @IsString()
  @Expose()
  sortBy?: string;

  @IsOptional()
  @IsIn(['asc', 'desc'])
  @Expose()
  sortOrder?: 'asc' | 'desc' = 'asc';

  @IsOptional()
  @IsString()
  @Expose()
  search?: string; // Terme de recherche global

  toTypeOrmOptions(): {
    skip: number;
    take: number;
    order?: Record<string, 'ASC' | 'DESC'>;
  } {
    const skip = ((this.page ?? 1) - 1) * (this.limit ?? 10);
    const take = this.limit ?? 10;
    let order: Record<string, 'ASC' | 'DESC'> | undefined = undefined;

    if (this.sortBy) {
      order = {
        [this.sortBy]: (this.sortOrder ?? 'asc').toUpperCase() as 'ASC' | 'DESC',
      };
    }

    return { skip, take, ...(order ? { order } : {}) };
  }
}

export class PaginationMetaDto {
  @Expose()
  page: number;

  @Expose()
  limit: number;

  @Expose()
  totalItems: number;

  @Expose()
  totalPages: number;

  @Expose()
  hasNextPage: boolean;

  @Expose()
  hasPreviousPage: boolean;
}

export class PaginatedResponseDto<T> {
  @Expose()
  items: T[];

  @Expose()
  meta: PaginationMetaDto;
}

// Alias pour maintenir la compatibilité
export type PaginationParamsDto = QueryParamsDto;
