import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, MaxLength } from 'class-validator';
import { LogisticsEquipmentKind } from '../../../domain/enum/logistics-equipment-kind.enum';

export class CreateLogisticsEquipmentTypeDto {
  @ApiProperty({
    description: 'The name of the logistics equipment type',
    example: 'Standard Pallet',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'The kind of logistics equipment',
    enum: LogisticsEquipmentKind,
    example: LogisticsEquipmentKind.PALLET,
  })
  @IsNotEmpty()
  @IsEnum(LogisticsEquipmentKind)
  kind: LogisticsEquipmentKind;
}
