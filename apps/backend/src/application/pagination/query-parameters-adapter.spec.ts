import { ILike, Repository } from 'typeorm';
import { FilterableColumn } from '../../infrastructure/decorator/filterable-column.decorator';
import { QueryParamsDto } from '../dto/pagination.dto';
import { QueryParametersAdapter } from './pagination-adapter';

class TestEntity {
  @FilterableColumn()
  username: string;

  @FilterableColumn()
  email: string;

  firstName: string; // Non filtrable
}

describe('QueryParametersAdapter', () => {
  let adapter: QueryParametersAdapter<TestEntity>;
  let mockRepository: jest.Mocked<Repository<TestEntity>>;

  beforeEach(() => {
    mockRepository = {
      findAndCount: jest.fn(),
    } as any;

    adapter = new QueryParametersAdapter(mockRepository, TestEntity);
  });

  describe('paginate', () => {
    it('should paginate without filters', async () => {
      const mockData = [
        { username: 'user1', email: '<EMAIL>' },
        { username: 'user2', email: '<EMAIL>' },
      ] as TestEntity[];

      mockRepository.findAndCount.mockResolvedValue([mockData, 2]);

      const pagination = new QueryParamsDto();
      pagination.page = 1;
      pagination.limit = 10;

      const result = await adapter.paginate(pagination, {}, {});

      expect(mockRepository.findAndCount).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        where: {},
      });

      expect(result).toEqual({
        items: mockData,
        meta: {
          page: 1,
          limit: 10,
          totalItems: 2,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
    });

    it('should paginate with filters', async () => {
      const mockData = [{ username: 'john', email: '<EMAIL>' }] as TestEntity[];

      mockRepository.findAndCount.mockResolvedValue([mockData, 1]);

      const pagination = new QueryParamsDto();
      pagination.page = 1;
      pagination.limit = 10;

      const queryParams = {
        username: 'john',
        email: 'test',
        page: 1, // Devrait être ignoré
        limit: 10, // Devrait être ignoré
      };

      const result = await adapter.paginate(pagination, {}, queryParams);

      expect(mockRepository.findAndCount).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        where: {
          username: ILike('%john%'),
          email: ILike('%test%'),
        },
      });

      expect(result.items).toEqual(mockData);
    });

    it('should apply sorting', async () => {
      const mockData = [] as TestEntity[];
      mockRepository.findAndCount.mockResolvedValue([mockData, 0]);

      const pagination = new QueryParamsDto();
      pagination.page = 1;
      pagination.limit = 10;
      pagination.sortBy = 'username';
      pagination.sortOrder = 'desc';

      await adapter.paginate(pagination, {}, {});

      expect(mockRepository.findAndCount).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        where: {},
        order: { username: 'DESC' },
      });
    });

    it('should combine existing where conditions with filters', async () => {
      const mockData = [] as TestEntity[];
      mockRepository.findAndCount.mockResolvedValue([mockData, 0]);

      const pagination = new QueryParamsDto();
      pagination.page = 1;
      pagination.limit = 10;

      const options = {
        where: { firstName: 'John' },
      };

      const queryParams = {
        username: 'test',
      };

      await adapter.paginate(pagination, options, queryParams);

      expect(mockRepository.findAndCount).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        where: {
          firstName: 'John',
          username: ILike('%test%'),
        },
      });
    });

    it('should calculate pagination meta correctly', async () => {
      const mockData = new Array(15).fill(null).map((_, i) => ({
        username: `user${i}`,
        email: `user${i}@test.com`,
      })) as TestEntity[];

      mockRepository.findAndCount.mockResolvedValue([mockData.slice(10, 15), 25]);

      const pagination = new QueryParamsDto();
      pagination.page = 3;
      pagination.limit = 10;

      const result = await adapter.paginate(pagination, {}, {});

      expect(result.meta).toEqual({
        page: 3,
        limit: 10,
        totalItems: 25,
        totalPages: 3,
        hasNextPage: false,
        hasPreviousPage: true,
      });
    });

    it('should ignore non-filterable fields in query params', async () => {
      const mockData = [] as TestEntity[];
      mockRepository.findAndCount.mockResolvedValue([mockData, 0]);

      const pagination = new QueryParamsDto();
      pagination.page = 1;
      pagination.limit = 10;

      const queryParams = {
        username: 'john',
        firstName: 'test', // Non filtrable
        invalidField: 'value', // Non existant
      };

      await adapter.paginate(pagination, {}, queryParams);

      expect(mockRepository.findAndCount).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        where: {
          username: ILike('%john%'),
        },
      });
    });
  });

  describe('validateFilters', () => {
    it('should validate filters correctly', () => {
      const queryParams = {
        username: 'john',
        email: '<EMAIL>',
      };

      const result = adapter.validateFilters(queryParams);

      expect(result).toEqual({
        valid: true,
        errors: [],
      });
    });

    it('should return errors for invalid filters', () => {
      const queryParams = {
        username: 'john',
        firstName: 'test', // Non filtrable
      };

      const result = adapter.validateFilters(queryParams);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Field 'firstName' is not filterable");
    });
  });

  describe('getFilterableFields', () => {
    it('should return filterable fields', () => {
      const result = adapter.getFilterableFields();

      expect(result).toEqual(['username', 'email']);
    });
  });
});
