import { FindManyOptions, FindOptionsOrder, Repository } from 'typeorm';
import { PaginationMetaDto, QueryParamsDto } from '../dto/pagination.dto';
import { FilterEngine } from '../filter/filter-engine';
import { SearchEngine } from '../search/search-engine';

export class QueryParametersAdapter<T> {
  private readonly filterEngine: FilterEngine<T>;
  private readonly searchEngine: SearchEngine<T>;

  constructor(
    private readonly repository: Repository<T>,
    private readonly entityClass: new () => T,
  ) {
    this.filterEngine = new FilterEngine(entityClass);
    this.searchEngine = new SearchEngine(entityClass);
  }

  async paginate(
    pagination: QueryParamsDto,
    options: Omit<FindManyOptions<T>, 'skip' | 'take' | 'order'> = {},
    queryParams: Record<string, any> = {},
  ): Promise<{ items: T[]; meta: PaginationMetaDto }> {
    const { skip, take, order } = pagination.toTypeOrmOptions();
    const { search, ...filterParams } = { ...pagination, ...queryParams };

    // Construire les conditions de recherche
    const searchConditions = this.searchEngine.buildSearchConditions(search || '');

    // Construire les conditions de filtrage
    const filterConditions = this.filterEngine.buildWhereConditions(filterParams);

    // Combiner search et filter conditions
    let whereConditions: any;

    if (searchConditions.length > 0 && Object.keys(filterConditions).length > 0) {
      // Combine search (OR) with filters (AND)
      // Each search condition is combined with all filter conditions
      whereConditions = searchConditions.map((searchCondition) => ({
        ...searchCondition,
        ...filterConditions,
      }));
    } else if (searchConditions.length > 0) {
      // Only search conditions
      whereConditions = searchConditions;
    } else if (Object.keys(filterConditions).length > 0) {
      // Only filter conditions
      whereConditions = filterConditions;
    } else {
      // Existing conditions from options
      whereConditions = options.where;
    }

    // Combine with existing options.where if present
    if (options.where && whereConditions && whereConditions !== options.where) {
      if (Array.isArray(whereConditions)) {
        whereConditions = whereConditions.map((condition) => ({
          ...options.where,
          ...condition,
        }));
      } else {
        whereConditions = {
          ...options.where,
          ...whereConditions,
        };
      }
    }

    const [items, totalItems] = await this.repository.findAndCount({
      ...options,
      where: whereConditions,
      skip,
      take,
      order: order as FindOptionsOrder<T>,
    });

    const totalPages = Math.ceil(totalItems / (pagination.limit ?? 10));
    const meta: PaginationMetaDto = {
      page: pagination.page ?? 1,
      limit: pagination.limit ?? 10,
      totalItems,
      totalPages,
      hasNextPage: (pagination.page ?? 1) < totalPages,
      hasPreviousPage: (pagination.page ?? 1) > 1,
    };

    return { items, meta };
  }

  validateFilters(queryParams: Record<string, any>): {
    valid: boolean;
    errors: string[];
  } {
    return this.filterEngine.validateFilters(queryParams);
  }

  getFilterableFields(): string[] {
    return this.filterEngine.getFilterableFields();
  }
}

export function createQueryParametersAdapter<T>(
  repository: Repository<T>,
  entityClass: new () => T,
): QueryParametersAdapter<T> {
  return new QueryParametersAdapter(repository, entityClass);
}

// Alias pour maintenir la compatibilité
export type PaginationAdapter<T> = QueryParametersAdapter<T>;
export const PaginationAdapter = QueryParametersAdapter;
export const createPaginationAdapter = createQueryParametersAdapter;
