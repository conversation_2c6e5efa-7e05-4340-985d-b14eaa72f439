import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import { Column, DataSource, Entity, PrimaryGeneratedColumn, Repository } from 'typeorm';
import { FilterableColumn } from '../../infrastructure/decorator/filterable-column.decorator';
import { SearchableColumn } from '../../infrastructure/decorator/searchable-column.decorator';
import { QueryParamsDto } from '../dto/pagination.dto';
import { QueryParametersAdapter } from '../pagination/pagination-adapter';

// Test entity with both searchable and filterable columns
@Entity('test_search_integration')
class TestSearchEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @SearchableColumn({ weight: 3 })
  @FilterableColumn()
  @Column()
  name: string;

  @SearchableColumn({ weight: 2 })
  @FilterableColumn()
  @Column()
  email: string;

  @SearchableColumn({ weight: 1, exact: true })
  @Column()
  code: string;

  @FilterableColumn()
  @Column()
  status: string;

  @Column()
  description: string; // Neither searchable nor filterable
}

describe('Search Integration Tests', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let repository: Repository<TestSearchEntity>;
  let adapter: QueryParametersAdapter<TestSearchEntity>;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [TestSearchEntity],
          synchronize: true,
        }),
        TypeOrmModule.forFeature([TestSearchEntity]),
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
    repository = module.get<Repository<TestSearchEntity>>(getRepositoryToken(TestSearchEntity));
    adapter = new QueryParametersAdapter(repository, TestSearchEntity);

    // Seed test data
    await repository.save([
      {
        name: 'John Doe',
        email: '<EMAIL>',
        code: 'USR001',
        status: 'active',
        description: 'Test user 1',
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        code: 'USR002',
        status: 'inactive',
        description: 'Test user 2',
      },
      {
        name: 'Bob Johnson',
        email: '<EMAIL>',
        code: 'ADM001',
        status: 'active',
        description: 'Admin user',
      },
    ]);
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  describe('Search functionality', () => {
    it('should search across multiple columns', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'john';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(2);
      expect(result.items.map((u) => u.name)).toEqual(expect.arrayContaining(['John Doe', 'Bob Johnson']));
    });

    it('should perform exact search when configured', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'USR001';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(1);
      expect(result.items[0].code).toBe('USR001');
    });

    it('should perform partial search by default', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'smith';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(1);
      expect(result.items[0].name).toBe('Jane Smith');
    });

    it('should return empty results for non-matching search', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'nonexistent';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(0);
      expect(result.meta.totalItems).toBe(0);
    });

    it('should ignore empty search terms', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = '';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(3);
      expect(result.meta.totalItems).toBe(3);
    });

    it('should handle whitespace-only search terms', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = '   ';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(3);
      expect(result.meta.totalItems).toBe(3);
    });
  });

  describe('Search + Filter combination', () => {
    it('should combine search and filters correctly', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'john';

      const result = await adapter.paginate(queryParams, {}, { status: 'active' });

      expect(result.items).toHaveLength(1);
      expect(result.items[0].name).toBe('Bob Johnson');
    });

    it('should work with filters only when no search term', async () => {
      const queryParams = new QueryParamsDto();

      const result = await adapter.paginate(queryParams, {}, { status: 'active' });

      expect(result.items).toHaveLength(2);
      expect(result.items.map((u) => u.status)).toEqual(['active', 'active']);
    });

    it('should work with search only when no filters', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'example.com';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(3); // All have example.com in email
    });
  });

  describe('Pagination with search', () => {
    it('should paginate search results correctly', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'example.com';
      queryParams.page = 1;
      queryParams.limit = 2;

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(2);
      expect(result.meta.totalItems).toBe(3);
      expect(result.meta.totalPages).toBe(2);
      expect(result.meta.hasNextPage).toBe(true);
    });

    it('should handle second page of search results', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'example.com';
      queryParams.page = 2;
      queryParams.limit = 2;

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(1);
      expect(result.meta.page).toBe(2);
      expect(result.meta.hasNextPage).toBe(false);
    });
  });

  describe('Sorting with search', () => {
    it('should sort search results', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'example.com';
      queryParams.sortBy = 'name';
      queryParams.sortOrder = 'asc';

      const result = await adapter.paginate(queryParams);

      expect(result.items).toHaveLength(3);
      expect(result.items[0].name).toBe('Bob Johnson');
      expect(result.items[1].name).toBe('Jane Smith');
      expect(result.items[2].name).toBe('John Doe');
    });
  });
});
