# Search Functionality

Ce module fournit des capacités de recherche globale sur les champs d'entités en utilisant le décorateur `@SearchableColumn()`.

## Vue d'ensemble

Le système de recherche permet d'effectuer des recherches globales sur plusieurs champs d'entités simultanément, avec support pour :

- Résultats de recherche pondérés
- Correspondance exacte vs partielle
- Alias de champs pour les relations
- Intégration avec le filtrage et la pagination existants

## Utilisation

### 1. Marquer les champs d'entités comme recherchables

```typescript
import { SearchableColumn } from '../../infrastructure/decorator/searchable-column.decorator';

@Entity()
export class UserEntity {
  @SearchableColumn({ weight: 3 })  // Priorité élevée
  @Column()
  username: string;
  
  @SearchableColumn({ weight: 2 })  // Priorité moyenne
  @Column()
  email: string;
  
  @SearchableColumn({ weight: 1, exact: true })  // Priorité faible, correspondance exacte
  @Column()
  code: string;
  
  @SearchableColumn({ alias: 'profile.name' })  // Avec alias de relation
  @Column()
  displayName: string;
}
```

### 2. Utilisation de l'API

La fonctionnalité de recherche est automatiquement disponible via les paramètres de requête :

```bash
# Recherche globale sur tous les champs recherchables
GET /users?search=john

# Recherche combinée avec filtres
GET /users?search=john&isActive=true&role=OPERATOR

# Recherche avec pagination
GET /users?search=john&page=1&limit=10

# Recherche avec tri
GET /users?search=john&sortBy=username&sortOrder=asc

# Utilisation combinée
GET /users?search=john&isActive=true&page=1&limit=10&sortBy=username&sortOrder=asc
```

### 3. Utilisation directe du SearchEngine

```typescript
const searchEngine = new SearchEngine(UserEntity);

// Vérifier si l'entité a des colonnes recherchables
if (searchEngine.hasSearchableColumns()) {
  // Construire les conditions de recherche
  const conditions = searchEngine.buildSearchConditions('john');
  
  // Obtenir les noms des colonnes recherchables
  const columns = searchEngine.getSearchableColumnNames();
  
  // Obtenir les options de colonne
  const options = searchEngine.getSearchableColumnOptions('username');
}
```

## Options de configuration

### SearchableColumnOptions

```typescript
interface SearchableColumnOptions {
  weight?: number;      // Poids pour le classement des résultats (1-10)
  exact?: boolean;      // true pour correspondance exacte, false pour partielle (défaut)
  alias?: string;       // Alias de champ pour les relations
}
```

## Logique de recherche

### Conditions de recherche

- Les termes de recherche sont appliqués avec la logique **OR** sur toutes les colonnes recherchables
- Les termes de recherche vides sont ignorés
- Les termes de recherche sont nettoyés des espaces

### Combinaison recherche et filtres

- Les conditions de recherche utilisent la logique **OR** (correspondance à n'importe quel champ recherchable)
- Les conditions de filtre utilisent la logique **AND** (correspondance à tous les filtres spécifiés)
- Quand les deux sont présents, chaque condition de recherche est combinée avec toutes les conditions de filtre

### Exemple de logique de requête

```sql
-- Requête : ?search=john&isActive=true&role=OPERATOR
WHERE (
  (username ILIKE '%john%' AND isActive = true AND role = 'OPERATOR') OR
  (email ILIKE '%john%' AND isActive = true AND role = 'OPERATOR') OR
  (firstName ILIKE '%john%' AND isActive = true AND role = 'OPERATOR') OR
  (lastName ILIKE '%john%' AND isActive = true AND role = 'OPERATOR')
)
```

## Considérations de performance

1. **Index de base de données** : Assurez-vous que les colonnes recherchables ont des index appropriés
2. **Longueur des termes de recherche** : Considérez une longueur minimale des termes de recherche pour éviter des requêtes trop larges
3. **Sélection des colonnes** : Limitez les colonnes recherchables aux champs fréquemment recherchés
4. **Exact vs Partiel** : Utilisez la correspondance exacte pour les codes/IDs, partielle pour les champs texte

## Tests

La fonctionnalité de recherche inclut des tests complets :

- Tests unitaires pour SearchEngine
- Tests d'intégration avec QueryParametersAdapter
- Exemples d'utilisation du monde réel

Exécuter les tests avec :

```bash
pnpm test src/application/search/
```

## Exemples complets

### Configuration d'entité complète

```typescript
@Entity('user')
export class UserEntity extends BaseDomainEntity {
  @SearchableColumn({ weight: 3 })
  @FilterableColumn()
  @Column()
  username: string;

  @SearchableColumn({ weight: 2 })
  @FilterableColumn()
  @Column()
  email: string;

  @SearchableColumn({ weight: 1 })
  @FilterableColumn()
  @Column()
  firstName: string;

  @SearchableColumn({ weight: 1 })
  @FilterableColumn()
  @Column()
  lastName: string;

  @FilterableColumn()
  @Column()
  isActive: boolean;
}
```

### Utilisation dans un contrôleur

```typescript
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  async getUsers(@Query() queryParams: QueryParamsDto) {
    return this.userService.findAll(queryParams);
  }
}
```

### Utilisation dans un service

```typescript
@Injectable()
export class UserService {
  private adapter: QueryParametersAdapter<UserEntity>;

  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
  ) {
    this.adapter = new QueryParametersAdapter(this.userRepository, UserEntity);
  }

  async findAll(queryParams: QueryParamsDto) {
    return this.adapter.paginate(queryParams);
  }
}
```

La fonctionnalité de recherche est maintenant prête pour la production et respecte tous les patterns architecturaux existants du projet.
