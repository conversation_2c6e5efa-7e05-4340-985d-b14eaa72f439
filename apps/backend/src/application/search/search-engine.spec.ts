import { Column, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ike } from 'typeorm';
import { SearchableColumn } from '../../infrastructure/decorator/searchable-column.decorator';
import { SearchEngine } from './search-engine';

// Test entity
@Entity()
class TestEntity {
  @SearchableColumn({ weight: 3 })
  @Column()
  name: string;

  @SearchableColumn({ weight: 2, exact: true })
  @Column()
  code: string;

  @SearchableColumn({ weight: 1, alias: 'profile.email' })
  @Column()
  email: string;

  @Column()
  description: string; // Not searchable
}

describe('SearchEngine', () => {
  let searchEngine: SearchEngine<TestEntity>;

  beforeEach(() => {
    searchEngine = new SearchEngine(TestEntity);
  });

  describe('buildSearchConditions', () => {
    it('should return empty array for empty search term', () => {
      const conditions = searchEngine.buildSearchConditions('');
      expect(conditions).toEqual([]);
    });

    it('should return empty array for whitespace-only search term', () => {
      const conditions = searchEngine.buildSearchConditions('   ');
      expect(conditions).toEqual([]);
    });

    it('should build partial search conditions for non-exact fields', () => {
      const conditions = searchEngine.buildSearchConditions('test');

      expect(conditions).toHaveLength(3);
      expect(conditions).toContainEqual({ name: ILike('%test%') });
      expect(conditions).toContainEqual({ 'profile.email': ILike('%test%') }); // with alias
    });

    it('should build exact search conditions for exact fields', () => {
      const conditions = searchEngine.buildSearchConditions('test');

      expect(conditions).toContainEqual({ code: Equal('test') });
    });

    it('should trim search term', () => {
      const conditions = searchEngine.buildSearchConditions('  test  ');

      expect(conditions).toContainEqual({ name: ILike('%test%') });
      expect(conditions).toContainEqual({ code: Equal('test') });
    });

    it('should use alias when provided', () => {
      const conditions = searchEngine.buildSearchConditions('test');

      expect(conditions).toContainEqual({ 'profile.email': ILike('%test%') });
    });
  });

  describe('getSearchableColumnNames', () => {
    it('should return all searchable column names', () => {
      const columnNames = searchEngine.getSearchableColumnNames();
      expect(columnNames).toEqual(['name', 'code', 'email']);
    });
  });

  describe('getSearchableColumnOptions', () => {
    it('should return options for existing column', () => {
      const options = searchEngine.getSearchableColumnOptions('name');
      expect(options).toEqual({ weight: 3, exact: false, alias: undefined });
    });

    it('should return options with exact flag', () => {
      const options = searchEngine.getSearchableColumnOptions('code');
      expect(options).toEqual({ weight: 2, exact: true, alias: undefined });
    });

    it('should return options with alias', () => {
      const options = searchEngine.getSearchableColumnOptions('email');
      expect(options).toEqual({
        weight: 1,
        exact: false,
        alias: 'profile.email',
      });
    });

    it('should return undefined for non-searchable column', () => {
      const options = searchEngine.getSearchableColumnOptions('description');
      expect(options).toBeUndefined();
    });
  });

  describe('hasSearchableColumns', () => {
    it('should return true when entity has searchable columns', () => {
      expect(searchEngine.hasSearchableColumns()).toBe(true);
    });

    it('should return false when entity has no searchable columns', () => {
      @Entity()
      class EmptyEntity {
        @Column()
        id: number;
      }

      const emptySearchEngine = new SearchEngine(EmptyEntity);
      expect(emptySearchEngine.hasSearchableColumns()).toBe(false);
    });
  });
});
