import { Equal, FindOptionsWhere, ILike } from 'typeorm';
import {
  getSearchableColumns,
  SearchableColumnOptions,
} from '../../infrastructure/decorator/searchable-column.decorator';

export class SearchEngine<T> {
  constructor(private readonly entityClass: new () => T) {}

  buildSearchConditions(searchTerm: string): FindOptionsWhere<T>[] {
    if (!searchTerm || searchTerm.trim() === '') {
      return [];
    }

    const searchableColumns = getSearchableColumns(this.entityClass);
    const conditions: FindOptionsWhere<T>[] = [];

    const cleanSearchTerm = searchTerm.trim();

    for (const [columnName, options] of Object.entries(searchableColumns)) {
      const condition: FindOptionsWhere<T> = {};
      const fieldName = options.alias || columnName;

      if (options.exact) {
        // Recherche exacte
        condition[fieldName as keyof T] = Equal(cleanSearchTerm) as any;
      } else {
        // Recherche partielle (ILIKE pour PostgreSQL)
        condition[fieldName as keyof T] = ILike(`%${cleanSearchTerm}%`) as any;
      }

      conditions.push(condition);
    }

    return conditions;
  }

  getSearchableColumnNames(): string[] {
    const searchableColumns = getSearchableColumns(this.entityClass);

    return Object.keys(searchableColumns);
  }

  getSearchableColumnOptions(columnName: string): SearchableColumnOptions | undefined {
    const searchableColumns = getSearchableColumns(this.entityClass);

    return searchableColumns[columnName];
  }

  hasSearchableColumns(): boolean {
    const searchableColumns = getSearchableColumns(this.entityClass);

    return Object.keys(searchableColumns).length > 0;
  }
}
