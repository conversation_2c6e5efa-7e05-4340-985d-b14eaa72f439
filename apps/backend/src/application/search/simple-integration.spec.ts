import { UserEntity } from '../../domain/entity/user.entity';
import { QueryParamsDto } from '../dto/pagination.dto';
import { QueryParametersAdapter } from '../pagination/pagination-adapter';
import { SearchEngine } from './search-engine';

// Mock Repository pour test simple
class MockRepository {
  private data = [
    {
      id: 1,
      username: 'john_doe',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: '<PERSON><PERSON>',
    },
    {
      id: 2,
      username: 'jane_smith',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
    },
    {
      id: 3,
      username: 'bob_johnson',
      email: '<EMAIL>',
      firstName: 'Bob',
      lastName: '<PERSON>',
    },
  ];

  async findAndCount(options: any): Promise<[any[], number]> {
    let result = [...this.data];

    // Simulate search filtering
    if (options.where && Array.isArray(options.where)) {
      const searchResults: any[] = [];

      for (const condition of options.where) {
        for (const item of this.data) {
          let matches = false;

          for (const [key, value] of Object.entries(condition)) {
            if (value && typeof value === 'object' && 'value' in value) {
              // Handle TypeORM operators like ILike
              const searchValue = (value as any).value;
              const itemValue = (item as any)[key];

              if (itemValue && itemValue.toLowerCase().includes(searchValue.toLowerCase().replace(/[%]/g, ''))) {
                matches = true;
                break;
              }
            }
          }

          if (matches && !searchResults.find((r) => r.id === item.id)) {
            searchResults.push(item);
          }
        }
      }
      result = searchResults;
    }

    // Apply pagination
    const skip = options.skip || 0;
    const take = options.take || 10;
    const paginatedResult = result.slice(skip, skip + take);

    return [paginatedResult, result.length];
  }
}

describe('Search Integration - Simple Test', () => {
  let adapter: QueryParametersAdapter<UserEntity>;
  let searchEngine: SearchEngine<UserEntity>;

  beforeEach(() => {
    const mockRepo = new MockRepository() as any;
    adapter = new QueryParametersAdapter(mockRepo, UserEntity);
    searchEngine = new SearchEngine(UserEntity);
  });

  describe('SearchEngine with UserEntity', () => {
    it('should detect searchable columns on UserEntity', () => {
      expect(searchEngine.hasSearchableColumns()).toBe(true);

      const columnNames = searchEngine.getSearchableColumnNames();
      expect(columnNames).toContain('username');
      expect(columnNames).toContain('email');
      expect(columnNames).toContain('firstName');
      expect(columnNames).toContain('lastName');
    });

    it('should have correct weights for searchable columns', () => {
      expect(searchEngine.getSearchableColumnOptions('username')).toEqual({
        weight: 3,
        exact: false,
        alias: undefined,
      });
      expect(searchEngine.getSearchableColumnOptions('email')).toEqual({
        weight: 2,
        exact: false,
        alias: undefined,
      });
      expect(searchEngine.getSearchableColumnOptions('firstName')).toEqual({
        weight: 1,
        exact: false,
        alias: undefined,
      });
    });

    it('should build search conditions correctly', () => {
      const conditions = searchEngine.buildSearchConditions('john');

      expect(conditions).toHaveLength(4); // 4 searchable columns
      expect(conditions[0]).toHaveProperty('username');
      expect(conditions[1]).toHaveProperty('email');
      expect(conditions[2]).toHaveProperty('firstName');
      expect(conditions[3]).toHaveProperty('lastName');
    });
  });

  describe('Integration with QueryParametersAdapter', () => {
    it('should work with search in QueryParamsDto', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = 'john';

      // This test verifies the integration compiles and runs
      const result = await adapter.paginate(queryParams);

      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('meta');
      expect(result.meta).toHaveProperty('totalItems');
    });

    it('should handle empty search gracefully', async () => {
      const queryParams = new QueryParamsDto();
      queryParams.search = '';

      const result = await adapter.paginate(queryParams);

      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('meta');
    });
  });
});
