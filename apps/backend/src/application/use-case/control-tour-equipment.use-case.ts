import { Injectable, NotFoundException } from '@nestjs/common';
import { In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { LogisticsEquipmentCount } from '../../domain/entity/logistics-equipment-count';
import { TourEntity } from '../../domain/entity/tour.entity';
import { LogisticsEquipmentOperation } from '../../domain/enum/logistics-equipment-operation.enum';
import { LogisticsEquipmentDetailsRepository } from '../../infrastructure/repository/logistics-equipment-details.repository';
import { LogisticsEquipmentTypeRepository } from '../../infrastructure/repository/logistics-equipment-type.repository';
import { TourRepository } from '../../infrastructure/repository/tour.repository';
import { ControlTourEquipmentDto } from '../dto/tour/control-tour-equipment.dto';
import { LogisticsEquipmentDetailDto } from '../dto/stop/logistics-equipment-detail.dto';
import { TourService } from '../service/tour.service';

/**
 * Use case pour effectuer le contrôle d'équipement d'une tournée
 *
 * Permet aux réceptionnistes de contrôler et valider l'équipement logistique
 * présent dans une tournée. Cette action permet de vérifier que l'équipement
 * chargé correspond bien aux attentes et de détecter d'éventuels écarts.
 *
 * Fonctionnalités :
 * - Enregistrement du matériel contrôlé (palettes, rolls, colis)
 * - Validation des types d'équipement autorisés
 * - Calcul des totaux d'équipement contrôlé au niveau tournée
 * - Détection d'écarts entre équipement attendu et contrôlé
 *
 * Règles métier :
 * - La tournée doit exister
 * - Au moins un type d'équipement doit être spécifié si des détails sont fournis
 * - Les écarts de contrôle sont acceptés pour le moment
 */
@Injectable()
export class ControlTourEquipmentUseCase {
  constructor(
    private readonly tourRepository: TourRepository,
    private readonly logisticsEquipmentDetailsRepository: LogisticsEquipmentDetailsRepository,
    private readonly logisticsEquipmentTypeRepository: LogisticsEquipmentTypeRepository,
    private readonly tourService: TourService,
  ) {}

  @Transactional()
  async execute(tourId: string, dto: ControlTourEquipmentDto): Promise<TourEntity> {
    const tour = await this.getAndValidateInputTour(tourId);

    if (dto.equipmentCount) {
      tour.controlledEquipmentCount = LogisticsEquipmentCount.fromDto(dto.equipmentCount);
    }

    if (dto.equipmentDetails && dto.equipmentDetails.length > 0) {
      await this.validateEquipmentDetails(dto.equipmentDetails);
      await this.updateTourControlledEquipmentCount(tour, dto.equipmentDetails);
    }

    const updatedTour = await this.tourRepository.save(tour);

    return updatedTour;
  }

  private async getAndValidateInputTour(tourId: string): Promise<TourEntity> {
    const tour = await this.tourRepository.findOne({ where: { id: tourId } });

    if (!tour) {
      throw new NotFoundException(`Tour with ID ${tourId} not found`);
    }

    return tour;
  }

  private async validateEquipmentDetails(equipmentDetails: LogisticsEquipmentDetailDto[]): Promise<void> {
    const equipmentTypes = await this.logisticsEquipmentTypeRepository.find({
      where: {
        id: In(equipmentDetails.map((detail) => detail.logisticsEquipmentTypeId)),
      },
    });

    if (equipmentTypes.length !== equipmentDetails.length) {
      throw new NotFoundException(
        `Invalid equipment details, some equipment types are not found: ${equipmentDetails
          .map((detail) => detail.logisticsEquipmentTypeId)
          .join(', ')}`,
      );
    }
  }

  private async updateTourControlledEquipmentCount(
    tour: TourEntity,
    equipmentDetails: LogisticsEquipmentDetailDto[],
  ): Promise<void> {
    // Supprimer les anciens enregistrements de contrôle pour cette tournée
    await this.logisticsEquipmentDetailsRepository.delete({
      tourId: tour.id,
      operation: LogisticsEquipmentOperation.CONTROLLED,
    });

    // Créer les nouveaux enregistrements de contrôle
    const equipmentDetailsToSave = equipmentDetails.map((detail) => {
      return this.logisticsEquipmentDetailsRepository.create({
        tourId: tour.id,
        operation: LogisticsEquipmentOperation.CONTROLLED,
        quantity: detail.quantity,
        logisticsEquipmentTypeId: detail.logisticsEquipmentTypeId,
      });
    });

    const savedEquipmentDetails = await this.logisticsEquipmentDetailsRepository.save(equipmentDetailsToSave);

    // Récupérer les détails avec les relations pour calculer le total
    const updatedEquipmentDetails = await this.logisticsEquipmentDetailsRepository.find({
      where: { id: In(savedEquipmentDetails.map((detail) => detail.id)) },
      relations: {
        logisticsEquipmentType: true,
      },
    });

    // Mettre à jour le compte total contrôlé
    tour.controlledEquipmentCount = LogisticsEquipmentCount.fromEquipmentDetails(updatedEquipmentDetails);
  }
}
