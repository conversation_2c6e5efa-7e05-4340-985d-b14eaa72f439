import { ConflictException, Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DateTime } from 'luxon';
import { ImportEntity } from '../../domain/entity/import.entity';
import { ImportSourceType } from '../../domain/enum/import-source-type.enum';
import { SftpImportService } from '../service/sftp-import.service';
import { ImportXmlUseCase } from './import-xml-use-case';

@Injectable()
export class ImportXmlFilesFromFtpUseCase {
  private readonly logger = new Logger(ImportXmlFilesFromFtpUseCase.name);
  private _isImportInProgress = false;

  constructor(
    private readonly sftpImportService: SftpImportService,
    private readonly importXmlUseCase: ImportXmlUseCase,
  ) {}

  @Cron(CronExpression.EVERY_30_MINUTES, {
    name: 'importTodayXMLFiles',
    timeZone: 'Europe/Paris',
  })
  public async importTodayXMLFiles(): Promise<void> {
    const date = DateTime.now().toISODate();
    this.logger.log(`Importing XML files from SFTP for date: ${date}`);
    await this.execute(date);
  }

  /**
   * Checks if an SFTP import is currently in progress
   * @returns boolean indicating if import is in progress
   */
  isImportInProgress(): boolean {
    return this._isImportInProgress;
  }

  /**
   * Imports XML tour files from SFTP for a specific date
   * @param date Date in YYYY-MM-DD format
   * @returns ImportEntity with import results
   * @throws ConflictException if another import is already in progress
   */
  async execute(date: string): Promise<ImportEntity> {
    if (this._isImportInProgress) {
      this.logger.warn(`SFTP import request rejected for date ${date}: another import is already in progress`);
      throw new ConflictException(
        'An SFTP import is already in progress. Please wait for it to complete before starting a new import.',
      );
    }

    const parsedDate = DateTime.fromISO(date);

    if (!parsedDate.isValid) {
      throw new Error('Invalid date format. Use YYYY-MM-DD');
    }

    this.logger.log(`Starting SFTP import for date: ${date}`);
    this._isImportInProgress = true;

    try {
      const files = await this.sftpImportService.readXmlFilesForDate(date);
      this.logger.log(`Found ${files.length} XML files on SFTP for ${date}`);

      if (files.length === 0) {
        this.logger.warn(`No XML files found on SFTP for date ${date}`);
      }

      const importEntity = await this.importXmlUseCase.importXmlFiles(
        files,
        parsedDate.toISODate(),
        ImportSourceType.FTP,
      );

      this.logger.log(`SFTP import completed for ${date}: ${importEntity.id}`);

      return importEntity;
    } catch (error) {
      this.logger.error(`SFTP import failed for date ${date}: ${error.message}`);
      throw error;
    } finally {
      this._isImportInProgress = false;
      this.logger.log(`SFTP import lock released for date: ${date}`);
    }
  }
}
