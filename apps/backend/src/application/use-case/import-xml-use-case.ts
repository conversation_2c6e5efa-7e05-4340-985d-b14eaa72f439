import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { ImportEntity } from '../../domain/entity/import.entity';
import { ImportSourceType } from '../../domain/enum/import-source-type.enum';
import { ImportRepository } from '../../infrastructure/repository/import.repository';
import { SkipTourImportException } from '../mappers/xml-import/skip-tour-import.exception';
import { XmlTourImportService } from '../mappers/xml-import/xml-tour-import.service';

@Injectable()
export class ImportXmlUseCase {
  private readonly logger = new Logger(ImportXmlUseCase.name);

  constructor(
    private readonly xmlTourImportService: XmlTourImportService,
    private readonly importRepository: ImportRepository,
  ) {}

  @Transactional()
  /**
   * import xml files.
   * @param toImportFiles the files to import
   * @param importDate the import date from ftp folder name
   */
  async importXmlFiles(
    toImportFiles: { xmlContent: string; fileName: string }[],
    importDate: string,
    importSourceType: ImportSourceType = ImportSourceType.FILESYSTEM,
  ): Promise<ImportEntity> {
    const importEntity = ImportEntity.createImport(importDate);

    await this.importRepository.save(importEntity);

    for (const toImportFile of toImportFiles) {
      try {
        const tourEntity = await this.xmlTourImportService.importTour(toImportFile.xmlContent, toImportFile.fileName, {
          importDate,
          importSourceType,
          importEventEntityId: importEntity.id,
        });

        importEntity.addSuccess(
          {
            fileName: toImportFile.fileName,
            fileContent: toImportFile.xmlContent,
          },
          tourEntity,
        );
      } catch (error) {
        if (error instanceof SkipTourImportException) {
          importEntity.addSkipped();

          continue;
        }

        this.logger.error(`Error importing XML file ${toImportFile.fileName}: ${error}`);

        importEntity.addError({
          fileName: toImportFile.fileName,
          fileContent: toImportFile.xmlContent,
          error: error instanceof Error ? error : new Error(String(error)),
        });
      }
    }

    const importedEntity = await this.importRepository.save(importEntity);

    return importedEntity;
  }
}
