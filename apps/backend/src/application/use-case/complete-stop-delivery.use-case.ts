import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DataSource, In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { ClientEntity } from '../../domain/entity/client.entity';
import { FileEntity } from '../../domain/entity/file.entity';
import { IncidentType } from '../../domain/entity/incident-type.entity';
import { LogisticsEquipmentCount } from '../../domain/entity/logistics-equipment-count';
import { StopCompletionProofPhotoEntity } from '../../domain/entity/stop-completion-proof-photo.entity';
import { DeliveryCompletionType, DeliveryStatus } from '../../domain/entity/stop-completion.entity';
import { StopEntity } from '../../domain/entity/stop.entity';
import { LogisticsEquipmentOperation } from '../../domain/enum/logistics-equipment-operation.enum';
import { DeliveryIncidentEvent } from '../../domain/event/delivery-incident.event';
import { StopCompletedEvent } from '../../domain/event/stop-completed.event';
import { ClientRepository } from '../../infrastructure/repository/client.repository';
import { IncidentTypeRepository } from '../../infrastructure/repository/incident-type.repository';
import { LogisticsEquipmentDetailsRepository } from '../../infrastructure/repository/logistics-equipment-details.repository';
import { LogisticsEquipmentTypeRepository } from '../../infrastructure/repository/logistics-equipment-type.repository';
import { StopRepository } from '../../infrastructure/repository/stop.repository';
import { FileUploadDto } from '../dto/file/file-upload.dto';
import { CompleteStopDeliveryDto } from '../dto/stop/complete-stop-delivery.dto';
import { LogisticsEquipmentDetailDto } from '../dto/stop/logistics-equipment-detail.dto';
import { FileService } from '../service/file.service';

@Injectable()
export class CompleteStopDeliveryUseCase {
  private readonly logger = new Logger(CompleteStopDeliveryUseCase.name);

  constructor(
    private readonly stopRepository: StopRepository,
    private readonly incidentTypeRepository: IncidentTypeRepository,
    private readonly fileService: FileService,
    private readonly logisticsEquipmentDetailsRepository: LogisticsEquipmentDetailsRepository,
    private readonly logisticsEquipmentTypeRepository: LogisticsEquipmentTypeRepository,
    private readonly clientRepository: ClientRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly dataSource: DataSource,
  ) {}

  @Transactional()
  async execute(stopId: string, dto: CompleteStopDeliveryDto): Promise<StopEntity> {
    // 1. Retrieve the stop
    const stop = await this.stopRepository.findOne({
      where: { id: stopId },
      relations: ['tour', 'client'],
    });

    if (!stop) {
      throw new NotFoundException(`Stop with id ${stopId} not found`);
    }

    // 2. Check if stop is already completed
    if (stop.completion?.deliveryStatus === DeliveryStatus.COMPLETED) {
      throw new BadRequestException('Stop has already been completed');
    }

    // 3. Validate business rules
    await this.validateBusinessRules(stop, dto);

    // 4. Handle file uploads
    const signatureFile = dto.signatureFile ? await this.uploadFile(dto.signatureFile, 'signature', stopId) : null;

    // Handle multiple proof photos
    const proofPhotoEntities: StopCompletionProofPhotoEntity[] = [];

    if (dto.photoFiles && dto.photoFiles.length > 0) {
      for (let i = 0; i < dto.photoFiles.length; i++) {
        const photoFile = await this.uploadFile(dto.photoFiles[i], 'proof', stopId, i);
        const proofPhotoEntity = Object.assign(new StopCompletionProofPhotoEntity(), {
          stopId: stopId,
          file: photoFile,
          fileId: photoFile.id,
          sequenceOrder: i,
        });
        proofPhotoEntities.push(proofPhotoEntity);
        await this.dataSource.manager.save(proofPhotoEntity);
      }
    }

    // 5. Validate incident type if provided
    let incidentType: IncidentType | null = null;

    if (dto.incidentTypeId) {
      incidentType = await this.incidentTypeRepository.findOne({
        where: { id: dto.incidentTypeId },
      });

      if (!incidentType) {
        throw new BadRequestException('Invalid incident type');
      }

      if (stop.tour) {
        this.eventEmitter.emit('delivery.incident', new DeliveryIncidentEvent(stop, incidentType));
      }
    }

    // 7. Update stop completion
    stop.completion.deliveryStatus =
      dto.incidentTypeId && dto.deliveryCompletionType === DeliveryCompletionType.NONE
        ? DeliveryStatus.FAILED
        : DeliveryStatus.COMPLETED;

    stop.completion.completedAt = new Date();
    stop.completion.signatureFileId = signatureFile?.id;
    stop.completion.signatureFile = signatureFile;
    stop.completion.signatureFirstName = dto.signatureFirstName;
    stop.completion.signatureLastName = dto.signatureLastName;
    stop.completion.signatureEmail = dto.signatureEmail;
    stop.completion.proofPhotos = proofPhotoEntities;
    stop.completion.incidentTypeId = incidentType?.id;
    stop.completion.incidentType = incidentType;
    stop.completion.deliveryCompletionType = dto.deliveryCompletionType;
    stop.completion.comments = dto.comments;
    stop.completion.latitude = dto.latitude;
    stop.completion.longitude = dto.longitude;
    stop.completion.precision = dto.precision;

    // Handle unloaded equipment
    if (dto.unloadedEquipment) {
      stop.completion.unloadedEquipmentCount = LogisticsEquipmentCount.fromDto(dto.unloadedEquipment);
    }

    if (dto.unloadedEquipmentDetails && dto.unloadedEquipmentDetails.length > 0) {
      await this.validateEquipmentDetails(dto.unloadedEquipmentDetails);
      await this.updateStopEquipmentCount(stop, dto.unloadedEquipmentDetails, LogisticsEquipmentOperation.UNLOADED);
    }

    // Handle returned equipment
    if (dto.returnedEquipment) {
      stop.completion.returnedEquipmentCount = LogisticsEquipmentCount.fromDto(dto.returnedEquipment);
    }

    if (dto.returnedEquipmentDetails && dto.returnedEquipmentDetails.length > 0) {
      await this.validateEquipmentDetails(dto.returnedEquipmentDetails);
      await this.updateStopEquipmentCount(stop, dto.returnedEquipmentDetails, LogisticsEquipmentOperation.RETURNED);
    }

    // 8. Save stop
    const savedStop = await this.stopRepository.save(stop);

    // 11. Emit stop completed event for email notification
    this.eventEmitter.emit(
      'stop.completed',
      new StopCompletedEvent(savedStop, stop.originalClientInfo?.email || dto.signatureEmail || null),
    );

    // 12. Return stop with updated relations
    return await this.stopRepository.findOne({
      where: { id: savedStop.id },
      relations: [
        'tour',
        'client',
        'completion.equipmentDetails',
        'completion.equipmentDetails.logisticsEquipmentType',
      ],
    });
  }

  private async validateBusinessRules(stop: StopEntity, dto: CompleteStopDeliveryDto): Promise<void> {
    // Rule: For secure delivery locations, proof photo is mandatory
    const isSecureLocation = dto.isSecureLocation;

    const hasPhotos = dto.photoFiles && dto.photoFiles.length > 0;

    if (isSecureLocation && !hasPhotos) {
      throw new BadRequestException('Photo proof is required for secure delivery locations');
    }

    if (!isSecureLocation && !dto.signatureFile) {
      throw new BadRequestException('Signature is required for non-secure delivery locations');
    }

    // TODO Implementer les règles metier pour les incidents
  }

  private async uploadFile(
    fileUpload: FileUploadDto,
    type: 'signature' | 'proof',
    stopId: string,
    sequenceOrder?: number,
  ): Promise<FileEntity> {
    // Remove data URL prefix if present
    const base64Content = fileUpload.base64.replace(/^data:[^;]+;base64,/, '');
    const buffer = Buffer.from(base64Content, 'base64');

    const timestamp = new Date().getTime();
    const fileExtension = this.getFileExtension(fileUpload.mimeType);
    const sequenceSuffix = sequenceOrder !== undefined ? `-${sequenceOrder}` : '';
    const filename = `stop-${stopId}-${type}${sequenceSuffix}-${timestamp}${fileExtension}`;

    // Convert FileUploadDto to Express.Multer.File format
    const multerFile = this.convertToMulterFile(fileUpload, buffer, filename);

    // Use FileService instead of direct S3Service
    return await this.fileService.createFile({
      file: multerFile,
      folder: 'stops',
      metadata: {
        type,
        stopId,
        sequenceOrder,
        uploadedAt: new Date().toISOString(),
        ...fileUpload.metadata,
      },
      isPublic: false,
    });
  }

  private getFileExtension(mimeType: string): string {
    switch (mimeType) {
      case 'image/png':
        return '.png';
      case 'image/jpeg':
      case 'image/jpg':
        return '.jpg';
      case 'image/gif':
        return '.gif';
      case 'image/webp':
        return '.webp';
      case 'application/pdf':
        return '.pdf';
      default:
        return '.bin';
    }
  }

  private convertToMulterFile(fileUpload: FileUploadDto, buffer: Buffer, filename: string): Express.Multer.File {
    return {
      buffer,
      originalname: fileUpload.filename,
      mimetype: fileUpload.mimeType,
      size: buffer.length,
      filename,
      fieldname: 'file',
      encoding: '7bit',
      stream: null,
      destination: '',
      path: '',
    } as Express.Multer.File;
  }

  private async validateEquipmentDetails(equipmentDetails: LogisticsEquipmentDetailDto[]): Promise<void> {
    const equipmentTypes = await this.logisticsEquipmentTypeRepository.find({
      where: {
        id: In(equipmentDetails.map((detail) => detail.logisticsEquipmentTypeId)),
      },
    });

    if (equipmentTypes.length !== equipmentDetails.length) {
      throw new NotFoundException(
        `Invalid equipment details, some equipment types are not found: ${equipmentDetails
          .map((detail) => detail.logisticsEquipmentTypeId)
          .join(', ')}`,
      );
    }
  }

  private async updateStopEquipmentCount(
    stop: StopEntity,
    equipmentDetails: LogisticsEquipmentDetailDto[],
    operation: LogisticsEquipmentOperation,
  ): Promise<void> {
    await this.logisticsEquipmentDetailsRepository.delete({
      stopId: stop.id,
      operation,
    });

    const equipmentDetailsToSave = equipmentDetails.map((detail) => {
      return this.logisticsEquipmentDetailsRepository.create({
        stopId: stop.id,
        operation,
        quantity: detail.quantity,
        logisticsEquipmentTypeId: detail.logisticsEquipmentTypeId,
      });
    });

    const savedEquipmentDetails = await this.logisticsEquipmentDetailsRepository.save(equipmentDetailsToSave);

    const updatedEquipmentDetails = await this.logisticsEquipmentDetailsRepository.find({
      where: { id: In(savedEquipmentDetails.map((detail) => detail.id)) },
      relations: {
        logisticsEquipmentType: true,
      },
    });

    if (operation === LogisticsEquipmentOperation.UNLOADED) {
      stop.completion.unloadedEquipmentCount = LogisticsEquipmentCount.fromEquipmentDetails(updatedEquipmentDetails);
    } else if (operation === LogisticsEquipmentOperation.RETURNED) {
      stop.completion.returnedEquipmentCount = LogisticsEquipmentCount.fromEquipmentDetails(updatedEquipmentDetails);
    }
  }

  /** Pour le moment on a pas toutes les specs a ce sujet a terme il faudrait creer une sorte de liste d'utilisateur */
  private async updateClientEmailIfNeeded(client: ClientEntity, newEmail: string, stopId: string): Promise<void> {
    const trimmedEmail = newEmail.trim().toLowerCase();

    // Vérifier si l'email a changé
    if (client.email?.toLowerCase() === trimmedEmail) {
      return;
    }

    // Mettre à jour l'email
    const previousEmail = client.email;
    client.email = trimmedEmail;

    await this.clientRepository.save(client);

    // Logger la modification pour traçabilité
    this.logger.log({
      message: 'Client email updated during stop completion',
      clientId: client.id,
      clientCode: client.code,
      stopId,
      previousEmail,
      newEmail: trimmedEmail,
    });
  }
}
