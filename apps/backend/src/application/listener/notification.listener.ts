import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { ArrayContains } from 'typeorm';
import { DeliveryIncidentEvent } from '../../domain/event/delivery-incident.event';
import { NotificationService } from '../service/notification.service';
import { UserRepository } from '../../infrastructure/repository/user.repository';
import { UserRole } from '../../domain/enum/user-role.enum';
import { NotificationType } from '../../domain/enum/notification-type.enum';

@Injectable()
export class NotificationListener {
  constructor(
    private readonly notificationService: NotificationService,
    private readonly userRepository: UserRepository,
  ) {}

  @OnEvent('delivery.incident', { async: true })
  async handleDeliveryIncident(event: DeliveryIncidentEvent): Promise<void> {
    const managerUsers = await this.userRepository.find({
      where: {
        roles: ArrayContains([UserRole.Manager]),
      },
    });

    for (const user of managerUsers) {
      await this.notificationService.create(NotificationType.DELIVERY_INCIDENT, user.id, {
        stopId: event.stop.id,
        tourId: event.stop.tour?.id,
        incidentType: event.incidentType.name,
        clientName: event.stop.client?.name || event.stop.originalClientInfo?.name,
      });
    }
  }
}
