import { ArrayContains, FindOptionsWhere, <PERSON>ike, IsNull } from 'typeorm';
import {
  getFilterableColumnNames,
  getFilterableColumns,
} from '../../infrastructure/decorator/filterable-column.decorator';

export class FilterEngine<T> {
  constructor(private readonly entityClass: new () => T) {}

  buildWhereConditions(queryParams: Record<string, any>): FindOptionsWhere<T> {
    const filterableColumns = getFilterableColumns(this.entityClass);
    const conditions: any = {};

    for (const [field, value] of Object.entries(queryParams)) {
      // Ignorer les paramètres de pagination/tri
      if (['page', 'limit', 'sortBy', 'sortOrder', 'search'].includes(field)) {
        continue;
      }

      const columnMetadata = filterableColumns[field];

      if (!columnMetadata?.enabled) {
        continue;
      }

      // Si la valeur est null ou undefined dans les paramètres de requête, ignorer
      if (value === null || value === undefined) {
        continue;
      }

      // Si la valeur est une chaîne vide, ignorer
      if (typeof value === 'string' && value.trim() === '') {
        continue;
      }

      // Résoudre le chemin de la propriété (pour les entités embedded)
      const targetField = this.resolveFieldPath(field, columnMetadata);

      // Gestion spéciale pour la valeur null qui signifie rechercher les valeurs nulles
      if (value === 'null') {
        conditions[targetField] = IsNull();
        continue;
      }

      // Appliquer le type de filtre selon les options du decorator
      const filterType = columnMetadata.options?.type || 'ilike';

      switch (filterType) {
        case 'array-contains':
          conditions[targetField] = ArrayContains([value]);
          break;
        case 'exact':
          conditions[targetField] = value;
          break;
        case 'ilike':
        default:
          conditions[targetField] = ILike(`%${value}%`);
          break;
      }
    }

    return conditions;
  }

  validateFilters(queryParams: Record<string, any>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const filterableColumnNames = getFilterableColumnNames(this.entityClass);

    for (const [field, value] of Object.entries(queryParams)) {
      // Ignorer les paramètres de pagination/tri/recherche
      if (['page', 'limit', 'sortBy', 'sortOrder', 'search'].includes(field)) {
        continue;
      }

      if (value && !filterableColumnNames.includes(field)) {
        errors.push(`Field '${field}' is not filterable`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  getFilterableFields(): string[] {
    return getFilterableColumnNames(this.entityClass);
  }

  /**
   * Résout le chemin d'une propriété pour les entités embedded
   * Par exemple: si field='tourOriginalNumber' et path='tourIdentifier.originalNumber'
   * alors retourne 'tourIdentifier.originalNumber' pour TypeORM
   */
  private resolveFieldPath(field: string, columnMetadata: any): string {
    // Si un path spécifique est défini dans les options, l'utiliser
    if (columnMetadata.options?.path) {
      return columnMetadata.options.path;
    }

    // Sinon, utiliser le nom du field par défaut (comportement existant)
    return field;
  }
}
