import { Array<PERSON>ontains, <PERSON>ike, IsNull } from 'typeorm';
import { FilterableColumn } from '../../infrastructure/decorator/filterable-column.decorator';
import { FilterEngine } from './filter-engine';

class TestEntity {
  @FilterableColumn()
  username: string;

  @FilterableColumn()
  email: string;

  @FilterableColumn()
  firstName: string;

  @FilterableColumn({ type: 'array-contains' })
  roles: string[];

  @FilterableColumn({ type: 'exact' })
  status: string;

  @FilterableColumn({ type: 'ilike', path: 'tourIdentifier.originalNumber' })
  tourOriginalNumber?: string;

  lastName: string; // Non filtrable
}

describe('FilterEngine', () => {
  let filterEngine: FilterEngine<TestEntity>;

  beforeEach(() => {
    filterEngine = new FilterEngine(TestEntity);
  });

  describe('buildWhereConditions', () => {
    it('should build conditions for filterable fields', () => {
      const queryParams = {
        username: 'john',
        email: '<EMAIL>',
        page: 1,
        limit: 10,
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        username: ILike('%john%'),
        email: ILike('%<EMAIL>%'),
      });
    });

    it('should handle array-contains filter type', () => {
      const queryParams = {
        roles: 'lrg-bl-manager',
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        roles: ArrayContains(['lrg-bl-manager']),
      });
    });

    it('should handle exact filter type', () => {
      const queryParams = {
        status: 'active',
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        status: 'active',
      });
    });

    it('should handle null special case with IsNull', () => {
      const queryParams = {
        roles: 'null',
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        roles: IsNull(),
      });
    });

    it('should ignore pagination and sorting parameters', () => {
      const queryParams = {
        username: 'john',
        page: 1,
        limit: 10,
        sortBy: 'username',
        sortOrder: 'asc',
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        username: ILike('%john%'),
      });
    });

    it('should ignore non-filterable fields', () => {
      const queryParams = {
        username: 'john',
        lastName: 'smith', // Non filtrable
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        username: ILike('%john%'),
      });
    });

    it('should ignore empty or whitespace-only values', () => {
      const queryParams = {
        username: '',
        email: '   ',
        firstName: 'john',
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        firstName: ILike('%john%'),
      });
    });

    it('should return empty object when no filterable params', () => {
      const queryParams = {
        page: 1,
        limit: 10,
        lastName: 'smith', // Non filtrable
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({});
    });

    it('should handle null and undefined values', () => {
      const queryParams = {
        username: null,
        email: undefined,
        firstName: 'john',
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        firstName: ILike('%john%'),
      });
    });

    it('should handle embedded entity filtering with path option', () => {
      const queryParams = {
        tourOriginalNumber: '2204',
      };

      const result = filterEngine.buildWhereConditions(queryParams);

      expect(result).toEqual({
        'tourIdentifier.originalNumber': ILike('%2204%'),
      });
    });
  });

  describe('validateFilters', () => {
    it('should validate filterable fields', () => {
      const queryParams = {
        username: 'john',
        email: '<EMAIL>',
        page: 1,
        limit: 10,
      };

      const result = filterEngine.validateFilters(queryParams);

      expect(result).toEqual({
        valid: true,
        errors: [],
      });
    });

    it('should return errors for non-filterable fields', () => {
      const queryParams = {
        username: 'john',
        lastName: 'smith', // Non filtrable
        invalidField: 'value', // Non existant
      };

      const result = filterEngine.validateFilters(queryParams);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Field 'lastName' is not filterable");
      expect(result.errors).toContain("Field 'invalidField' is not filterable");
    });

    it('should ignore pagination parameters in validation', () => {
      const queryParams = {
        page: 1,
        limit: 10,
        sortBy: 'username',
        sortOrder: 'asc',
      };

      const result = filterEngine.validateFilters(queryParams);

      expect(result).toEqual({
        valid: true,
        errors: [],
      });
    });

    it('should ignore empty values in validation', () => {
      const queryParams = {
        username: '',
        lastName: '', // Non filtrable mais vide
      };

      const result = filterEngine.validateFilters(queryParams);

      expect(result).toEqual({
        valid: true,
        errors: [],
      });
    });
  });

  describe('getFilterableFields', () => {
    it('should return list of filterable fields', () => {
      const result = filterEngine.getFilterableFields();

      expect(result).toEqual(['username', 'email', 'firstName', 'roles', 'status', 'tourOriginalNumber']);
    });
  });
});
