import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../../domain/entity/user.entity';
import { QueryParametersAdapter } from '../pagination/pagination-adapter';
import { QueryParamsDto } from '../dto/pagination.dto';

describe('Filter Integration Test', () => {
  let adapter: QueryParametersAdapter<UserEntity>;
  let repository: Repository<UserEntity>;

  beforeEach(async () => {
    const mockRepository = {
      findAndCount: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getRepositoryToken(UserEntity),
          useValue: mockRepository,
        },
      ],
    }).compile();

    repository = module.get<Repository<UserEntity>>(getRepositoryToken(UserEntity));
    adapter = new QueryParametersAdapter(repository, UserEntity);
  });

  it('should create a complete filtering workflow', async () => {
    // Simuler des données utilisateur
    const mockUsers = [
      {
        id: '1',
        username: 'john_doe',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
      },
      {
        id: '2',
        username: 'jane_smith',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
      },
    ] as UserEntity[];

    (repository.findAndCount as jest.Mock).mockResolvedValue([mockUsers, 2]);

    // Paramètres de pagination
    const pagination = new QueryParamsDto();
    pagination.page = 1;
    pagination.limit = 10;
    pagination.sortBy = 'username';
    pagination.sortOrder = 'asc';

    // Paramètres de filtrage comme ils arriveraient du frontend
    const queryParams = {
      page: '1',
      limit: '10',
      sortBy: 'username',
      sortOrder: 'asc',
      username: 'john',
      email: 'example',
    };

    // Exécuter la requête
    const result = await adapter.paginate(pagination, {}, queryParams);

    // Vérifier que le repository a été appelé avec les bons paramètres
    expect(repository.findAndCount).toHaveBeenCalledWith({
      skip: 0,
      take: 10,
      order: { username: 'ASC' },
      where: {
        username: expect.objectContaining({
          _type: 'ilike',
          _value: '%john%',
        }),
        email: expect.objectContaining({
          _type: 'ilike',
          _value: '%example%',
        }),
      },
    });

    // Vérifier le résultat
    expect(result.items).toEqual(mockUsers);
    expect(result.meta).toEqual({
      page: 1,
      limit: 10,
      totalItems: 2,
      totalPages: 1,
      hasNextPage: false,
      hasPreviousPage: false,
    });
  });

  it('should handle empty filters gracefully', async () => {
    const mockUsers = [] as UserEntity[];
    (repository.findAndCount as jest.Mock).mockResolvedValue([mockUsers, 0]);

    const pagination = new QueryParamsDto();
    pagination.page = 1;
    pagination.limit = 10;

    const queryParams = {
      page: '1',
      limit: '10',
    };

    const result = await adapter.paginate(pagination, {}, queryParams);

    expect(repository.findAndCount).toHaveBeenCalledWith({
      skip: 0,
      take: 10,
      where: {},
    });

    expect(result.items).toEqual([]);
  });

  it('should validate filters correctly', () => {
    const validParams = {
      username: 'john',
      email: '<EMAIL>',
      page: '1',
      limit: '10',
    };

    const validResult = adapter.validateFilters(validParams);
    expect(validResult.valid).toBe(true);
    expect(validResult.errors).toEqual([]);

    const invalidParams = {
      username: 'john',
      nonFilterableField: 'value',
      anotherInvalid: 'test',
    };

    const invalidResult = adapter.validateFilters(invalidParams);
    expect(invalidResult.valid).toBe(false);
    expect(invalidResult.errors).toContain("Field 'nonFilterableField' is not filterable");
    expect(invalidResult.errors).toContain("Field 'anotherInvalid' is not filterable");
  });

  it('should return correct filterable fields', () => {
    const fields = adapter.getFilterableFields();

    // UserEntity a @FilterableColumn() sur username, email, firstName, lastName, locale, color
    expect(fields).toContain('username');
    expect(fields).toContain('email');
    expect(fields).toContain('firstName');
    expect(fields).toContain('lastName');
    expect(fields).toContain('locale');
    expect(fields).toContain('color');
  });
});
