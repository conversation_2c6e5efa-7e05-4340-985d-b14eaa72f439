import { Injectable } from '@nestjs/common';

export interface DeliveryConfirmationTemplateData {
  clientName: string;
  stopId: string;
  deliveryDate: Date;
  tourNumber: string;
  deliveryAddress: string;
  completedAt: Date;
  deliveryStatus: string;
  hasSignature: boolean;
  hasPhoto: boolean;
}

@Injectable()
export class EmailTemplateRendererService {
  generateDeliveryConfirmationHtml(data: DeliveryConfirmationTemplateData): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de livraison</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .info-row { margin: 10px 0; }
        .label { font-weight: bold; }
        .footer { background-color: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }
        .proof-info { background-color: #e9ecef; padding: 15px; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Confirmation de livraison</h1>
        </div>
        
        <div class="content">
            <p>Bonjour ${data.clientName || 'Client'},</p>
            
            <p>Nous vous confirmons que votre livraison a été effectuée avec succès.</p>
            
            <div class="info-row">
                <span class="label">Numéro de tournée :</span> ${data.tourNumber}
            </div>
            
            <div class="info-row">
                <span class="label">Date de livraison :</span> ${data.deliveryDate.toLocaleDateString('fr-FR')}
            </div>
            
            <div class="info-row">
                <span class="label">Adresse de livraison :</span> ${data.deliveryAddress}
            </div>
            
            <div class="info-row">
                <span class="label">Heure de livraison :</span> ${data.completedAt.toLocaleString('fr-FR')}
            </div>
            
            <div class="info-row">
                <span class="label">Statut :</span> ${data.deliveryStatus}
            </div>
            
            <div class="proof-info">
                <h3>Preuves de livraison</h3>
                <div class="info-row">
                    <span class="label">Signature :</span> ${data.hasSignature ? '✓ Disponible' : '✗ Non disponible'}
                </div>
                <div class="info-row">
                    <span class="label">Photo :</span> ${data.hasPhoto ? '✓ Disponible' : '✗ Non disponible'}
                </div>
            </div>
            
            <p>Pour toute question concernant cette livraison, n'hésitez pas à nous contacter en mentionnant la référence : <strong>${data.stopId}</strong></p>
        </div>
        
        <div class="footer">
            <p>LRG Livraisons - Service client<br>
            Ce message a été généré automatiquement, merci de ne pas y répondre.</p>
        </div>
    </div>
</body>
</html>
    `.trim();
  }

  generateDeliveryConfirmationText(data: DeliveryConfirmationTemplateData): string {
    return `
CONFIRMATION DE LIVRAISON

Bonjour ${data.clientName || 'Client'},

Nous vous confirmons que votre livraison a été effectuée avec succès.

Détails de la livraison :
- Numéro de tournée : ${data.tourNumber}
- Date de livraison : ${data.deliveryDate.toLocaleDateString('fr-FR')}
- Adresse de livraison : ${data.deliveryAddress}
- Heure de livraison : ${data.completedAt.toLocaleString('fr-FR')}
- Statut : ${data.deliveryStatus}

Preuves de livraison :
- Signature : ${data.hasSignature ? 'Disponible' : 'Non disponible'}
- Photo : ${data.hasPhoto ? 'Disponible' : 'Non disponible'}

Pour toute question concernant cette livraison, n'hésitez pas à nous contacter en mentionnant la référence : ${data.stopId}

LRG Livraisons - Service client
Ce message a été généré automatiquement, merci de ne pas y répondre.
    `.trim();
  }

  generateDeliveryConfirmationSubject(data: DeliveryConfirmationTemplateData): string {
    return `Confirmation de livraison - ${data.tourNumber} - ${data.deliveryDate.toLocaleDateString('fr-FR')}`;
  }
}
