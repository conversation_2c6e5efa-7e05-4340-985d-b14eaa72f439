import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { UserRepresentation } from '@s3pweb/keycloak-admin-client-cjs';
import { UserEntity } from '../../domain/entity/user.entity';
import { UserRole } from '../../domain/enum/user-role.enum';
import { UserRepository } from '../../infrastructure/repository/user.repository';
import { KeycloakAdminService } from '../../infrastructure/service/keycloak-admin.service';
import { isArrayEquals } from '../../infrastructure/util/isArrayEquals.utls';
import { PaginationMetaDto, QueryParamsDto } from '../dto/pagination.dto';
import { QueryParametersAdapter } from '../pagination/pagination-adapter';
import { ColorService } from './color.service';

@Injectable()
export class UserQueryService {
  private readonly logger = new Logger(UserQueryService.name);
  private readonly paginationAdapter: QueryParametersAdapter<UserEntity>;

  constructor(
    private readonly userRepository: UserRepository,
    private readonly colorService: ColorService,
    private readonly keycloakAdminService: KeycloakAdminService,
  ) {
    this.paginationAdapter = new QueryParametersAdapter(userRepository, UserEntity);
  }

  async getOneById(id: string): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { id },
      loadEagerRelations: false,
    });

    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found`);
    }

    return this.hydrateAndPersistUser(user);
  }

  async getOneByUsername(username: string): Promise<UserEntity> {
    const user = await this.userRepository.findOne({
      where: { username },
      loadEagerRelations: false,
    });

    if (!user) {
      throw new NotFoundException(`User with username "${username}" not found`);
    }

    return this.hydrateAndPersistUser(user);
  }

  async findAllPaginated(
    pagination: QueryParamsDto,
    queryParams: Record<string, any> = {},
  ): Promise<{ items: UserEntity[]; meta: PaginationMetaDto }> {
    const { items: users, meta } = await this.paginationAdapter.paginate(pagination, {}, queryParams);

    // Hydrate each user via Keycloak and auto-migrate colors
    const hydratedUsers = await Promise.all(
      users.map(async (user) => {
        return await this.hydrateAndPersistUser(user);
      }),
    );

    return { items: hydratedUsers, meta };
  }

  public async hydrateAndPersistUser(user: UserEntity): Promise<UserEntity> {
    const keycloakUser = await this.keycloakAdminService.getUserByUsername(user.username).catch(() => null);

    if (!keycloakUser) {
      return user;
    }

    const mappedUserReturn = this.mapUserInformationFromKeycloak(user, keycloakUser);
    let shouldSave = mappedUserReturn[1].shouldSave;
    const mappedUser = mappedUserReturn[0];

    if (!user.color) {
      const color = this.colorService.generateColorFromId(user.id);
      mappedUser.color = color;
      shouldSave = true;
    }

    if (shouldSave) {
      await this.userRepository.save(mappedUser);
    }

    return mappedUser;
  }

  private mapUserInformationFromKeycloak(
    userEntity: UserEntity,
    keycloakUserRepresentation: UserRepresentation,
  ): [UserEntity, { shouldSave: boolean }] {
    let shouldSave = false;

    if (userEntity.email !== keycloakUserRepresentation.email && keycloakUserRepresentation.email) {
      userEntity.email = keycloakUserRepresentation.email;
      shouldSave = true;
    }

    if (userEntity.firstName !== keycloakUserRepresentation.firstName && keycloakUserRepresentation.firstName) {
      userEntity.firstName = keycloakUserRepresentation.firstName;
      shouldSave = true;
    }

    if (userEntity.lastName !== keycloakUserRepresentation.lastName && keycloakUserRepresentation.lastName) {
      userEntity.lastName = keycloakUserRepresentation.lastName;
      shouldSave = true;
    }

    const applicationRoles = keycloakUserRepresentation.realmRoles?.filter((role) =>
      Object.values(UserRole).includes(role as UserRole),
    );

    if (!!applicationRoles?.length && !isArrayEquals(userEntity.roles || [], applicationRoles)) {
      userEntity.roles = applicationRoles as UserRole[];
      shouldSave = true;
    }

    return [userEntity, { shouldSave }];
  }
}
