import { Injectable } from '@nestjs/common';
import { nanoid } from 'nanoid';
import { FileEntity } from '../../domain/entity/file.entity';
import { UserEntity } from '../../domain/entity/user.entity';
import { FileRepository } from '../../infrastructure/repository/file.repository';
import { S3Service } from '../../infrastructure/service/s3.service';
import { PaginationMetaDto, PaginationParamsDto } from '../dto/pagination.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';

@Injectable()
export class FileService {
  private readonly paginationAdapter: PaginationAdapter<FileEntity>;

  constructor(
    private readonly fileRepository: FileRepository,
    private readonly s3Service: S3Service,
  ) {
    this.paginationAdapter = new PaginationAdapter(fileRepository, FileEntity);
  }

  async createFile(params: {
    file: Express.Multer.File;
    folder: string;
    createdByUser?: UserEntity;
    metadata?: Record<string, unknown>;
    isPublic?: boolean;
  }): Promise<FileEntity> {
    const { file, folder, createdByUser, metadata, isPublic } = params;
    const s3fileKey = `${folder}/${nanoid(16)}`;

    // Upload to S3
    await this.s3Service.upload(s3fileKey, file.buffer, {
      contentType: file.mimetype,
    });

    // Persist in DB (fileSize stocké en number, à sérialiser en string côté DTO/API)
    const fileEntity = this.fileRepository.create({
      s3fileKey,
      originalFilename: file.originalname,
      fileSize: file.size?.toString(),
      contentType: file.mimetype,
      uploadedBy: createdByUser,
      metadata,
      isPublic: isPublic ?? false,
    });

    return this.fileRepository.save(fileEntity);
  }

  async getPaginatedFiles(pagination: PaginationParamsDto): Promise<{ items: FileEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(pagination, {
      relations: ['uploadedBy'],
    });
  }
}
