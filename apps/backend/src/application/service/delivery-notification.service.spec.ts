import { Test, TestingModule } from '@nestjs/testing';
import { DeliveryNotificationService } from './delivery-notification.service';
import { EmailService } from '../../infrastructure/service/email.service';
import { EmailTemplateRendererService } from './email-template-renderer.service';

describe('DeliveryNotificationService', () => {
  let service: DeliveryNotificationService;
  let emailService: jest.Mocked<EmailService>;

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  const mockEmailTemplateRenderer = {
    generateDeliveryConfirmationSubject: jest.fn().mockReturnValue('Test Subject'),
    generateDeliveryConfirmationHtml: jest.fn().mockReturnValue('<html>Test HTML</html>'),
    generateDeliveryConfirmationText: jest.fn().mockReturnValue('Test Text'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeliveryNotificationService,
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: EmailTemplateRendererService,
          useValue: mockEmailTemplateRenderer,
        },
      ],
    }).compile();

    service = module.get<DeliveryNotificationService>(DeliveryNotificationService);
    emailService = module.get(EmailService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendDeliveryNotification', () => {
    const baseNotificationData = {
      stopId: 'stop-123',
      tourNumber: '4029',
      deliveryDate: new Date('2024-01-15'),
      deliveryAddress: '123 Test Street',
      completedAt: new Date('2024-01-15T14:30:00Z'),
      deliveryStatus: 'COMPLETED',
      hasSignature: true,
      hasPhoto: false,
    };

    it('should use operator input email with highest priority', async () => {
      const data = {
        ...baseNotificationData,
        operatorInputEmail: '<EMAIL>',
        clientEntity: {
          id: 'client-1',
          name: 'Test Client',
          email: '<EMAIL>',
        },
        originalClientInfo: {
          name: 'Original Client',
          email: '<EMAIL>',
        },
      };

      await service.sendDeliveryNotification(data);

      expect(emailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Test Subject',
          html: '<html>Test HTML</html>',
          text: 'Test Text',
        }),
      );
    });

    it('should use client entity email when operator input is not provided', async () => {
      const data = {
        ...baseNotificationData,
        operatorInputEmail: null,
        clientEntity: {
          id: 'client-1',
          name: 'Test Client',
          email: '<EMAIL>',
        },
        originalClientInfo: {
          name: 'Original Client',
          email: '<EMAIL>',
        },
      };

      await service.sendDeliveryNotification(data);

      expect(emailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
      );
    });

    it('should use original client info email when others are not available', async () => {
      const data = {
        ...baseNotificationData,
        operatorInputEmail: null,
        clientEntity: {
          id: 'client-1',
          name: 'Test Client',
          email: null,
        },
        originalClientInfo: {
          name: 'Original Client',
          email: '<EMAIL>',
        },
      };

      await service.sendDeliveryNotification(data);

      expect(emailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
      );
    });

    it('should skip notification when no email is available', async () => {
      const data = {
        ...baseNotificationData,
        operatorInputEmail: null,
        clientEntity: null,
        originalClientInfo: null,
      };

      await service.sendDeliveryNotification(data);

      expect(emailService.sendEmail).not.toHaveBeenCalled();
    });

    it('should handle empty/whitespace emails correctly', async () => {
      const data = {
        ...baseNotificationData,
        operatorInputEmail: '   ',
        clientEntity: {
          id: 'client-1',
          name: 'Test Client',
          email: '',
        },
        originalClientInfo: {
          name: 'Original Client',
          email: '<EMAIL>',
        },
      };

      await service.sendDeliveryNotification(data);

      expect(emailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
      );
    });

    it('should not throw error when email sending fails', async () => {
      const data = {
        ...baseNotificationData,
        operatorInputEmail: '<EMAIL>',
        clientEntity: null,
        originalClientInfo: null,
      };

      emailService.sendEmail.mockRejectedValue(new Error('SMTP Error'));

      await expect(service.sendDeliveryNotification(data)).resolves.not.toThrow();
    });

    it('should use fallback client name when none is provided', async () => {
      const data = {
        ...baseNotificationData,
        operatorInputEmail: '<EMAIL>',
        clientEntity: null,
        originalClientInfo: null,
      };

      await service.sendDeliveryNotification(data);

      expect(emailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
      );
    });
  });
});
