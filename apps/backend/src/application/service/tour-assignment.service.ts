import { Injectable, NotFoundException } from '@nestjs/common';
import { TourAssignmentEntity } from '../../domain/entity/tour-assignment.entity';
import { TourAssignmentRepository } from '../../infrastructure/repository/tour-assignment.repository';
import { PaginationMetaDto, PaginationParamsDto } from '../dto/pagination.dto';
import { CreateTourAssignmentDto, UpdateTourAssignmentDto } from '../dto/tour-assignment/tour-assignment.dto';
import { TourAssignmentMapper } from '../mappers/tour-assignment.mapper';
import { PaginationAdapter } from '../pagination/pagination-adapter';

@Injectable()
export class TourAssignmentService {
  private readonly paginationAdapter: PaginationAdapter<TourAssignmentEntity>;

  constructor(
    private readonly tourAssignmentRepository: TourAssignmentRepository,
    private readonly mapper: TourAssignmentMapper,
  ) {
    this.paginationAdapter = new PaginationAdapter(tourAssignmentRepository, TourAssignmentEntity);
  }

  async create(createDto: CreateTourAssignmentDto): Promise<TourAssignmentEntity> {
    const assignment = await this.mapper.toEntity(createDto);

    const mergeableAssignments = await this.tourAssignmentRepository.findMergeableAssignments(
      assignment.tourIdentifier.number,
      assignment.tourIdentifier.type,
      assignment.tourIdentifier.originalNumber,
      assignment.userId,
      assignment.fromDate,
      assignment.toDate,
    );

    if (mergeableAssignments.length > 0) {
      // Calculer les nouvelles dates en fusionnant toutes les assignations
      const allFromDates = [assignment.fromDate, ...mergeableAssignments.map((a) => a.fromDate)];
      const allToDates = [assignment.toDate, ...mergeableAssignments.map((a) => a.toDate)].filter(Boolean);

      const newFromDate = allFromDates.reduce((min, date) => (date < min ? date : min));

      // Si la nouvelle assignation ou une existante est ouverte (toDate = undefined), l'assignation fusionnée est ouverte
      const hasOpenEndedAssignment = !assignment.toDate || mergeableAssignments.some((a) => !a.toDate);
      const newToDate = hasOpenEndedAssignment
        ? undefined
        : allToDates.reduce((max, date) => (date > max ? date : max));

      // Supprimer toutes les assignations existantes sauf la première
      const primaryAssignment = mergeableAssignments[0];
      const assignmentsToDelete = mergeableAssignments.slice(1);

      await Promise.all(assignmentsToDelete.map((a) => this.tourAssignmentRepository.delete(a.id)));

      // Mettre à jour l'assignation principale
      primaryAssignment.fromDate = newFromDate;
      primaryAssignment.toDate = newToDate;

      return this.tourAssignmentRepository.save(primaryAssignment);
    }

    return this.tourAssignmentRepository.save(assignment);
  }

  async update(id: string, updateDto: UpdateTourAssignmentDto): Promise<TourAssignmentEntity> {
    const existing = await this.tourAssignmentRepository.findOne({
      where: { id },
    });

    if (!existing) {
      throw new NotFoundException(`TourAssignment with ID ${id} not found`);
    }

    // If userId is being changed, check for potential conflicts
    if (updateDto.userId && updateDto.userId !== existing.userId) {
      const newFromDate = updateDto.fromDate || existing.fromDate;
      const newToDate = updateDto.toDate !== undefined ? updateDto.toDate : existing.toDate;

      const conflictingAssignments = await this.tourAssignmentRepository.findMergeableAssignments(
        existing.tourIdentifier.number,
        existing.tourIdentifier.type,
        existing.tourIdentifier.originalNumber,
        updateDto.userId,
        newFromDate,
        newToDate,
      );

      if (conflictingAssignments.length > 0) {
        throw new Error(
          `Cannot update assignment: User ${updateDto.userId} already has conflicting assignments for this tour during the specified period`,
        );
      }
    }

    const updated = await this.mapper.toEntity(updateDto, existing);

    return await this.tourAssignmentRepository.save(updated);
  }

  async findOne(id: string): Promise<TourAssignmentEntity> {
    const assignment = await this.tourAssignmentRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!assignment) {
      throw new NotFoundException(`TourAssignment with ID ${id} not found`);
    }

    return assignment;
  }

  async findAll(): Promise<TourAssignmentEntity[]> {
    return this.tourAssignmentRepository.find({
      relations: ['user'],
      order: { fromDate: 'DESC' },
    });
  }

  async findAllPaginated(
    pagination: PaginationParamsDto,
  ): Promise<{ items: TourAssignmentEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(pagination, {
      relations: ['user'],
    });
  }

  async delete(id: string): Promise<void> {
    const result = await this.tourAssignmentRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`TourAssignment with ID ${id} not found`);
    }
  }
}
