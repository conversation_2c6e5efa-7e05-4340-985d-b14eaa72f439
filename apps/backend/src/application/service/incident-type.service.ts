import { Injectable, NotFoundException } from '@nestjs/common';
import { IncidentType } from '../../domain/entity/incident-type.entity';
import { IncidentTypeRepository } from '../../infrastructure/repository/incident-type.repository';
import { PaginationMetaDto, PaginationParamsDto } from '../dto/pagination.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';
import { CreateIncidentTypeDto, UpdateIncidentTypeDto } from '../dto/incident-type/incident-type.dto';

@Injectable()
export class IncidentTypeService {
  private readonly paginationAdapter: PaginationAdapter<IncidentType>;

  constructor(private readonly incidentTypeRepository: IncidentTypeRepository) {
    this.paginationAdapter = new PaginationAdapter(incidentTypeRepository, IncidentType);
  }

  async create(createIncidentTypeDto: CreateIncidentTypeDto): Promise<IncidentType> {
    const incidentType = this.incidentTypeRepository.create(createIncidentTypeDto);

    return this.incidentTypeRepository.save(incidentType);
  }

  async findAll(pagination: PaginationParamsDto): Promise<{ items: IncidentType[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(pagination);
  }

  async findAllWithoutPagination(): Promise<IncidentType[]> {
    return this.incidentTypeRepository.find();
  }

  async findOne(id: string): Promise<IncidentType> {
    const incidentType = await this.incidentTypeRepository.findOneBy({ id });

    if (!incidentType) {
      throw new NotFoundException(`IncidentType with ID ${id} not found`);
    }

    return incidentType;
  }

  async update(id: string, updateIncidentTypeDto: UpdateIncidentTypeDto): Promise<IncidentType> {
    const incidentType = await this.findOne(id);
    Object.assign(incidentType, updateIncidentTypeDto);

    return this.incidentTypeRepository.save(incidentType);
  }

  async remove(id: string): Promise<void> {
    const incidentType = await this.findOne(id);
    await this.incidentTypeRepository.remove(incidentType);
  }
}
