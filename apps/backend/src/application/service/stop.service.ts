import { Injectable, NotFoundException } from '@nestjs/common';
import { Between, Not, IsNull } from 'typeorm';
import { StopEntity } from '../../domain/entity/stop.entity';
import { StopRepository } from '../../infrastructure/repository/stop.repository';
import { TourRepository } from '../../infrastructure/repository/tour.repository';
import { PaginationMetaDto, PaginationParamsDto, QueryParamsDto } from '../dto/pagination.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';

@Injectable()
export class StopService {
  private readonly paginationAdapter: PaginationAdapter<StopEntity>;

  constructor(
    private readonly stopRepository: StopRepository,
    private readonly tourRepository: TourRepository,
  ) {
    this.paginationAdapter = new PaginationAdapter(stopRepository, StopEntity);
  }

  /**
   * Récup<PERSON> les stops avec pagination pour une date de livraison spécifique
   */
  async getPaginatedStopsByDeliveryDate(
    date: string,
    pagination: PaginationParamsDto,
    filters: Record<string, string> = {},
  ): Promise<{ items: StopEntity[]; meta: PaginationMetaDto }> {
    // Build base where conditions
    const whereConditions: any = {
      tour: {
        deliveryDate: date,
      },
    };

    // Handle hasEquipmentReturns filter
    if (filters.hasEquipmentReturns === 'true') {
      // Filter for stops that have at least one equipment return value
      whereConditions.completion = [
        { palletCount: Not(IsNull()) },
        { rollCount: Not(IsNull()) },
        { packageCount: Not(IsNull()) },
      ];
      // Remove the filter from the filters object to prevent it from being processed by FilterEngine
      const { hasEquipmentReturns: _hasEquipmentReturns, ...remainingFilters } = filters;
      filters = remainingFilters;
    }

    return this.paginationAdapter.paginate(
      pagination,
      {
        where: whereConditions,
        relations: {
          tour: {
            tourIdentifier: true,
          },
          client: true,
          completion: true,
          shipmentLines: true,
          deliveryNotes: {
            file: true,
          },
        },
      },
      filters,
    );
  }

  /**
   * Récupère les stops d'aujourd'hui avec pagination
   */
  async getTodayStops(
    pagination: PaginationParamsDto,
    filters: Record<string, string> = {},
  ): Promise<{ items: StopEntity[]; meta: PaginationMetaDto }> {
    const today = new Date().toISOString().split('T')[0];

    return this.getPaginatedStopsByDeliveryDate(today, pagination, filters);
  }

  /**
   * Récupère les stops pour une plage de dates
   */
  async getPaginatedStopsByDateRange(
    startDate: string,
    endDate: string,
    pagination: PaginationParamsDto,
    filters: Record<string, string> = {},
  ): Promise<{ items: StopEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(
      pagination,
      {
        where: {
          tour: {
            deliveryDate: Between(startDate, endDate),
          },
        },
        relations: {
          tour: {
            tourIdentifier: true,
          },
          client: true,
          completion: true,
          shipmentLines: true,
          deliveryNotes: {
            file: true,
          },
        },
      },
      filters,
    );
  }

  /**
   * Récupère les stops d'une tournée spécifique avec pagination
   */
  async getStopsByTourId(
    tourId: string,
    pagination: PaginationParamsDto,
    filters: Record<string, string> = {},
  ): Promise<{ items: StopEntity[]; meta: PaginationMetaDto }> {
    // Verify that the tour exists
    const tour = await this.tourRepository.findOne({
      where: { id: tourId },
    });

    if (!tour) {
      throw new NotFoundException(`Tour with ID ${tourId} not found`);
    }

    // Set default order by sequenceInTour if not already set
    const paginationWithOrder = Object.assign(new QueryParamsDto(), {
      ...pagination,
      sortBy: pagination.sortBy || 'sequenceInTour',
      sortOrder: pagination.sortOrder || 'asc',
    });

    return this.paginationAdapter.paginate(
      paginationWithOrder,
      {
        where: {
          tourId: tourId,
        },
        relations: {
          tour: {
            tourIdentifier: true,
          },
          client: true,
          completion: true,
          shipmentLines: true,
          deliveryNotes: {
            file: true,
          },
        },
      },
      filters,
    );
  }
}
