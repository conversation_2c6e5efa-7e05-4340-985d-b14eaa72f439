import { Injectable, NotFoundException } from '@nestjs/common';
import { LogisticsEquipmentTypeRepository } from '../../infrastructure/repository/logistics-equipment-type.repository';
import { LogisticsEquipmentTypeEntity } from '../../domain/entity/logistics-equipment-type.entity';
import { CreateLogisticsEquipmentTypeDto } from '../dto/logistics-equipment-type/create-logistics-equipment-type.dto';
import { UpdateLogisticsEquipmentTypeDto } from '../dto/logistics-equipment-type/update-logistics-equipment-type.dto';
import { PaginationMetaDto, PaginationParamsDto } from '../dto/pagination.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';

@Injectable()
export class LogisticsEquipmentTypeService {
  private readonly paginationAdapter: PaginationAdapter<LogisticsEquipmentTypeEntity>;

  constructor(private readonly logisticsEquipmentTypeRepository: LogisticsEquipmentTypeRepository) {
    this.paginationAdapter = new PaginationAdapter(logisticsEquipmentTypeRepository, LogisticsEquipmentTypeEntity);
  }

  async findAll(): Promise<LogisticsEquipmentTypeEntity[]> {
    return this.logisticsEquipmentTypeRepository.find({
      order: { name: 'ASC' },
    });
  }

  async findAllPaginated(pagination: PaginationParamsDto): Promise<{
    items: LogisticsEquipmentTypeEntity[];
    meta: PaginationMetaDto;
  }> {
    return this.paginationAdapter.paginate(pagination);
  }

  async findById(id: string): Promise<LogisticsEquipmentTypeEntity> {
    const entity = await this.logisticsEquipmentTypeRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new NotFoundException(`Logistics equipment type with ID ${id} not found`);
    }

    return entity;
  }

  async create(dto: CreateLogisticsEquipmentTypeDto): Promise<LogisticsEquipmentTypeEntity> {
    const entity = this.logisticsEquipmentTypeRepository.create(dto);

    return this.logisticsEquipmentTypeRepository.save(entity);
  }

  async update(id: string, dto: UpdateLogisticsEquipmentTypeDto): Promise<LogisticsEquipmentTypeEntity> {
    await this.findById(id);
    await this.logisticsEquipmentTypeRepository.update(id, dto);

    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.findById(id);
    await this.logisticsEquipmentTypeRepository.delete(id);
  }
}
