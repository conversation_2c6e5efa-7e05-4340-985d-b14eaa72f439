import { ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { UserEntity } from '../../domain/entity/user.entity';
import { UserRepository } from '../../infrastructure/repository/user.repository';
import { KeycloakAdminService } from '../../infrastructure/service/keycloak-admin.service';
import { CreateUserRequest } from '../dto/user/create-user-request';
import { PatchCurrentUserRequest } from '../dto/user/patch-current-user-request';
import { UpdateUserRequest } from '../dto/user/update-user-request.dto';
import { ColorService } from './color.service';
import { UserQueryService } from './user-query.service';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly keycloakAdminService: KeycloakAdminService,
    private readonly colorService: ColorService,
    private readonly userQueryService: UserQueryService,
  ) {}

  async createUser(createUserDto: CreateUserRequest): Promise<UserEntity> {
    this.logger.log(`Creating user with username: ${createUserDto.username}`);

    const existingUser = await this.userRepository.findOne({
      where: [{ username: createUserDto.username }, { email: createUserDto.email }],
    });

    if (existingUser) {
      throw new ConflictException('User with this username or email already exists');
    }

    const color = createUserDto.color || this.colorService.generateColorFromId(createUserDto.username);

    let keycloakUserId: string | null = null;

    try {
      if (createUserDto.createInKeycloak) {
        this.logger.log(`Creating user in Keycloak: ${createUserDto.email}`);

        const keycloakUser = await this.keycloakAdminService.createUser(
          createUserDto.email,
          createUserDto.firstName || '',
          createUserDto.lastName || '',
          createUserDto.locale || 'fr',
        );

        if (!keycloakUser?.id) {
          throw new Error('Failed to create user in Keycloak');
        }

        keycloakUserId = keycloakUser.id;

        if (createUserDto.password) {
          await this.keycloakAdminService.setUserPassword(keycloakUser.id, createUserDto.password);
          this.logger.log(`Password set for Keycloak user: ${keycloakUser.id}`);
        }

        if (createUserDto.role) {
          await this.keycloakAdminService.syncUserRoles(keycloakUser.id, [createUserDto.role]);
          this.logger.log(`Role ${createUserDto.role} synced to Keycloak user: ${keycloakUser.id}`);
        }
      }

      const userEntity = this.userRepository.create({
        username: createUserDto.username,
        email: createUserDto.email,
        firstName: createUserDto.firstName,
        lastName: createUserDto.lastName,
        locale: createUserDto.locale || 'fr',
        color,
        isSSO: !createUserDto.createInKeycloak,
        roles: createUserDto.role ? [createUserDto.role] : [],
      });

      const savedUser = await this.userRepository.save(userEntity);
      this.logger.log(`User created successfully with ID: ${savedUser.id}`);

      return await this.userQueryService.getOneById(savedUser.id);
    } catch (error) {
      if (keycloakUserId && createUserDto.createInKeycloak) {
        try {
          await this.keycloakAdminService.deleteUser(keycloakUserId);
          this.logger.log(`Rolled back Keycloak user creation: ${keycloakUserId}`);
        } catch (rollbackError) {
          this.logger.error(`Failed to rollback Keycloak user creation: ${rollbackError}`);
        }
      }

      this.logger.error(`Failed to create user: ${error}`);
      throw error;
    }
  }

  async updateUser(userId: string, updateUserDto: PatchCurrentUserRequest | UpdateUserRequest): Promise<UserEntity> {
    const user = await this.userQueryService.getOneById(userId);

    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    const keycloakUser = await this.keycloakAdminService.getUserByUsername(user.username).catch(() => null);

    if (keycloakUser) {
      if ('firstName' in updateUserDto && updateUserDto.firstName) {
        keycloakUser.firstName = updateUserDto.firstName;
      }

      if ('lastName' in updateUserDto && updateUserDto.lastName) {
        keycloakUser.lastName = updateUserDto.lastName;
      }

      if ('locale' in updateUserDto && updateUserDto.locale) {
        keycloakUser.attributes.locale = [updateUserDto.locale];
      }
      await this.keycloakAdminService.updateUser(keycloakUser);
    }

    if ('color' in updateUserDto && updateUserDto.color) {
      user.color = updateUserDto.color;
    }

    if ('role' in updateUserDto && updateUserDto.role) {
      user.roles = [updateUserDto.role];

      if (keycloakUser) {
        await this.keycloakAdminService.syncUserRoles(keycloakUser.id, [updateUserDto.role]);
        this.logger.log(`Synced user role to ${updateUserDto.role} in Keycloak`);
      }
    }

    await this.userRepository.save(user);

    return await this.userQueryService.getOneById(userId);
  }

  async resetUserPassword(userId: string, newPassword: string, temporary: boolean = false): Promise<void> {
    const user = await this.userQueryService.getOneById(userId);

    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    const keycloakUser = await this.keycloakAdminService.getUserByUsername(user.username);

    await this.keycloakAdminService.setUserPassword(keycloakUser.id, newPassword, temporary);

    this.logger.log(`Password reset for user ${userId} (${user.username}) - temporary: ${temporary}`);
  }
}
