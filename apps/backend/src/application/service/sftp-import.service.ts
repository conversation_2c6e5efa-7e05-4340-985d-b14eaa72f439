import { Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import * as path from 'path';
import { SftpService } from '../../infrastructure/service/sftp.service';

@Injectable()
export class SftpImportService {
  private readonly importRoot = process.env.SFTP_IMPORT_ROOT_PATH || '/';

  constructor(private readonly sftpService: SftpService) {}

  async readXmlFilesForDate(date: string): Promise<{ fileName: string; xmlContent: string }[]> {
    const dir = path.posix.join(this.importRoot, DateTime.fromISO(date).toISODate() || date);
    const files = await this.sftpService.listFiles(dir);
    const xmlFiles = files.filter((f) => f.endsWith('.xml'));
    const result: { fileName: string; xmlContent: string }[] = [];

    for (const fileName of xmlFiles) {
      const buffer = await this.sftpService.readFile(path.posix.join(dir, fileName));

      if (buffer) {
        result.push({ fileName, xmlContent: buffer.toString('utf8') });
      }
    }

    return result;
  }

  async readFileForDate(date: string, filename: string): Promise<Buffer | null> {
    const dir = path.posix.join(this.importRoot, DateTime.fromISO(date).toISODate() || date);

    return this.sftpService.readFile(path.posix.join(dir, filename));
  }
}
