import { Test, TestingModule } from '@nestjs/testing';
import { ColorService } from '../color.service';

describe('ColorService', () => {
  let service: ColorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ColorService],
    }).compile();

    service = module.get<ColorService>(ColorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateColorFromId', () => {
    it('should return the same color for the same ID', () => {
      const id = 'test-user-123';
      const color1 = service.generateColorFromId(id);
      const color2 = service.generateColorFromId(id);

      expect(color1).toBe(color2);
    });

    it('should return different colors for different IDs', () => {
      const color1 = service.generateColorFromId('user-1');
      const color2 = service.generateColorFromId('user-2');

      expect(color1).not.toBe(color2);
    });

    it('should return a valid hex color', () => {
      const color = service.generateColorFromId('test-user');

      expect(color).toMatch(/^#[0-9A-F]{6}$/i);
    });

    it('should return a color from the palette', () => {
      const color = service.generateColorFromId('test-user');
      const palette = service.getColorPalette();

      expect(palette).toContain(color);
    });
  });

  describe('getColorPalette', () => {
    it('should return an array of hex colors', () => {
      const palette = service.getColorPalette();

      expect(Array.isArray(palette)).toBe(true);
      expect(palette.length).toBeGreaterThan(0);
      palette.forEach((color) => {
        expect(color).toMatch(/^#[0-9A-F]{6}$/i);
      });
    });

    it('should return a copy of the palette', () => {
      const palette1 = service.getColorPalette();
      const palette2 = service.getColorPalette();

      expect(palette1).not.toBe(palette2);
      expect(palette1).toEqual(palette2);
    });
  });

  describe('deterministic behavior', () => {
    it('should generate consistent colors across service instances', async () => {
      const module1: TestingModule = await Test.createTestingModule({
        providers: [ColorService],
      }).compile();
      const service1 = module1.get<ColorService>(ColorService);

      const module2: TestingModule = await Test.createTestingModule({
        providers: [ColorService],
      }).compile();
      const service2 = module2.get<ColorService>(ColorService);

      const testId = 'consistent-test-id';
      const color1 = service1.generateColorFromId(testId);
      const color2 = service2.generateColorFromId(testId);

      expect(color1).toBe(color2);
    });
  });
});
