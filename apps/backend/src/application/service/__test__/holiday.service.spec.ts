import { HolidayRepository } from '../../../infrastructure/repository/holiday.repository';
import { HolidayService } from '../holiday.service';

describe('HolidayService', () => {
  describe('ensureHolidaysPopulated', () => {
    it('inserts holidays when none exist', async () => {
      const mockRepo: Partial<HolidayRepository> = {
        find: jest.fn().mockResolvedValue([]),
        save: jest.fn(),
      };
      const service = new HolidayService(mockRepo as HolidayRepository);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01T00:00:00Z'));

      await service.ensureHolidaysPopulated();

      expect(mockRepo.save).toHaveBeenCalled();
      // 11 holidays for two years
      expect((mockRepo.save as jest.Mock).mock.calls.length).toBe(22);
      expect((mockRepo.save as jest.Mock).mock.calls[0][0]).toEqual(
        expect.objectContaining({
          name: "<PERSON><PERSON> de l'<PERSON>",
          year: 2024,
        }),
      );

      jest.useRealTimers();
    });
  });

  describe('calculateEaster', () => {
    const service = new HolidayService({} as HolidayRepository);

    it('calculates earliest possible Easter (1818)', () => {
      const date = (service as any).calculateEaster(1818);
      expect(date.toISOString().split('T')[0]).toBe('1818-03-22');
    });

    it('calculates latest possible Easter (1943)', () => {
      const date = (service as any).calculateEaster(1943);
      expect(date.toISOString().split('T')[0]).toBe('1943-04-25');
    });

    it('calculates Easter for leap year 2016', () => {
      const date = (service as any).calculateEaster(2016);
      expect(date.toISOString().split('T')[0]).toBe('2016-03-27');
    });

    it('calculates Easter for year 2000', () => {
      const date = (service as any).calculateEaster(2000);
      expect(date.toISOString().split('T')[0]).toBe('2000-04-23');
    });
  });
});
