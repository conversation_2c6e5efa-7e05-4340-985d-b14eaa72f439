import { Test, TestingModule } from '@nestjs/testing';
import { TourAssignmentEntity } from '../../../domain/entity/tour-assignment.entity';
import { TourIdentifier } from '../../../domain/entity/tour-identifier';
import { TourType } from '../../../domain/enum/tour.enums';
import { TourAssignmentRepository } from '../../../infrastructure/repository/tour-assignment.repository';
import { CreateTourAssignmentDto } from '../../dto/tour-assignment/tour-assignment.dto';
import { TourAssignmentMapper } from '../../mappers/tour-assignment.mapper';
import { TourAssignmentService } from '../tour-assignment.service';

describe('TourAssignmentService - Assignment Fusion Logic', () => {
  let service: TourAssignmentService;
  let repository: jest.Mocked<TourAssignmentRepository>;
  let mapper: jest.Mocked<TourAssignmentMapper>;

  beforeEach(async () => {
    const mockRepository = {
      findMergeableAssignments: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockMapper = {
      toEntity: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TourAssignmentService,
        {
          provide: TourAssignmentRepository,
          useValue: mockRepository,
        },
        {
          provide: TourAssignmentMapper,
          useValue: mockMapper,
        },
      ],
    }).compile();

    service = module.get<TourAssignmentService>(TourAssignmentService);
    repository = module.get(TourAssignmentRepository);
    mapper = module.get(TourAssignmentMapper);
  });

  describe('create with duplicate prevention', () => {
    const createDto: CreateTourAssignmentDto = {
      tourIdentifier: {
        number: '4012',
        type: TourType.Normal,
        originalNumber: '4012',
      },
      fromDate: '2024-01-15',
      toDate: '2024-01-20',
      userId: 'user-123',
    };

    const createAssignment = (fromDate: string, toDate?: string): TourAssignmentEntity => {
      const assignment = new TourAssignmentEntity();
      assignment.id = 'assignment-123';
      assignment.tourIdentifier = new TourIdentifier();
      assignment.fromDate = fromDate;
      assignment.toDate = toDate;
      assignment.userId = 'user-123';

      return assignment;
    };

    it('should extend existing assignment dates when both have toDate', async () => {
      const existingAssignment = createAssignment('2024-01-10', '2024-01-18');
      const newAssignment = createAssignment('2024-01-15', '2024-01-25');

      mapper.toEntity.mockResolvedValue(newAssignment);
      repository.findMergeableAssignments.mockResolvedValue([existingAssignment]);
      repository.save.mockResolvedValue(existingAssignment);

      await service.create({ ...createDto, toDate: '2024-01-25' });

      expect(existingAssignment.fromDate).toBe('2024-01-10');
      expect(existingAssignment.toDate).toBe('2024-01-25');
    });

    it('should make existing assignment open-ended when new is open-ended', async () => {
      const existingAssignment = createAssignment('2024-01-10', '2024-01-18');
      const newAssignment = createAssignment('2024-01-15');

      mapper.toEntity.mockResolvedValue(newAssignment);
      repository.findMergeableAssignments.mockResolvedValue([existingAssignment]);
      repository.save.mockResolvedValue(existingAssignment);

      await service.create({ ...createDto, toDate: undefined });

      expect(existingAssignment.toDate).toBeUndefined();
    });

    it('should create new assignment when no mergeable assignments found', async () => {
      const newAssignment = createAssignment('2024-01-15', '2024-01-20');

      mapper.toEntity.mockResolvedValue(newAssignment);
      repository.findMergeableAssignments.mockResolvedValue([]);
      repository.save.mockResolvedValue(newAssignment);

      const result = await service.create(createDto);

      expect(result).toBe(newAssignment);
      expect(repository.save).toHaveBeenCalledWith(newAssignment);
    });

    it('should merge multiple separate assignments when filling gap', async () => {
      // Scenario: assignments on 20th and 22nd, adding 21st should merge all three
      const assignment20 = createAssignment('2024-01-20', '2024-01-20');
      const assignment22 = createAssignment('2024-01-22', '2024-01-22');
      const newAssignment21 = createAssignment('2024-01-21', '2024-01-21');

      mapper.toEntity.mockResolvedValue(newAssignment21);
      repository.findMergeableAssignments.mockResolvedValue([assignment20, assignment22]);
      repository.save.mockResolvedValue(assignment20);
      repository.delete.mockResolvedValue({ affected: 1, raw: [] });

      const result = await service.create({
        ...createDto,
        fromDate: '2024-01-21',
        toDate: '2024-01-21',
      });

      expect(assignment20.fromDate).toBe('2024-01-20');
      expect(assignment20.toDate).toBe('2024-01-22');
      expect(repository.delete).toHaveBeenCalledWith(assignment22.id);
      expect(result).toBe(assignment20);
    });
  });
});

describe('TourAssignmentService - Update with Conflict Prevention', () => {
  let service: TourAssignmentService;
  let repository: jest.Mocked<TourAssignmentRepository>;
  let mapper: jest.Mocked<TourAssignmentMapper>;

  beforeEach(async () => {
    const mockRepository = {
      findMergeableAssignments: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
      findOne: jest.fn(),
    };

    const mockMapper = {
      toEntity: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TourAssignmentService,
        {
          provide: TourAssignmentRepository,
          useValue: mockRepository,
        },
        {
          provide: TourAssignmentMapper,
          useValue: mockMapper,
        },
      ],
    }).compile();

    service = module.get<TourAssignmentService>(TourAssignmentService);
    repository = module.get(TourAssignmentRepository);
    mapper = module.get(TourAssignmentMapper);
  });

  describe('update with userId change', () => {
    it('should allow userId change when no conflicts exist', async () => {
      const existingAssignment = new TourAssignmentEntity();
      existingAssignment.id = 'existing-id';
      existingAssignment.userId = 'user-1';
      existingAssignment.fromDate = '2024-01-10';
      existingAssignment.toDate = '2024-01-15';
      existingAssignment.tourIdentifier = TourIdentifier.fromDto({
        number: '4001',
        type: TourType.Normal,
        originalNumber: '4001',
      });

      const updateDto = { userId: 'user-2' };

      repository.findOne.mockResolvedValue(existingAssignment);
      repository.findMergeableAssignments.mockResolvedValue([]); // No conflicts

      const updatedAssignment = { ...existingAssignment, userId: 'user-2' };
      mapper.toEntity.mockResolvedValue(updatedAssignment);
      repository.save.mockResolvedValue(updatedAssignment);

      const result = await service.update('existing-id', updateDto);

      expect(repository.findMergeableAssignments).toHaveBeenCalledWith(
        '4001',
        TourType.Normal,
        '4001',
        'user-2',
        '2024-01-10',
        '2024-01-15',
      );
      expect(result).toBe(updatedAssignment);
    });

    it('should reject userId change when conflicts exist', async () => {
      const existingAssignment = new TourAssignmentEntity();
      existingAssignment.id = 'existing-id';
      existingAssignment.userId = 'user-1';
      existingAssignment.fromDate = '2024-01-10';
      existingAssignment.toDate = '2024-01-15';
      existingAssignment.tourIdentifier = TourIdentifier.fromDto({
        number: '4001',
        type: TourType.Normal,
        originalNumber: '4001',
      });

      const conflictingAssignment = new TourAssignmentEntity();
      conflictingAssignment.userId = 'user-2';
      conflictingAssignment.fromDate = '2024-01-12';
      conflictingAssignment.toDate = '2024-01-18';

      const updateDto = { userId: 'user-2' };

      repository.findOne.mockResolvedValue(existingAssignment);
      repository.findMergeableAssignments.mockResolvedValue([conflictingAssignment]);

      await expect(service.update('existing-id', updateDto)).rejects.toThrow(
        'Cannot update assignment: User user-2 already has conflicting assignments for this tour during the specified period',
      );
    });

    it('should allow userId change when same user', async () => {
      const existingAssignment = new TourAssignmentEntity();
      existingAssignment.id = 'existing-id';
      existingAssignment.userId = 'user-1';
      existingAssignment.fromDate = '2024-01-10';
      existingAssignment.toDate = '2024-01-15';

      const updateDto = { userId: 'user-1', notes: 'Updated notes' };

      repository.findOne.mockResolvedValue(existingAssignment);

      const updatedAssignment = {
        ...existingAssignment,
        notes: 'Updated notes',
      };
      mapper.toEntity.mockResolvedValue(updatedAssignment);
      repository.save.mockResolvedValue(updatedAssignment);

      const result = await service.update('existing-id', updateDto);

      // Should not check for conflicts when userId hasn't changed
      expect(repository.findMergeableAssignments).not.toHaveBeenCalled();
      expect(result).toBe(updatedAssignment);
    });
  });
});
