import { Injectable } from '@nestjs/common';
import { createHash } from 'crypto';

@Injectable()
export class ColorService {
  // Palette de couleurs avec bon contraste pour du texte blanc
  private readonly colorPalette = [
    '#E53E3E', // Rouge
    '#D69E2E', // Orange/Jaune
    '#38A169', // Vert
    '#3182CE', // Bleu
    '#805AD5', // Violet
    '#D53F8C', // Rose
    '#00B5D8', // Cyan
    '#DD6B20', // Orange foncé
    '#319795', // Teal
    '#C53030', // Rouge carmin
    '#9F7AEA', // Violet clair
    '#48BB78', // Vert clair
  ];

  /**
   * Génère une couleur basée sur un ID string en utilisant MD5 + modulo
   * Garantit la cohérence : même ID = même couleur
   */
  generateColorFromId(id: string): string {
    if (!id) {
      return this.colorPalette[0];
    }

    // Génération du hash MD5
    const hash = createHash('md5').update(id).digest('hex');

    // Conversion des premiers 8 caractères du hash en nombre
    const hashNumber = parseInt(hash.substring(0, 8), 16);

    // Modulo pour obtenir un index de couleur
    const colorIndex = hashNumber % this.colorPalette.length;

    return this.colorPalette[colorIndex];
  }

  /**
   * Retourne la palette de couleurs disponibles
   */
  getColorPalette(): string[] {
    return [...this.colorPalette];
  }

  /**
   * Vérifie si une couleur fait partie de la palette
   */
  isValidColor(color: string): boolean {
    return this.colorPalette.includes(color);
  }
}
