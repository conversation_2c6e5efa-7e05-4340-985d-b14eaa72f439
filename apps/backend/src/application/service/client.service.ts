import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationMetaDto, PaginationParamsDto } from '../dto/pagination.dto';
import { ClientEntity } from '../../domain/entity/client.entity';
import { ClientRepository } from '../../infrastructure/repository/client.repository';
import { UpdateClientDto } from '../dto/client/update-client.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';
import { Address } from '../../domain/entity/address';

@Injectable()
export class ClientService {
  private readonly paginationAdapter: PaginationAdapter<ClientEntity>;

  constructor(private readonly clientRepository: ClientRepository) {
    this.paginationAdapter = new PaginationAdapter(clientRepository, ClientEntity);
  }

  async findAll(pagination: PaginationParamsDto): Promise<{ items: ClientEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(pagination);
  }

  async findOne(id: string): Promise<ClientEntity> {
    const client = await this.clientRepository.findOne({ where: { id } });

    if (!client) {
      throw new NotFoundException(`Client with ID ${id} not found`);
    }

    return client;
  }

  async update(id: string, updateDto: UpdateClientDto): Promise<ClientEntity> {
    const client = await this.findOne(id);

    if (updateDto.email !== undefined) {
      client.email = updateDto.email;
    }

    if (updateDto.address) {
      const address = new Address();

      if (updateDto.address.line1 !== undefined) {
        address.line1 = updateDto.address.line1;
      } else if (client.address?.line1) {
        address.line1 = client.address.line1;
      }

      if (updateDto.address.line2 !== undefined) {
        address.line2 = updateDto.address.line2;
      } else if (client.address?.line2) {
        address.line2 = client.address.line2;
      }

      if (updateDto.address.line3 !== undefined) {
        address.line3 = updateDto.address.line3;
      } else if (client.address?.line3) {
        address.line3 = client.address.line3;
      }

      if (updateDto.address.line4 !== undefined) {
        address.line4 = updateDto.address.line4;
      } else if (client.address?.line4) {
        address.line4 = client.address.line4;
      }

      client.address = address;
    }

    return this.clientRepository.save(client);
  }
}
