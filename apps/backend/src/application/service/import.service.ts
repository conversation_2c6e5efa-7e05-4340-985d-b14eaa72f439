import { Injectable, NotFoundException } from '@nestjs/common';
import { Between, LessThanOrEqual, MoreThanOrEqual, Not } from 'typeorm';
import { ImportEntity } from '../../domain/entity/import.entity';
import { ImportStatus } from '../../domain/enum/import-status.enum';
import { ImportRepository } from '../../infrastructure/repository/import.repository';
import { TourRepository } from '../../infrastructure/repository/tour.repository';
import { ImportListParamsDto } from '../dto/import/import-list-params.dto';
import { PaginationMetaDto, QueryParamsDto } from '../dto/pagination.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';

export interface ImportResult {
  success: boolean;
  importId: string;
  totalRecords: number;
  processedRecords: number;
  failedRecords: number;
  errors?: Array<{ recordId?: string; message: string }>;
}

@Injectable()
export class ImportService {
  private readonly paginationAdapter: PaginationAdapter<ImportEntity>;

  constructor(
    private readonly importRepository: ImportRepository,
    private readonly tourRepository: TourRepository,
  ) {
    this.paginationAdapter = new PaginationAdapter(importRepository, ImportEntity);
  }

  async getImportStatus(id: string): Promise<ImportEntity> {
    const importEntity = await this.importRepository.findOne({
      where: { id },
      relations: ['tours'],
    });

    if (!importEntity) {
      throw new NotFoundException('Import not found');
    }

    return importEntity;
  }

  async getImportList(params: ImportListParamsDto): Promise<{ items: ImportEntity[]; meta: PaginationMetaDto }> {
    const { startDate, endDate, status, ...paginationFields } = params;

    // Build where conditions for date filtering
    const whereConditions: any = {};

    if (startDate && endDate) {
      whereConditions.importDate = Between(startDate, endDate);
    } else if (startDate) {
      whereConditions.importDate = MoreThanOrEqual(startDate);
    } else if (endDate) {
      whereConditions.importDate = LessThanOrEqual(endDate);
    }

    // Extract pagination parameters
    const queryParams = Object.assign(new QueryParamsDto(), paginationFields);

    // Build filters object for status
    const filters: Record<string, any> = {};

    if (status) {
      filters.status = status;
    }

    const hasStatusFilter = Boolean(status);

    if (!hasStatusFilter) {
      whereConditions.status = Not(ImportStatus.Skipped);
    }

    return this.paginationAdapter.paginate(
      queryParams,
      {
        relations: ['tours'],
        where: whereConditions,
      },
      filters,
    );
  }

  async getImportDetails(id: string): Promise<ImportEntity> {
    const importEntity = await this.importRepository
      .createQueryBuilder('import')
      .addSelect('import.importedFiles')
      .leftJoinAndSelect('import.tours', 'tours')
      .where('import.id = :id', { id })
      .getOne();

    if (!importEntity) {
      throw new NotFoundException('Import not found');
    }

    return importEntity;
  }
}
