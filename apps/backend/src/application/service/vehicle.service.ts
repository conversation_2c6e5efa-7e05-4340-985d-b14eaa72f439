import { Injectable, NotFoundException } from '@nestjs/common';
import { VehicleRepository } from '../../infrastructure/repository/vehicle.repository';
import { VehicleEntity } from '../../domain/entity/vehicle.entity';
import { CreateVehicleDto } from '../dto/vehicle/create-vehicle.dto';
import { UpdateVehicleDto } from '../dto/vehicle/update-vehicle.dto';
import { PaginationMetaDto, PaginationParamsDto } from '../dto/pagination.dto';
import { PaginationAdapter } from '../pagination/pagination-adapter';

@Injectable()
export class VehicleService {
  private readonly paginationAdapter: PaginationAdapter<VehicleEntity>;

  constructor(private readonly vehicleRepository: VehicleRepository) {
    this.paginationAdapter = new PaginationAdapter(vehicleRepository, VehicleEntity);
  }

  async findAll(): Promise<VehicleEntity[]> {
    return this.vehicleRepository.find({
      order: { licensePlate: 'ASC' },
    });
  }

  async findAllPaginated(
    pagination: PaginationParamsDto,
  ): Promise<{ items: VehicleEntity[]; meta: PaginationMetaDto }> {
    return this.paginationAdapter.paginate(pagination);
  }

  async findById(id: string): Promise<VehicleEntity> {
    const entity = await this.vehicleRepository.findOne({
      where: { id },
    });

    if (!entity) {
      throw new NotFoundException(`Vehicle with ID ${id} not found`);
    }

    return entity;
  }

  async create(dto: CreateVehicleDto): Promise<VehicleEntity> {
    const entity = this.vehicleRepository.create(dto);

    return this.vehicleRepository.save(entity);
  }

  async update(id: string, dto: UpdateVehicleDto): Promise<VehicleEntity> {
    await this.findById(id);
    await this.vehicleRepository.update(id, dto);

    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.findById(id);
    await this.vehicleRepository.delete(id);
  }
}
