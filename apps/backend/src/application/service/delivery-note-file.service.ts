import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import { DateTime } from 'luxon';
import * as path from 'path';
import { FileEntity } from '../../domain/entity/file.entity';
import { ImportSourceType } from '../../domain/enum/import-source-type.enum';
import { FileRepository } from '../../infrastructure/repository/file.repository';
import { S3Service } from '../../infrastructure/service/s3.service';
import { SftpImportService } from './sftp-import.service';
import { FileService } from './file.service';

@Injectable()
export class DeliveryNoteFileService {
  private readonly logger = new Logger(DeliveryNoteFileService.name);

  constructor(
    private readonly fileRepository: FileRepository,
    private readonly s3Service: S3Service,
    private readonly sftpImportService: SftpImportService,
    private readonly fileService: FileService,
  ) {}

  async uploadDeliveryNotePdf(
    filename: string,
    importContext: { importDate: string; importSourceType: ImportSourceType },
  ): Promise<FileEntity | null> {
    try {
      const pdfBuffer = await this.readPdfFile(filename, importContext);

      if (!pdfBuffer) {
        this.logger.warn(`PDF file not found: ${filename}`);

        return null;
      }

      return await this.createAndUploadFile(filename, pdfBuffer, importContext);
    } catch (error) {
      this.logger.error(`Error uploading PDF ${filename}:`, error);

      return null;
    }
  }

  private async readPdfFile(
    filename: string,
    importContext: { importDate: string; importSourceType: ImportSourceType },
  ): Promise<Buffer | null> {
    if (importContext.importSourceType === ImportSourceType.FILESYSTEM) {
      return this.readFromFilesystem(filename, importContext.importDate);
    } else if (importContext.importSourceType === ImportSourceType.FTP) {
      return this.readFromSFtp(filename, importContext.importDate);
    } else {
      this.logger.warn(`Unknown import source type: ${importContext.importSourceType}`);

      return null;
    }
  }

  private readFromFilesystem(filename: string, importDate: string): Buffer | null {
    const parsedDate = DateTime.fromISO(importDate).toFormat('yyyyMMdd');
    const dataDir = path.join(__dirname, '../../../data', parsedDate);
    const pdfPath = path.join(dataDir, filename);

    if (!fs.existsSync(pdfPath)) {
      this.logger.warn(`PDF file not found: ${pdfPath}`);

      return null;
    }

    return fs.readFileSync(pdfPath);
  }

  private async readFromSFtp(filename: string, importDate: string): Promise<Buffer | null> {
    const buffer = await this.sftpImportService.readFileForDate(importDate, filename);

    if (!buffer) {
      this.logger.warn(`PDF file not found on FTP: ${filename}`);

      return null;
    }

    return buffer;
  }

  private async createAndUploadFile(
    filename: string,
    pdfBuffer: Buffer,
    importContext: { importDate: string; importSourceType: ImportSourceType },
  ): Promise<FileEntity> {
    // Generate folder based on import date (deliveryNote/YYYY-MM)
    const importDate = DateTime.fromISO(importContext.importDate);
    const folder = `deliveryNote/${importDate.toFormat('yyyy-MM')}`;

    const multerFile: Express.Multer.File = {
      buffer: pdfBuffer,
      originalname: filename,
      mimetype: 'application/pdf',
      size: pdfBuffer.length,
      filename,
      fieldname: 'file',
      encoding: '7bit',
      stream: null,
      destination: '',
      path: '',
    };

    // Use FileService instead of direct S3Service
    return await this.fileService.createFile({
      file: multerFile,
      folder,
      metadata: {
        importDate: importContext.importDate,
        importSourceType: importContext.importSourceType,
      },
      isPublic: false,
    });
  }
}
