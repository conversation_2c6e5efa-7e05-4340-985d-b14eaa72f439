import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { HolidayEntity } from '../../domain/entity/holiday.entity';
import { HolidayRepository } from '../../infrastructure/repository/holiday.repository';

@Injectable()
export class HolidayService {
  private readonly logger = new Logger(HolidayService.name);

  constructor(private readonly holidayRepository: HolidayRepository) {}

  async findAll(): Promise<HolidayEntity[]> {
    return this.holidayRepository.find({
      order: { date: 'ASC' },
    });
  }

  async findByYear(year: number): Promise<HolidayEntity[]> {
    return this.holidayRepository.find({
      where: { year },
      order: { date: 'ASC' },
    });
  }

  async findOne(id: string): Promise<HolidayEntity> {
    const holiday = await this.holidayRepository.findOneBy({ id });

    if (!holiday) {
      throw new NotFoundException(`Holiday with ID ${id} not found`);
    }

    return holiday;
  }

  async ensureHolidaysPopulated(): Promise<void> {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;

    await this.ensureHolidaysForYear(currentYear);
    await this.ensureHolidaysForYear(nextYear);
  }

  private async ensureHolidaysForYear(year: number): Promise<void> {
    const existingHolidays = await this.findByYear(year);

    if (existingHolidays.length === 0) {
      this.logger.log(`Populating French holidays for year ${year}...`);
      const holidays = this.calculateFrenchHolidays(year);

      for (const holiday of holidays) {
        await this.holidayRepository.save({
          name: holiday.name,
          date: holiday.date,
          year: year,
        });
      }

      this.logger.log(`Successfully populated ${holidays.length} French holidays for ${year}`);
    } else {
      this.logger.log(`Holidays for year ${year} already exist (${existingHolidays.length} holidays)`);
    }
  }

  private calculateFrenchHolidays(year: number): Array<{ name: string; date: Date }> {
    const holidays: Array<{ name: string; date: Date }> = [];

    holidays.push(
      { name: "Jour de l'An", date: new Date(year, 0, 1) },
      { name: 'Fête du Travail', date: new Date(year, 4, 1) },
      { name: 'Victoire 1945', date: new Date(year, 4, 8) },
      { name: 'Fête nationale', date: new Date(year, 6, 14) },
      { name: 'Assomption', date: new Date(year, 7, 15) },
      { name: 'Toussaint', date: new Date(year, 10, 1) },
      { name: 'Armistice 1918', date: new Date(year, 10, 11) },
      { name: 'Noël', date: new Date(year, 11, 25) },
    );

    const easterDate = this.calculateEaster(year);

    const easterMonday = new Date(easterDate);
    easterMonday.setDate(easterDate.getDate() + 1);
    holidays.push({ name: 'Lundi de Pâques', date: easterMonday });

    const ascension = new Date(easterDate);
    ascension.setDate(easterDate.getDate() + 39);
    holidays.push({ name: 'Ascension', date: ascension });

    const pentecostMonday = new Date(easterDate);
    pentecostMonday.setDate(easterDate.getDate() + 50);
    holidays.push({ name: 'Lundi de Pentecôte', date: pentecostMonday });

    return holidays.sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  private calculateEaster(year: number): Date {
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;

    return new Date(year, month - 1, day);
  }
}
