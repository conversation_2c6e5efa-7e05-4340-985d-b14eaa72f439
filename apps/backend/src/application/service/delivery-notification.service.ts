import { Injectable, Logger } from '@nestjs/common';
import { EmailService } from '../../infrastructure/service/email.service';
import { EmailTemplateRendererService } from './email-template-renderer.service';
import { Email } from '../../domain/model/email';

export interface DeliveryEmailData {
  clientEmail: string;
  clientName: string;
  stopId: string;
  deliveryDate: Date;
  tourNumber: string;
  deliveryAddress: string;
  completedAt: Date;
  deliveryStatus: string;
  hasSignature: boolean;
  hasPhoto: boolean;
  emailSource: 'operator_input' | 'client_entity' | 'original_info';
}

@Injectable()
export class DeliveryNotificationService {
  private readonly logger = new Logger(DeliveryNotificationService.name);

  constructor(
    private readonly emailService: EmailService,
    private readonly emailTemplateRenderer: EmailTemplateRendererService,
  ) {}

  async sendDeliveryNotification(data: {
    stopId: string;
    tourNumber: string;
    deliveryDate: Date;
    deliveryAddress: string;
    completedAt: Date;
    deliveryStatus: string;
    hasSignature: boolean;
    hasPhoto: boolean;
    operatorInputEmail: string | null;
    clientEntity: {
      id: string;
      name: string;
      email: string | null;
    } | null;
    originalClientInfo: {
      name: string | null;
      email: string | null;
    } | null;
  }): Promise<void> {
    try {
      // Résoudre l'email selon l'ordre de priorité
      const emailResolution = this.resolveClientEmail(
        data.operatorInputEmail,
        data.clientEntity,
        data.originalClientInfo,
      );

      if (!emailResolution) {
        this.logger.log(`No email found for stop ${data.stopId}, skipping notification`);

        return;
      }

      const deliveryEmailData: DeliveryEmailData = {
        clientEmail: emailResolution.email,
        clientName: emailResolution.clientName,
        stopId: data.stopId,
        deliveryDate: data.deliveryDate,
        tourNumber: data.tourNumber,
        deliveryAddress: data.deliveryAddress,
        completedAt: data.completedAt,
        deliveryStatus: data.deliveryStatus,
        hasSignature: data.hasSignature,
        hasPhoto: data.hasPhoto,
        emailSource: emailResolution.source,
      };

      const templateData = {
        clientName: deliveryEmailData.clientName,
        stopId: deliveryEmailData.stopId,
        deliveryDate: deliveryEmailData.deliveryDate,
        tourNumber: deliveryEmailData.tourNumber,
        deliveryAddress: deliveryEmailData.deliveryAddress,
        completedAt: deliveryEmailData.completedAt,
        deliveryStatus: deliveryEmailData.deliveryStatus,
        hasSignature: deliveryEmailData.hasSignature,
        hasPhoto: deliveryEmailData.hasPhoto,
      };

      const subject = this.emailTemplateRenderer.generateDeliveryConfirmationSubject(templateData);
      const html = this.emailTemplateRenderer.generateDeliveryConfirmationHtml(templateData);
      const text = this.emailTemplateRenderer.generateDeliveryConfirmationText(templateData);

      const email = new Email(deliveryEmailData.clientEmail, subject, html, text);

      await this.emailService.sendEmail(email);

      this.logger.log({
        message: 'Delivery notification sent successfully',
        stopId: data.stopId,
        recipientEmail: deliveryEmailData.clientEmail,
        emailSource: deliveryEmailData.emailSource,
        tourNumber: data.tourNumber,
      });
    } catch (error) {
      this.logger.error(`Failed to send delivery notification for stop ${data.stopId}: ${error.message}`, error.stack);

      // Ne pas lever l'erreur pour ne pas bloquer la complétion du stop
      // L'erreur est loggée pour monitoring
    }
  }

  private resolveClientEmail(
    operatorInputEmail: string | null,
    clientEntity: {
      id: string;
      name: string;
      email: string | null;
    } | null,
    originalClientInfo: {
      name: string | null;
      email: string | null;
    } | null,
  ): {
    email: string;
    clientName: string;
    source: 'operator_input' | 'client_entity' | 'original_info';
  } | null {
    // Priorité 1: Email saisi par l'opérateur
    if (operatorInputEmail?.trim()) {
      const clientName = clientEntity?.name || originalClientInfo?.name || 'Client';

      return {
        email: operatorInputEmail.trim(),
        clientName,
        source: 'operator_input',
      };
    }

    // Priorité 2: Email de l'entité client
    if (clientEntity?.email?.trim()) {
      return {
        email: clientEntity.email.trim(),
        clientName: clientEntity.name || 'Client',
        source: 'client_entity',
      };
    }

    // Priorité 3: Email du originalClientInfo
    if (originalClientInfo?.email?.trim()) {
      const clientName = originalClientInfo.name || 'Client';

      return {
        email: originalClientInfo.email.trim(),
        clientName,
        source: 'original_info',
      };
    }

    return null;
  }
}
