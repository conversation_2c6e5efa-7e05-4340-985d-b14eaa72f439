import { Injectable, NotFoundException } from '@nestjs/common';
import { NotificationEntity } from '../../domain/entity/notification.entity';
import { NotificationRepository } from '../../infrastructure/repository/notification.repository';
import { NotificationType } from '../../domain/enum/notification-type.enum';

@Injectable()
export class NotificationService {
  constructor(private readonly notificationRepository: NotificationRepository) {}

  async create(type: NotificationType, userId: string, data?: Record<string, unknown>): Promise<NotificationEntity> {
    const notification = this.notificationRepository.create({
      type,
      userId,
      data,
      isRead: false,
    });

    return this.notificationRepository.save(notification);
  }

  async findByUserId(userId: string): Promise<NotificationEntity[]> {
    return this.notificationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async markAsRead(id: string): Promise<NotificationEntity> {
    const notification = await this.notificationRepository.findOneBy({ id });

    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }

    notification.isRead = true;

    return this.notificationRepository.save(notification);
  }
}
