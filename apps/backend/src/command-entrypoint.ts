import 'dotenv/config';
import 'reflect-metadata';

import { CommandFactory } from 'nest-commander';

import { initializeTransactionalContext } from 'typeorm-transactional';
import { appDatasource } from './app.datasource';
import { AppModule } from './app.module';

async function bootstrap(): Promise<void> {
  try {
    initializeTransactionalContext();
    await appDatasource.initialize();
    await CommandFactory.run(AppModule, {
      logger: ['error', 'warn', 'log'],
      errorHandler: () => {
        // console.log(error.name);
        // console.error('Root Error Handler:', error);
        process.exit(1);
      },

      // This line allows to log stack trace for errors
      serviceErrorHandler: (error) => {
        console.error('Service Error Handler:', error);
        process.exit(1);
      },
    });
    process.exit(0);
  } catch (error) {
    console.error('Unhandled error during bootstrap or CommandFactory execution:', error);
    process.exit(1);
  }
}

bootstrap();
