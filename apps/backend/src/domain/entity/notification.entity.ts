import { Expose } from 'class-transformer';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { NotificationType } from '../enum/notification-type.enum';
import { BaseDomainEntity } from './base.entity';
import { UserEntity } from './user.entity';

@Entity({
  name: 'notification',
})
export class NotificationEntity extends BaseDomainEntity {
  @Column('varchar', { length: 50, nullable: false })
  @Expose()
  type: NotificationType;

  @Column('jsonb', { nullable: true })
  @Expose()
  data?: Record<string, unknown>;

  @Column('uuid', { nullable: false })
  @Expose()
  userId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'user_id' })
  @Expose()
  user: UserEntity;

  @Column('boolean', { default: false })
  @Expose()
  isRead: boolean;
}
