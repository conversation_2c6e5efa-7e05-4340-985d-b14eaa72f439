import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Column, Entity } from 'typeorm';
import { LogisticsEquipmentKind } from '../enum/logistics-equipment-kind.enum';
import { BaseDomainEntity } from './base.entity';

@Entity({ name: 'logistics_equipment_types' })
export class LogisticsEquipmentTypeEntity extends BaseDomainEntity {
  @ApiProperty({
    description: 'The name of the logistics equipment type',
    example: 'Standard Pallet',
  })
  @Column({ type: 'varchar', length: 255, unique: true })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'The kind of logistics equipment',
    enum: LogisticsEquipmentKind,
    example: LogisticsEquipmentKind.PALLET,
  })
  @Column({
    type: 'enum',
    enum: LogisticsEquipmentKind,
  })
  @Expose()
  kind: LogisticsEquipmentKind;
}
