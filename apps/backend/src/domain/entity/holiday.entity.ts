import { Expose } from 'class-transformer';
import { Column, Entity } from 'typeorm';
import { BaseDomainEntity } from './base.entity';

@Entity('holidays')
export class HolidayEntity extends BaseDomainEntity {
  @Column('varchar', { length: 255, nullable: false })
  @Expose()
  name: string;

  @Column('date', { nullable: false })
  @Expose()
  date: Date;

  @Column('int', { nullable: false })
  @Expose()
  year: number;
}
