import { Expose } from 'class-transformer';
import { Column, Entity, OneToMany } from 'typeorm';
import { Address } from './address';
import { BaseDomainEntity } from './base.entity';
import { StopEntity } from './stop.entity';

@Entity({
  name: 'client',
})
/**
 * Client, lors de l'import des STOP via les fichiers XML si le client a un code on le cree sous forme d'entite
 */
export class ClientEntity extends BaseDomainEntity {
  @Column('varchar', { length: 255, nullable: false, unique: true })
  @Expose()
  code: string;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  name?: string;

  @Column(() => Address)
  @Expose()
  address: Address;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  email?: string;

  @OneToMany(() => StopEntity, (stop) => stop.client)
  @Expose()
  stops: StopEntity[];
}
