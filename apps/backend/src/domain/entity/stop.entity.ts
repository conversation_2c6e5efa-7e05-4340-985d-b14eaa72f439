import { Expose } from 'class-transformer';
import { Column, <PERSON><PERSON><PERSON>, Join<PERSON>olum<PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Address } from './address';
import { BaseDomainEntity } from './base.entity';
import { ClientEntity } from './client.entity';
import { DeliveryNoteEntity } from './delivery-note.entity';
import { ShipmentLineEntity } from './shipment-line.entity';
import { StopCompletion } from './stop-completion.entity';
import { TourEntity } from './tour.entity';

export class OriginalClientInfo {
  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  code?: string;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  name?: string;

  @Column(() => Address)
  @Expose()
  address: Address;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  email?: string;
}

@Entity({
  name: 'stop',
})
export class StopEntity extends BaseDomainEntity {
  @ManyToOne(() => TourEntity, (tour) => tour.stops, { nullable: false })
  @JoinColumn({ name: 'tour_id' })
  @Expose()
  tour: TourEntity;

  @Column('uuid', { name: 'tour_id', nullable: false })
  @Expose()
  tourId: string;

  @Column('integer', { nullable: false })
  @Expose()
  /** Correspond a la position de l'arret dans le tour lors de l'import XML */
  sequenceInTour: number;

  @Column(() => OriginalClientInfo, { prefix: 'original_client' })
  @Expose()
  originalClientInfo: OriginalClientInfo;

  @ManyToOne(() => ClientEntity, (client) => client.stops, { nullable: true })
  @JoinColumn({ name: 'client_id' })
  @Expose()
  client: ClientEntity;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  sortingCode: string;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  /** Pour de la visualisation */
  deliveryTimeWindow: string;

  @OneToMany(() => ShipmentLineEntity, (shipmentLine) => shipmentLine.stop)
  @Expose()
  shipmentLines: ShipmentLineEntity[];

  @OneToMany(() => DeliveryNoteEntity, (deliveryNote) => deliveryNote.stop)
  @Expose()
  /** BLs associées à l'arret */
  deliveryNotes: DeliveryNoteEntity[];

  @Column(() => StopCompletion, { prefix: false })
  @Expose()
  completion: StopCompletion;
}
