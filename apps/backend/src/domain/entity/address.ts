import { Expose } from 'class-transformer';
import { Column } from 'typeorm';

export class Address {
  @Column('varchar', { length: 512, name: 'line_1', nullable: true })
  @Expose()
  line1?: string;

  @Column('varchar', { length: 512, name: 'line_2', nullable: true })
  @Expose()
  line2?: string;

  @Column('varchar', { length: 512, name: 'line_3', nullable: true })
  @Expose()
  line3?: string;

  @Column('varchar', { length: 512, name: 'line_4', nullable: true })
  @Expose()
  line4?: string;
}
