import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Expose } from 'class-transformer';
import { BaseDomainEntity } from './base.entity';
import { TourIdentifier } from './tour-identifier';
import { UserEntity } from './user.entity';

@Entity({
  name: 'tour_assignment',
})
export class TourAssignmentEntity extends BaseDomainEntity {
  @Expose()
  @Column(() => TourIdentifier, { prefix: 'tour' })
  tourIdentifier: TourIdentifier;

  @Expose()
  @Column({ type: 'date' })
  fromDate: string;

  /** Si toDate n'est pas renseigné, cela signifie que le tour est actif pour tous les jours à partir de fromDate */
  @Expose()
  @Column({ type: 'date', nullable: true })
  toDate?: string;

  @Expose()
  @Column('text', { nullable: true })
  notes?: string;

  @Expose()
  @ManyToOne(() => UserEntity, (user) => user.tourAssignments)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Expose()
  @Column('uuid')
  @JoinColumn({ name: 'user_id' })
  userId: string;
}
