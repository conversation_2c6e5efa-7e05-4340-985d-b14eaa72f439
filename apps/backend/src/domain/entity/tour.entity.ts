import { Expose } from 'class-transformer';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { FilterableColumn } from '../../infrastructure/decorator/filterable-column.decorator';
import { TourStatus } from '../enum/tour.enums';
import { BaseDomainEntity } from './base.entity';
import { ImportEntity } from './import.entity';
import { LogisticsEquipmentCount } from './logistics-equipment-count';
import { LogisticsEquipmentDetailsEntity } from './logistic-equipment-details.entity';
import { StopEntity } from './stop.entity';
import { TourIdentifier } from './tour-identifier';

@Entity({
  name: 'tour',
})
@Index('idx_tour_identifier_lookup', ['tourIdentifier.originalNumber', 'deliveryDate'], {
  unique: true,
})
export class TourEntity extends BaseDomainEntity {
  @Column(() => TourIdentifier, { prefix: 'tour' })
  @Expose()
  tourIdentifier: TourIdentifier;

  @Column('date')
  @Expose()
  deliveryDate: string;

  @Column('enum', { enum: TourStatus, default: TourStatus.Planned })
  @Expose()
  @FilterableColumn({ type: 'exact' })
  status: TourStatus;

  @Column('varchar', { length: 255, nullable: false })
  @Expose()
  providerFileName: string;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  providerFileHash: string;

  @OneToMany(() => StopEntity, (stop) => stop.tour)
  @Expose()
  stops: StopEntity[];

  @OneToMany(() => LogisticsEquipmentDetailsEntity, (equipment) => equipment.tour, {})
  @Expose()
  controlledEquipment: LogisticsEquipmentDetailsEntity[];

  @ManyToOne('ImportEntity', 'tours', { nullable: true })
  @JoinColumn({ name: 'import_batch_id' })
  @Expose()
  importBatch?: ImportEntity;

  @Column('uuid', { name: 'import_batch_id', nullable: true })
  @Expose()
  importBatchId?: string;

  @Column(() => LogisticsEquipmentCount, { prefix: 'total_loaded' })
  @Expose()
  totalLoadedEquipmentCount?: LogisticsEquipmentCount;

  @Column(() => LogisticsEquipmentCount, { prefix: 'total_preloaded' })
  @Expose()
  totalPreloadedEquipmentCount?: LogisticsEquipmentCount;

  @Column(() => LogisticsEquipmentCount, { prefix: 'total_returned' })
  @Expose()
  totalReturnedEquipmentCount?: LogisticsEquipmentCount;

  @Column(() => LogisticsEquipmentCount, { prefix: 'total_unloaded' })
  @Expose()
  totalUnloadedEquipmentCount?: LogisticsEquipmentCount;

  @Column(() => LogisticsEquipmentCount, { prefix: 'controlled' })
  @Expose()
  controlledEquipmentCount?: LogisticsEquipmentCount;

  // Virtual property for filtering on tour original number
  @FilterableColumn({ type: 'ilike', path: 'tourIdentifier.originalNumber' })
  tourOriginalNumber?: string;
}
