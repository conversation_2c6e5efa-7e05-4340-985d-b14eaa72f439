import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BeforeInsert, BeforeUpdate, Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { LogisticsEquipmentOperation } from '../enum/logistics-equipment-operation.enum';
import { BaseDomainEntity } from './base.entity';
import { LogisticsEquipmentTypeEntity } from './logistics-equipment-type.entity';
import { StopEntity } from './stop.entity';
import { TourEntity } from './tour.entity';

@Entity({ name: 'logistics_equipment_details' })
export class LogisticsEquipmentDetailsEntity extends BaseDomainEntity {
  @ApiProperty({
    description: 'The stop this equipment record belongs to',
  })
  @ManyToOne(() => StopEntity, (stop) => stop.completion.equipmentDetails, {
    nullable: true,
  })
  @JoinColumn({ name: 'stop_id' })
  @Expose()
  stop?: StopEntity;

  @Column('uuid', { name: 'stop_id', nullable: true })
  @Expose()
  stopId?: string;

  @ApiProperty({
    description: 'The tour this equipment record belongs to',
  })
  @ManyToOne(() => TourEntity, (tour) => tour.controlledEquipment, {
    nullable: true,
  })
  @JoinColumn({ name: 'tour_id' })
  @Expose()
  tour?: TourEntity;

  @Column('uuid', { name: 'tour_id', nullable: true })
  @Expose()
  tourId?: string;

  @ApiProperty({
    description: 'The type of logistics equipment',
  })
  @ManyToOne(() => LogisticsEquipmentTypeEntity, { nullable: false })
  @JoinColumn({ name: 'logistics_equipment_type_id' })
  @Expose()
  logisticsEquipmentType: LogisticsEquipmentTypeEntity;

  @Column('uuid', { name: 'logistics_equipment_type_id', nullable: false })
  @Expose()
  logisticsEquipmentTypeId: string;

  @ApiProperty({
    description: 'The quantity of this equipment type',
    example: 5,
  })
  @Column({ type: 'int', nullable: false })
  @Expose()
  quantity: number;

  @ApiProperty({
    description: 'The operation type (RETURNED, UNLOADED, LOADED)',
    enum: LogisticsEquipmentOperation,
    example: LogisticsEquipmentOperation.RETURNED,
  })
  @Column({
    type: 'enum',
    enum: LogisticsEquipmentOperation,
    nullable: false,
  })
  @Expose()
  operation: LogisticsEquipmentOperation;

  @BeforeInsert()
  @BeforeUpdate()
  validateLogisticsEquipmentOperation(): void {
    if (!this.operation) {
      throw new Error('Operation is required');
    }

    if (this.operation === LogisticsEquipmentOperation.CONTROLLED && !this.tourId) {
      throw new Error('Tour ID is required for controlled operation');
    }

    if (!this.stopId) {
      throw new Error('Stop ID is required for controlled operation');
    }

    if (this.stopId && this.tourId) {
      throw new Error('Stop ID and tour ID cannot be set at the same time');
    }
  }
}
