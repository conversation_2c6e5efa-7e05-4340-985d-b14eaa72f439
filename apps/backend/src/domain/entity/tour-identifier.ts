import { Expose } from 'class-transformer';
import { Column, Index } from 'typeorm';
import { TourIdentifierDto } from '../../application/dto/tour-identifier.dto';
import { TourType } from '../enum/tour.enums';

export class TourIdentifier {
  @Column('varchar', { length: 255 })
  @Index()
  @Expose()
  number: string;

  @Column({ type: 'enum', enum: TourType })
  @Index()
  @Expose()
  type: TourType;

  @Column('varchar', { length: 255 })
  @Index()
  @Expose()
  originalNumber: string; // 2204TK

  equals(other: TourIdentifier): boolean {
    return this.number === other.number && this.type === other.type;
  }

  static fromOriginalNumber(originalNumber: string): TourIdentifier {
    const tourIdentifier = new TourIdentifier();
    tourIdentifier.originalNumber = originalNumber;
    tourIdentifier.number = originalNumber;
    tourIdentifier.type = TourType.Normal;

    if (tourIdentifier.originalNumber.endsWith('TK')) {
      tourIdentifier.type = TourType.Frozen;
      tourIdentifier.number = originalNumber.replace('TK', '');
    }

    return tourIdentifier;
  }

  static fromDto(dto: TourIdentifierDto): TourIdentifier {
    const tourIdentifier = new TourIdentifier();

    tourIdentifier.number = dto.number;
    tourIdentifier.type = dto.type;
    tourIdentifier.originalNumber = dto.originalNumber;

    return tourIdentifier;
  }
}
