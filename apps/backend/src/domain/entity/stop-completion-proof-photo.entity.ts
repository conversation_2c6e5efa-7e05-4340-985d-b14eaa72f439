import { Expose } from 'class-transformer';
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseDomainEntity } from './base.entity';
import { FileEntity } from './file.entity';
import { StopEntity } from './stop.entity';

/**
 * Junction table entity to establish many-to-many relationship
 * between StopCompletion and FileEntity for proof photos.
 * This allows multiple proof photos per stop completion.
 */
@Entity({
  name: 'stop_completion_proof_photo',
})
export class StopCompletionProofPhotoEntity extends BaseDomainEntity {
  @ManyToOne(() => StopEntity, (stop) => stop.completion.proofPhotos, { nullable: false })
  @JoinColumn({ name: 'stop_id' })
  @Expose()
  stop: StopEntity;

  @Column('uuid', { name: 'stop_id', nullable: false })
  @Expose()
  stopId: string;

  @ManyToOne(() => FileEntity, { nullable: false })
  @JoinColumn({ name: 'file_id' })
  @Expose()
  file: FileEntity;

  @Column('uuid', { name: 'file_id', nullable: false })
  @Expose()
  fileId: string;

  @Column('integer', { nullable: false, default: 0 })
  @Expose()
  /** Order of the photo in the sequence (0-based) */
  sequenceOrder: number;

  @Column('text', { nullable: true })
  @Expose()
  /** Optional description or caption for the photo */
  description?: string;
}
