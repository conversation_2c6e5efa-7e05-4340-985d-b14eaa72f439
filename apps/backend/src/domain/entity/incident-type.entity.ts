import { Expose } from 'class-transformer';
import { Column, Entity } from 'typeorm';
import { BaseDomainEntity } from './base.entity';

@Entity('incident_types')
export class IncidentType extends BaseDomainEntity {
  @Column({ type: 'varchar', length: 255 })
  @Expose()
  name: string;

  /** Si true, lors de la livraison, annule la livraison */
  @Column({ type: 'boolean', default: false })
  @Expose()
  doesCancelDelivery: boolean;
}
