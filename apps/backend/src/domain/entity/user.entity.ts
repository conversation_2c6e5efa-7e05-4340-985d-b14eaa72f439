import { Expose } from 'class-transformer';
import { Column, Entity, OneToMany } from 'typeorm';
import { FilterableColumn } from '../../infrastructure/decorator/filterable-column.decorator';
import { SearchableColumn } from '../../infrastructure/decorator/searchable-column.decorator';
import { UserRole } from '../enum/user-role.enum';
import { BaseDomainEntity } from './base.entity';
import { FileEntity } from './file.entity';
import { TourAssignmentEntity } from './tour-assignment.entity';

@Entity({
  name: 'user',
})
export class UserEntity extends BaseDomainEntity {
  @SearchableColumn({ weight: 3 })
  @Column('varchar', { length: 255, nullable: false, unique: true })
  @FilterableColumn()
  @Expose()
  username: string;

  @Column('varchar', { length: 5, nullable: true })
  @FilterableColumn()
  @Expose()
  locale: string | null;

  @SearchableColumn({ weight: 2 })
  @Column('varchar', { length: 255, nullable: true })
  @FilterableColumn()
  @Expose()
  email: string | null;

  @SearchableColumn({ weight: 1 })
  @Column('varchar', { length: 100, nullable: true })
  @FilterableColumn()
  @Expose()
  firstName: string | null;

  @SearchableColumn({ weight: 1 })
  @Column('varchar', { length: 100, nullable: true })
  @FilterableColumn()
  @Expose()
  lastName: string | null;

  @Column('varchar', { length: 7, nullable: true })
  @FilterableColumn()
  @Expose()
  color: string | null;

  @Column('boolean', { default: true })
  @Expose()
  isSSO: boolean;

  @Column('enum', { enum: UserRole, nullable: true, array: true })
  @Expose()
  @FilterableColumn({ type: 'array-contains' })
  roles: UserRole[] = [];

  @OneToMany(() => FileEntity, (file) => file.uploadedBy)
  @Expose()
  files: FileEntity[];

  @OneToMany(() => TourAssignmentEntity, (tourAssignment) => tourAssignment.user)
  @Expose()
  tourAssignments: TourAssignmentEntity[];
}
