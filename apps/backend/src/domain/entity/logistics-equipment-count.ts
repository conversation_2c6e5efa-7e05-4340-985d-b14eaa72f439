import { Column } from 'typeorm';
import { EquipmentCountDto } from '../../application/dto/stop/complete-stop-delivery.dto';
import { LogisticsEquipmentKind } from '../enum/logistics-equipment-kind.enum';
import { LogisticsEquipmentDetailsEntity } from './logistic-equipment-details.entity';

export class LogisticsEquipmentCount {
  @Column('int', { nullable: true })
  palletCount?: number;

  @Column('int', { nullable: true })
  rollCount?: number;

  @Column('int', { nullable: true })
  packageCount?: number;

  constructor() {
    this.palletCount = 0;
    this.rollCount = 0;
    this.packageCount = 0;
  }

  public static fromEquipmentDetails(equipmentDetails: LogisticsEquipmentDetailsEntity[]): LogisticsEquipmentCount {
    return Object.assign(new LogisticsEquipmentCount(), {
      palletCount: equipmentDetails
        .filter((detail) => detail.logisticsEquipmentType?.kind === LogisticsEquipmentKind.PALLET)
        .reduce((acc, detail) => acc + detail.quantity, 0),
      rollCount: equipmentDetails
        .filter((detail) => detail.logisticsEquipmentType?.kind === LogisticsEquipmentKind.ROLL)
        .reduce((acc, detail) => acc + detail.quantity, 0),
      packageCount: equipmentDetails
        .filter((detail) => detail.logisticsEquipmentType?.kind === LogisticsEquipmentKind.PACKAGE)
        .reduce((acc, detail) => acc + detail.quantity, 0),
    });
  }

  public static fromDto(dto: EquipmentCountDto): LogisticsEquipmentCount {
    return Object.assign(new LogisticsEquipmentCount(), {
      palletCount: dto.palletCount,
      rollCount: dto.rollCount,
      packageCount: dto.packageCount,
    });
  }

  get total(): number {
    return this.palletCount + this.rollCount + this.packageCount;
  }
}
