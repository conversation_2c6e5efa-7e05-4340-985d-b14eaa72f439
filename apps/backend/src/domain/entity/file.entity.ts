import { Expose } from 'class-transformer';
import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseDomainEntity } from './base.entity';
import { UserEntity } from './user.entity';

@Entity({
  name: 'file',
})
export class FileEntity extends BaseDomainEntity {
  @Column('varchar', { length: 255, unique: true })
  s3fileKey: string;

  @Column('varchar', { length: 255 })
  @Expose()
  originalFilename: string;

  // ATTENTION : bigint doit être sérialisé en string côté API/DTO
  @Column('bigint')
  @Expose()
  fileSize: string;

  @Column('varchar', { length: 255, nullable: true })
  @Expose()
  contentType?: string;

  @ManyToOne(() => UserEntity, (user) => user.files, { nullable: true })
  @Expose()
  uploadedBy?: UserEntity;

  @Column('jsonb', { nullable: true })
  @Expose()
  metadata?: Record<string, unknown>;

  @Column('boolean', { default: false })
  @Expose()
  isPublic: boolean;

  @Expose()
  get downloadUrl(): string {
    return `/file/${this.id}/download`;
  }
}
