import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Column, Entity } from 'typeorm';
import { BaseDomainEntity } from './base.entity';

@Entity({ name: 'vehicles' })
export class VehicleEntity extends BaseDomainEntity {
  @ApiProperty({
    description: "Plaque d'immatriculation du véhicule",
    example: 'AB-123-CD',
  })
  @Column({ type: 'varchar', length: 255 })
  @Expose()
  licensePlate: string;

  @ApiProperty({
    description: 'Modèle du véhicule',
    example: 'Renault Master',
    required: false,
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @Expose()
  model?: string;

  @ApiProperty({
    description: "Numéro d'assurance du véhicule",
    example: 'ASS123456789',
    required: false,
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @Expose()
  insuranceNumber?: string;

  @ApiProperty({
    description: 'Date de fin de validité du contrôle technique',
    example: '2024-12-31',
    required: false,
  })
  @Column({ type: 'date', nullable: true })
  @Expose()
  technicalControlExpiryDate?: string;
}
