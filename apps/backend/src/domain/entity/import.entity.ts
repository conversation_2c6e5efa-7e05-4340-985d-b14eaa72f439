import { Expose } from 'class-transformer';
import { Column, Entity, OneToMany } from 'typeorm';
import { FilterableColumn } from '../../infrastructure/decorator/filterable-column.decorator';
import { ImportStatus } from '../enum/import-status.enum';
import { BaseDomainEntity } from './base.entity';
import { TourEntity } from './tour.entity';

@Entity({
  name: 'import',
  orderBy: {
    createdAt: 'DESC',
  },
})
export class ImportEntity extends BaseDomainEntity {
  @Column('enum', { enum: ImportStatus })
  @FilterableColumn({ type: 'exact' })
  @Expose()
  status: ImportStatus;

  @Column('integer', { default: 0 })
  @Expose()
  successfulRecords: number;

  @Column('integer', { default: 0 })
  @Expose()
  failedRecords: number;

  @Column('integer', { default: 0 })
  @Expose()
  skippedRecords: number;

  @Column('jsonb', { nullable: true })
  @Expose()
  errors?: Array<{
    fileName: string;
    fileContent: string;
    message: string;
    details?: Record<string, unknown>;
  }>;

  @Column('jsonb', { nullable: true })
  @Expose()
  metadata?: Record<string, unknown>;

  // Relations
  @OneToMany(() => TourEntity, (tour) => tour.importBatch)
  @Expose()
  tours?: TourEntity[];

  @Column('date')
  @FilterableColumn({ type: 'exact' })
  @Expose()
  importDate: string;

  @Column('jsonb', { nullable: false, select: false })
  @Expose()
  importedFiles: Array<{ fileName: string; fileContent: string }>;

  static createImport(importDate: string): ImportEntity {
    const importEntity = new ImportEntity();

    importEntity.status = ImportStatus.Completed;
    importEntity.successfulRecords = 0;
    importEntity.failedRecords = 0;
    importEntity.skippedRecords = 0;
    importEntity.errors = [];
    importEntity.metadata = {};
    importEntity.importDate = importDate;
    importEntity.importedFiles = [];

    return importEntity;
  }

  addSuccess({ fileName, fileContent }: { fileName: string; fileContent: string }, importedTour: TourEntity): void {
    this.successfulRecords += 1;
    this.updateStatus();

    this.importedFiles.push({ fileName, fileContent });
    this.tours?.push(importedTour);
  }

  addError({ fileName, fileContent, error }: { fileName: string; fileContent: string; error: Error }): void {
    this.failedRecords += 1;

    if (!this.errors) {
      this.errors = [];
    }
    this.errors.push({ fileName, fileContent, message: error.message });

    this.updateStatus();
  }

  addSkipped(): void {
    this.skippedRecords += 1;
    this.updateStatus();
  }
  private updateStatus(): void {
    if (this.successfulRecords === 0 && this.failedRecords === 0) {
      this.status = ImportStatus.Skipped;
    } else if (this.failedRecords === 0) {
      this.status = ImportStatus.Completed;
    } else if (this.successfulRecords > 0) {
      this.status = ImportStatus.PartiallyCompleted;
    } else {
      this.status = ImportStatus.Failed;
    }
  }
}
