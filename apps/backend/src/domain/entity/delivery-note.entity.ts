import { Expose } from 'class-transformer';
import { <PERSON>umn, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { BaseDomainEntity } from './base.entity';
import { FileEntity } from './file.entity';
import { StopEntity } from './stop.entity';

@Entity({
  name: 'delivery_note',
})
/** <PERSON> livraison */
export class DeliveryNoteEntity extends BaseDomainEntity {
  @ManyToOne(() => StopEntity, (stop) => stop.deliveryNotes)
  @JoinColumn({ name: 'stop_id' })
  @Expose()
  stop: StopEntity;

  @Column('uuid', { name: 'stop_id', nullable: false })
  @Expose()
  stopId: string;

  @Column('varchar', { nullable: false, unique: true })
  @Expose()
  filename: string;

  @ManyToOne(() => FileEntity, { nullable: true })
  @JoinColumn({ name: 'file_id' })
  @Expose()
  file?: FileEntity;

  @Column('uuid', { nullable: true, name: 'file_id' })
  @Expose()
  fileId?: string;

  @Column('integer', { nullable: false })
  @Expose()
  sequenceInStop: number;
}
