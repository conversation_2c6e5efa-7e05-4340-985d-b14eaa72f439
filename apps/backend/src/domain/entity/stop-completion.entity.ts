import { Expose } from 'class-transformer';
import { Column, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { FileEntity } from './file.entity';
import { IncidentType } from './incident-type.entity';
import { LogisticsEquipmentDetailsEntity } from './logistic-equipment-details.entity';
import { LogisticsEquipmentCount } from './logistics-equipment-count';
import { StopCompletionProofPhotoEntity } from './stop-completion-proof-photo.entity';

export enum DeliveryCompletionType {
  FULL = 'FULL',
  PARTIAL = 'PARTIAL',
  NONE = 'NONE',
}

export enum DeliveryStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  /** Attention a ne pas confondre avec les incidents: Failed = livraison non aboutie soit DeliveryCompletionType None */
  FAILED = 'FAILED',
}

export class StopCompletion {
  @Column({
    type: 'enum',
    enum: DeliveryStatus,
    default: DeliveryStatus.PENDING,
  })
  deliveryStatus: DeliveryStatus;

  @Column({ type: 'timestamp', nullable: true })
  completedAt: Date | null;

  // Signature file
  @Column({ type: 'uuid', nullable: true })
  signatureFileId: string | null;

  @ManyToOne(() => FileEntity, { nullable: true })
  @JoinColumn({ name: 'signature_file_id' })
  signatureFile: FileEntity | null;

  @Column({ type: 'varchar', nullable: true })
  signatureFirstName?: string;

  @Column({ type: 'varchar', nullable: true })
  signatureLastName?: string;

  @Column({ type: 'varchar', nullable: true })
  signatureEmail?: string;

  // Proof photos (multiple photos supported)
  @OneToMany(() => StopCompletionProofPhotoEntity, (proofPhoto) => proofPhoto.stop, {
    cascade: true,
    eager: false,
  })
  @Expose()
  proofPhotos: StopCompletionProofPhotoEntity[];

  // Equipment return (ramassage d'agrès)
  @Column(() => LogisticsEquipmentCount, { prefix: 'returned' })
  returnedEquipmentCount?: LogisticsEquipmentCount;

  // Unloaded equipment (déchargement d'agrès)
  @Column(() => LogisticsEquipmentCount, { prefix: 'unloaded' })
  unloadedEquipmentCount?: LogisticsEquipmentCount;

  // Loaded equipment (chargement d'agrès)
  @Column(() => LogisticsEquipmentCount, { prefix: 'loaded' })
  loadedEquipmentCount?: LogisticsEquipmentCount;

  // Preloaded equipment (préchargement d'agrès)
  @Column(() => LogisticsEquipmentCount, { prefix: 'preloaded' })
  preloadedEquipmentCount?: LogisticsEquipmentCount;

  // Incident handling
  @Column({ type: 'uuid', nullable: true })
  incidentTypeId: string | null;

  @ManyToOne(() => IncidentType, { nullable: true })
  @JoinColumn({ name: 'incident_type_id' })
  incidentType: IncidentType | null;

  @Column({
    type: 'enum',
    enum: DeliveryCompletionType,
    nullable: true,
  })
  deliveryCompletionType: DeliveryCompletionType | null;

  @OneToMany(() => LogisticsEquipmentDetailsEntity, (equipment) => equipment.stop, {})
  @Expose()
  equipmentDetails: LogisticsEquipmentDetailsEntity[];

  // Comments
  @Column({ type: 'text', nullable: true })
  comments: string | null;

  // Location data captured during completion
  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude: number | null;

  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude: number | null;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  precision: number | null;
}
