import { Expose } from 'class-transformer';
import { Column, <PERSON>ti<PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { ShipmentOperationType } from '../enum/shipment-line.enums';
import { BaseDomainEntity } from './base.entity';
import { StopEntity } from './stop.entity';

@Entity({
  name: 'shipment_line',
})
export class ShipmentLineEntity extends BaseDomainEntity {
  @ManyToOne(() => StopEntity, (stop) => stop.shipmentLines)
  @JoinColumn({ name: 'stop_id' })
  @Expose()
  stop: StopEntity;

  @Column('uuid', { name: 'stop_id', nullable: false })
  @Expose()
  stopId: string;

  @Column('enum', { enum: ShipmentOperationType, nullable: false })
  @Expose()
  operation: ShipmentOperationType;

  @Column('boolean', { nullable: false })
  @Expose()
  isFrozen: boolean;

  @Column('varchar', { nullable: true })
  @Expose()
  shipperName: string;

  @Column('decimal', { nullable: true })
  @Expose()
  weightKg: number;

  @Column('integer', { nullable: true })
  @Expose()
  /** Nombre de palettes */
  palletCount?: number;

  @Column('integer', { nullable: true })
  @Expose()
  /** Nombre de rolls */
  rollCount?: number;

  @Column('integer', { nullable: true })
  @Expose()
  /** Nombre de colis */
  packageCount?: number;

  @Column('decimal', { nullable: true })
  @Expose()
  /** Montant a payer si spécifié, dans la currency du client */
  amount?: number;

  @Column('integer', { nullable: false })
  @Expose()
  /** Sequence dans le stop */
  sequenceInStop: number;
}
