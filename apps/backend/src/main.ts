import 'reflect-metadata';

import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { SchedulerRegistry } from '@nestjs/schedule';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as express from 'express';
import basicAuth from 'express-basic-auth';
import { initializeTransactionalContext } from 'typeorm-transactional';
import { appDatasource } from './app.datasource';
import { AppModule } from './app.module';
import { HolidayService } from './application/service/holiday.service';
import { injectEnvironmentVariables } from './infrastructure/service/env-injection.service';

async function bootstrap(): Promise<void> {
  initializeTransactionalContext();

  if (!appDatasource.isInitialized) {
    await appDatasource.initialize();
  }

  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug'],
    rawBody: true,
  });

  const logger = new Logger('Bootstrap');

  // Configuration du préfixe global et des pipes
  app.setGlobalPrefix('api');
  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  app.enableCors();

  // Augmenter la taille limite du body pour les images en base64
  app.use(express.json({ limit: '100mb' }));

  // Inject environment variables into frontend index.html
  injectEnvironmentVariables();

  try {
    const holidayService = app.get(HolidayService);
    await holidayService.ensureHolidaysPopulated();
  } catch (error) {
    logger.error('Failed to populate holidays at startup:', error);
  }

  // Configuration de Swagger (+ Basic Auth)
  const config = new DocumentBuilder()
    .setTitle('API Documentation')
    .setDescription("Documentation de l'API du backend")
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Protect Swagger with Basic Auth (env: SWAGGER_USER / SWAGGER_PASSWORD),
  const swaggerUser = process.env.SWAGGER_USER ?? 'in2delivery';
  const swaggerPassword = process.env.SWAGGER_PASSWORD ?? 'documentation'; // Ici on peut se permettre de laisser le mot de passe en clair car c'est une documentation, les endpoints restent protégés par le token JWT

  // Apply auth on both the UI route and the JSON docs route
  app.use(
    ['/api/doc', '/api/doc-json'],
    basicAuth({
      users: { [swaggerUser]: swaggerPassword },
      challenge: true,
      realm: 'Swagger',
    }),
  );

  SwaggerModule.setup('api/doc', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'method',
    },
  });
  await app.listen(process.env.PORT ?? 3000);
  logger.log(`Application is running on: ${await app.getUrl()}`);

  // List registered cron jobs
  try {
    const schedulerRegistry = app.get(SchedulerRegistry);
    const cronJobs = schedulerRegistry.getCronJobs();
    logger.log('📅 Registered cron jobs:');

    if (cronJobs.size === 0) {
      logger.warn('No cron jobs found!');
    } else {
      cronJobs.forEach((job, name) => {
        logger.log(`  - ${name}: Next run at ${job.nextDate()}`);
      });
    }
  } catch (error) {
    logger.error('Failed to list cron jobs:', error);
  }
}

bootstrap();
