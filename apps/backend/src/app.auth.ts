import { Provider } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import 'dotenv/config';
import {
  AuthGuard as KeycloakConnectAuthGuard,
  KeycloakConnectOptions,
  RoleGuard as KeycloakRoleGuard,
  ResourceGuard,
  TokenValidation,
} from 'nest-keycloak-connect';
import { CompositeAuthGuard } from './infrastructure/guard/composite-auth.guard';
import { CompositeRoleGuard } from './infrastructure/guard/composite-role.guard';
import { KeycloakAuthGuard } from './infrastructure/guard/keycloak-auth.guard';

export const keycloakConfiguration: KeycloakConnectOptions = {
  authServerUrl: process.env.KEYCLOAK_SERVER_URL,
  realm: process.env.KEYCLOAK_REALM,
  clientId: process.env.KEYCLOAK_CLIENT_ID,
  secret: process.env.KEYCLOAK_CLIENT_SECRET as string,
  'ssl-required': 'external',
  tokenValidation: TokenValidation.ONLINE,
};

export const keycloakProviders: Provider<any>[] = [
  {
    provide: APP_GUARD,
    useClass: CompositeAuthGuard,
  },
  {
    // global level resource guard (@Resource & @Scopes)
    provide: APP_GUARD,
    useClass: ResourceGuard,
  },
  {
    // global level role guard (@Roles & @AllowAnyRole)
    provide: APP_GUARD,
    useClass: CompositeRoleGuard,
  },
  KeycloakConnectAuthGuard,
  KeycloakAuthGuard,
  KeycloakRoleGuard,
];
