import { Injectable } from '@nestjs/common';
import inquirer from 'inquirer';
import { Command, CommandRunner } from 'nest-commander';
import { TourIdentifierDto } from '../../application/dto/tour-identifier.dto';
import { TourAssignmentService } from '../../application/service/tour-assignment.service';
import { TourType } from '../../domain/enum/tour.enums';
import { TourRepository } from '../repository/tour.repository';
import { UserRepository } from '../repository/user.repository';

@Injectable()
@Command({
  name: 'assign-tour',
  description: 'Assign a tour to a user for a specific period',
})
export class AssignTourCommand extends CommandRunner {
  constructor(
    private readonly tourRepository: TourRepository,
    private readonly userRepository: UserRepository,
    private readonly tourAssignmentService: TourAssignmentService,
  ) {
    super();
  }

  async run(): Promise<void> {
    try {
      // Get distinct tour numbers and users
      const [distinctTourNumbers, users] = await Promise.all([
        this.tourRepository.findDistinctOriginalTourNumbers(),
        this.userRepository.find({
          order: { username: 'ASC' },
        }),
      ]);

      if (distinctTourNumbers.length === 0) {
        console.error('No tours found in database');

        return;
      }

      if (users.length === 0) {
        console.error('No users found in database');

        return;
      }

      // Create choices for tours
      const tourChoices = distinctTourNumbers.map((originalNumber) => {
        // Determine tour type based on suffix
        let tourType = TourType.Normal;
        let tourNumber = originalNumber;

        if (originalNumber.endsWith('TK')) {
          tourType = TourType.Frozen;
          tourNumber = originalNumber.slice(0, -2);
        }

        return {
          name: originalNumber,
          value: JSON.stringify({
            number: tourNumber,
            type: tourType,
            originalNumber: originalNumber,
          }),
        };
      });

      // Create choices for users
      const userChoices = users.map((user) => ({
        name: `${user.username} (${user.email})`,
        value: user.id,
      }));

      // Prompt questions
      const answers = await inquirer.prompt([
        {
          type: 'list',
          name: 'tourIdentifierStr',
          message: 'Select a tour to assign:',
          choices: tourChoices,
        },
        {
          type: 'list',
          name: 'userId',
          message: 'Select a user to assign the tour to:',
          choices: userChoices,
        },
        {
          type: 'input',
          name: 'fromDate',
          message: 'Enter the start date (ISO format YYYY-MM-DD):',
          validate: (input: string): boolean | string => {
            const date = new Date(input);

            if (isNaN(date.getTime()) || !input.match(/^\d{4}-\d{2}-\d{2}$/)) {
              return 'Please enter a valid date in ISO format (YYYY-MM-DD)';
            }

            return true;
          },
        },
        {
          type: 'input',
          name: 'toDate',
          message: 'Enter the end date (ISO format YYYY-MM-DD, leave empty for no end date):',
          validate: (input: string): boolean | string => {
            if (!input) {
              return true;
            }
            const date = new Date(input);

            if (isNaN(date.getTime()) || !input.match(/^\d{4}-\d{2}-\d{2}$/)) {
              return 'Please enter a valid date in ISO format (YYYY-MM-DD) or leave empty';
            }

            return true;
          },
        },
      ]);

      const tourIdentifierData = JSON.parse(answers.tourIdentifierStr);

      // Validate dates
      const from = new Date(answers.fromDate);
      const to = answers.toDate ? new Date(answers.toDate) : null;

      if (isNaN(from.getTime())) {
        console.error('Invalid from date format');

        return;
      }

      if (to && isNaN(to.getTime())) {
        console.error('Invalid to date format');

        return;
      }

      if (to && from > to) {
        console.error('From date cannot be after to date');

        return;
      }

      // Create assignment
      const tourIdentifier: TourIdentifierDto = {
        number: tourIdentifierData.number,
        type: tourIdentifierData.type as TourType,
        originalNumber: tourIdentifierData.originalNumber,
      };

      const assignment = await this.tourAssignmentService.create({
        tourIdentifier,
        userId: answers.userId,
        fromDate: from.toISOString().split('T')[0],
        toDate: to ? to.toISOString().split('T')[0] : null,
      });

      console.log('✅ Tour assignment created successfully:');
      console.log(`   Tour: ${tourIdentifierData.originalNumber}`);
      console.log(`   User: ${users.find((u) => u.id === answers.userId)?.username}`);
      console.log(`   From: ${assignment.fromDate}`);
      console.log(`   To: ${assignment.toDate || 'No end date'}`);
    } catch (error) {
      console.error('Error creating tour assignment:', error);
    }
  }
}
