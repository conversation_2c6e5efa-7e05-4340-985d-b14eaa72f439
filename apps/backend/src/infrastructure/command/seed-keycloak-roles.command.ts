import { Command, CommandRunner } from 'nest-commander';
import { UserRole } from '../../domain/enum/user-role.enum';
import { KeycloakAdminService } from '../service/keycloak-admin.service';

@Command({
  name: 'seed-roles',
  description: 'Will seed the necessary roles for the application',
})
export class SeedKeycloakRolesCommand extends CommandRunner {
  constructor(private readonly keycloakAdminService: KeycloakAdminService) {
    super();
  }

  async run(): Promise<void> {
    console.log('Seeding Keycloak roles...');
    await this.keycloakAdminService.createMissingRoles(Object.values(UserRole));
    process.exit(0);
  }
}
