import { Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import { Command, CommandRunner, Option } from 'nest-commander';
import { DataSource } from 'typeorm';
import { DeliveryNoteEntity } from '../../domain/entity/delivery-note.entity';
import { ShipmentLineEntity } from '../../domain/entity/shipment-line.entity';
import { StopEntity } from '../../domain/entity/stop.entity';
import { TourEntity } from '../../domain/entity/tour.entity';
import { TourStatus } from '../../domain/enum/tour.enums';
import { TourRepository } from '../repository/tour.repository';
import { StopRepository } from '../repository/stop.repository';
import { ShipmentLineRepository } from '../repository/shipment-line.repository';
import { DeliveryNoteRepository } from '../repository/delivery-note.repository';

interface DuplicateToursOptions {
  sourceDate: string;
  startDate: string;
  endDate: string;
  tourNumbers?: string;
  dryRun?: boolean;
}

@Injectable()
@Command({
  name: 'duplicate-tours',
  description: 'Duplicate existing tours over a date range for development',
})
export class DuplicateToursCommand extends CommandRunner {
  constructor(
    private readonly dataSource: DataSource,
    private readonly tourRepository: TourRepository,
    private readonly stopRepository: StopRepository,
    private readonly shipmentLineRepository: ShipmentLineRepository,
    private readonly deliveryNoteRepository: DeliveryNoteRepository,
  ) {
    super();
  }

  async run(_passedParam: string[], options: DuplicateToursOptions): Promise<void> {
    // Validate dates
    const sourceDate = DateTime.fromISO(options.sourceDate);
    const startDate = DateTime.fromISO(options.startDate);
    const endDate = DateTime.fromISO(options.endDate);

    if (!sourceDate.isValid || !startDate.isValid || !endDate.isValid) {
      console.error('❌ Invalid date format. Use YYYY-MM-DD');

      return;
    }

    if (startDate > endDate) {
      console.error('❌ Start date must be before end date');

      return;
    }

    const tourNumbers = options.tourNumbers?.split(',').map((n) => n.trim());

    console.log(`📋 Duplicating tours from ${options.sourceDate}`);
    console.log(`📅 Target range: ${options.startDate} to ${options.endDate}`);

    if (tourNumbers) {
      console.log(`🔢 Tour numbers: ${tourNumbers.join(', ')}`);
    }

    if (options.dryRun) {
      console.log('🧪 DRY RUN MODE - No changes will be saved');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      // Find source tours
      const tourQuery = queryRunner.manager
        .createQueryBuilder(TourEntity, 'tour')
        .leftJoinAndSelect('tour.stops', 'stops')
        .leftJoinAndSelect('stops.shipmentLines', 'shipmentLines')
        .leftJoinAndSelect('stops.deliveryNotes', 'deliveryNotes')
        .where('tour.deliveryDate = :sourceDate', {
          sourceDate: sourceDate.toISODate(),
        });

      if (tourNumbers && tourNumbers.length > 0) {
        tourQuery.andWhere('tourIdentifier.number IN (:...tourNumbers)', {
          tourNumbers,
        });
      }

      const sourceTours = await tourQuery.getMany();

      if (sourceTours.length === 0) {
        console.error('❌ No tours found for the source date');

        return;
      }

      console.log(`✅ Found ${sourceTours.length} tours to duplicate`);

      // Calculate number of days to duplicate
      let currentDate = startDate;
      const dates: DateTime[] = [];

      while (currentDate <= endDate) {
        dates.push(currentDate);
        currentDate = currentDate.plus({ days: 1 });
      }

      console.log(`📅 Will create tours for ${dates.length} days`);

      await queryRunner.startTransaction();

      let totalToursCreated = 0;
      let totalStopsCreated = 0;

      for (const targetDate of dates) {
        console.log(`\n📅 Processing date: ${targetDate.toISODate()}`);

        for (const sourceTour of sourceTours) {
          // Check if tour already exists for this date
          const existingTour = await queryRunner.manager
            .createQueryBuilder(TourEntity, 'tour')
            .where('tour.deliveryDate = :deliveryDate', {
              deliveryDate: targetDate.toISODate(),
            })
            .andWhere('tour.tourIdentifier.number = :number', {
              number: sourceTour.tourIdentifier.number,
            })
            .andWhere('tour.tourIdentifier.type = :type', {
              type: sourceTour.tourIdentifier.type,
            })
            .getOne();

          if (existingTour) {
            console.log(
              `⏭️  Tour ${sourceTour.tourIdentifier.originalNumber} already exists for ${targetDate.toISODate()}, skipping`,
            );
            continue;
          }

          if (!options.dryRun) {
            // Create new tour
            const newTour = this.tourRepository.create({
              tourIdentifier: {
                number: sourceTour.tourIdentifier.number,
                type: sourceTour.tourIdentifier.type,
                originalNumber: sourceTour.tourIdentifier.originalNumber,
              },
              deliveryDate: targetDate.toISODate(),
              status: TourStatus.Planned,
              providerFileName: sourceTour.providerFileName,
              importBatchId: null,
            });

            const savedTour = await queryRunner.manager.save(TourEntity, newTour);
            totalToursCreated++;

            // Duplicate stops
            for (const sourceStop of sourceTour.stops) {
              const newStop = this.stopRepository.create({
                tour: savedTour,
                sequenceInTour: sourceStop.sequenceInTour,
                sortingCode: sourceStop.sortingCode,
                originalClientInfo: sourceStop.originalClientInfo,
                // Reset completion status
                completion: null,
              });

              const savedStop = await queryRunner.manager.save(StopEntity, newStop);
              totalStopsCreated++;

              // Duplicate shipment lines
              if (sourceStop.shipmentLines && sourceStop.shipmentLines.length > 0) {
                const newShipmentLines = sourceStop.shipmentLines.map((line) =>
                  this.shipmentLineRepository.create({
                    stop: savedStop,
                    operation: line.operation,
                    isFrozen: line.isFrozen,
                    shipperName: line.shipperName,
                    weightKg: line.weightKg,
                    palletCount: line.palletCount,
                    rollCount: line.rollCount,
                    packageCount: line.packageCount,
                    amount: line.amount,
                    sequenceInStop: line.sequenceInStop,
                  }),
                );
                await queryRunner.manager.save(ShipmentLineEntity, newShipmentLines);
              }

              // Duplicate delivery notes
              if (sourceStop.deliveryNotes && sourceStop.deliveryNotes.length > 0) {
                const newDeliveryNotes = sourceStop.deliveryNotes.map((note) => {
                  // Generate a new filename by finding and replacing any date pattern YYYYMMDD
                  // Example: BL_4012_20250502_0004876078.pdf -> BL_4012_20250120_0004876078.pdf
                  let newFilename = note.filename;

                  // Try to find a date pattern in the filename (8 consecutive digits)
                  const datePattern = /(\d{8})/;
                  const match = newFilename.match(datePattern);

                  if (match) {
                    // Check if this looks like a date (starts with 20)
                    const potentialDate = match[1];

                    if (potentialDate.startsWith('20')) {
                      // Replace the date with the target date
                      newFilename = newFilename.replace(potentialDate, targetDate.toFormat('yyyyMMdd'));
                    }
                  }

                  // If filename is still the same (no date found or replaced), append the date to make it unique
                  if (newFilename === note.filename) {
                    const baseFilename = note.filename.replace('.pdf', '');
                    newFilename = `${baseFilename}_${targetDate.toFormat('yyyyMMdd')}.pdf`;
                  }

                  return this.deliveryNoteRepository.create({
                    stop: savedStop,
                    filename: newFilename,
                    file: note.file, // Keep reference to same PDF file
                    fileId: note.fileId,
                    sequenceInStop: note.sequenceInStop,
                  });
                });
                await queryRunner.manager.save(DeliveryNoteEntity, newDeliveryNotes);
              }
            }

            console.log(
              `✅ Created tour ${sourceTour.tourIdentifier.originalNumber} with ${sourceTour.stops.length} stops`,
            );
          } else {
            console.log(
              `🔍 Would create tour ${sourceTour.tourIdentifier.originalNumber} with ${sourceTour.stops.length} stops`,
            );
            totalToursCreated++;
            totalStopsCreated += sourceTour.stops.length;
          }
        }
      }

      if (!options.dryRun) {
        await queryRunner.commitTransaction();
        console.log(`\n✅ Successfully created ${totalToursCreated} tours with ${totalStopsCreated} stops`);
      } else {
        console.log(`\n🧪 DRY RUN: Would create ${totalToursCreated} tours with ${totalStopsCreated} stops`);
      }
    } catch (error) {
      if (!options.dryRun) {
        await queryRunner.rollbackTransaction();
      }
      console.error('❌ Error duplicating tours:', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  @Option({
    flags: '-s, --source-date <sourceDate>',
    description: 'Source date to copy tours from (YYYY-MM-DD)',
    required: true,
  })
  parseSourceDate(val: string): string {
    return val;
  }

  @Option({
    flags: '-f, --start-date <startDate>',
    description: 'Start date for duplication range (YYYY-MM-DD)',
    required: true,
  })
  parseStartDate(val: string): string {
    return val;
  }

  @Option({
    flags: '-t, --end-date <endDate>',
    description: 'End date for duplication range (YYYY-MM-DD)',
    required: true,
  })
  parseEndDate(val: string): string {
    return val;
  }

  @Option({
    flags: '-n, --tour-numbers <tourNumbers>',
    description: 'Comma-separated list of tour numbers to duplicate (optional)',
    required: false,
  })
  parseTourNumbers(val: string): string {
    return val;
  }

  @Option({
    flags: '-d, --dry-run',
    description: 'Preview what would be duplicated without making changes',
    defaultValue: false,
  })
  parseDryRun(): boolean {
    return true;
  }
}
