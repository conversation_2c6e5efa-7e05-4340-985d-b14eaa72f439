### Command Pattern (NestJS Commander)

Commands are CLI utilities that follow this pattern:

```typescript
import { Injectable } from '@nestjs/common';
import { Command, CommandRunner } from 'nest-commander';

@Injectable()
@Command({
  name: 'command-name', // kebab-case, used in CLI as: pnpm command-entrypoint-dev command-name
  description: 'Clear description of what the command does',
})
export class MyCommand extends CommandRunner {
  constructor(
    // Inject required services, repositories, etc.
    private readonly myService: MyService,
  ) {
    super();
  }

  async run(passedParams: string[], options?: Record<string, any>): Promise<void> {
    // Command implementation
    // Use console.log for output
    // Use process.exit(0) for success, process.exit(1) for failure
  }
}
```

#### Steps to create a new command:

1. **Create the command file** in `src/infrastructure/command/`:
   ```typescript
   // src/infrastructure/command/my-command.command.ts
   ```

2. **Register in AppModule** providers:
   ```typescript
   // app.module.ts
   providers: [
     // ... other providers
     MyCommand,
   ]
   ```

3. **Run the command**:
   ```bash
   # Development
   pnpm command-entrypoint-dev my-command
   
   # Production
   pnpm command-entrypoint-prod my-command
   ```

**Interactive command with prompts:**
```typescript
import inquirer from 'inquirer';

@Command({
  name: 'assign-tour',
  description: 'Interactively assign tours',
})
export class AssignTourCommand extends CommandRunner {
  async run(): Promise<void> {
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'tourId',
        message: 'Select a tour:',
        choices: tourChoices,
      },
      {
        type: 'input',
        name: 'date',
        message: 'Enter date (YYYY-MM-DD):',
        validate: (input) => {
          // Validation logic
          return true;
        },
      },
    ]);
    
    // Process answers
  }
}
```

#### Command Best Practices:

- Use descriptive command names in kebab-case
- Always include a clear description
- Handle errors with try-catch and proper exit codes
- Use Logger for consistent output formatting
- For interactive commands, use `inquirer` for prompts
- Commands should be idempotent when possible
- Always call `process.exit()` to properly terminate

# Available CLI Commands

## seed-keycloak-roles
Seeds default Keycloak roles (MANAGER, OPERATOR)

## assign-tour  
Interactive command to assign a tour to an operator

## import-xml-tours
Import tours from XML files in the data directory

## import-xml-tours-from-ftp
Import tours from XML files stored on the FTP server for a specific day

## duplicate-tours
Duplicate existing tours over a date range for development

## sync-holidays
Synchronize French holidays for a given year (clears existing holidays for the year and recreates them)

Example usage:
```bash
# Development
pnpm command-entrypoint-dev seed-keycloak-roles
pnpm command-entrypoint-dev assign-tour
pnpm command-entrypoint-dev import-xml-tours --dir ./data/20241201
pnpm command-entrypoint-dev import-xml-tours-from-ftp --date 2024-12-01

# Synchronize holidays for current year
pnpm command-entrypoint-dev sync-holidays

# Synchronize holidays for specific year
pnpm command-entrypoint-dev sync-holidays --year 2025
pnpm command-entrypoint-dev sync-holidays -y 2024

# Duplicate tours from a source date to a date range
pnpm command-entrypoint-dev duplicate-tours --source-date 2025-05-02 --start-date 2025-01-20 --end-date 2025-01-25
pnpm command-entrypoint-dev duplicate-tours -s 2025-05-02 -f 2025-01-20 -t 2025-01-25 --dry-run
pnpm command-entrypoint-dev duplicate-tours -s 2025-05-02 -f 2025-01-20 -t 2025-01-25 --tour-numbers 4012,4022

# Production
pnpm command-entrypoint-prod seed-keycloak-roles
pnpm command-entrypoint-prod sync-holidays --year 2025
```