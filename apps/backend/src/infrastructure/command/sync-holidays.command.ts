import { Injectable, Logger } from '@nestjs/common';
import { Command, CommandRunner, Option } from 'nest-commander';
import { HolidayService } from '../../application/service/holiday.service';
import { HolidayRepository } from '../repository/holiday.repository';

interface SyncHolidaysOptions {
  year?: number;
}

@Injectable()
@Command({
  name: 'sync-holidays',
  description: 'Synchronize French holidays for a given year',
})
export class SyncHolidaysCommand extends CommandRunner {
  private readonly logger = new Logger(SyncHolidaysCommand.name);

  constructor(
    private readonly holidayService: HolidayService,
    private readonly holidayRepository: HolidayRepository,
  ) {
    super();
  }

  async run(passedParams: string[], options?: SyncHolidaysOptions): Promise<void> {
    const year = options?.year || new Date().getFullYear();

    this.logger.log(`Synchronizing French holidays for year ${year}...`);

    try {
      // Remove existing holidays for the year
      await this.removeExistingHolidays(year);

      // Calculate and insert French holidays
      const holidays = this.calculateFrenchHolidays(year);

      for (const holiday of holidays) {
        await this.holidayRepository.save({
          name: holiday.name,
          date: holiday.date,
          year: year,
        });
        this.logger.log(`Added: ${holiday.name} - ${holiday.date.toDateString()}`);
      }

      this.logger.log(`Successfully synchronized ${holidays.length} French holidays for ${year}`);
      process.exit(0);
    } catch (error) {
      this.logger.error(`Failed to sync holidays: ${error.message}`);
      process.exit(1);
    }
  }

  @Option({
    flags: '-y, --year <year>',
    description: 'Year to synchronize holidays for (default: current year)',
  })
  parseYear(val: string): number {
    const year = parseInt(val, 10);

    if (isNaN(year) || year < 1900 || year > 2100) {
      throw new Error('Year must be a valid number between 1900 and 2100');
    }

    return year;
  }

  private async removeExistingHolidays(year: number): Promise<void> {
    const existingHolidays = await this.holidayRepository.find({
      where: { year },
    });

    if (existingHolidays.length > 0) {
      await this.holidayRepository.remove(existingHolidays);
      this.logger.log(`Removed ${existingHolidays.length} existing holidays for year ${year}`);
    }
  }

  private calculateFrenchHolidays(year: number): Array<{ name: string; date: Date }> {
    const holidays: Array<{ name: string; date: Date }> = [];

    // Fixed holidays
    holidays.push(
      { name: "Jour de l'An", date: new Date(year, 0, 1) },
      { name: 'Fête du Travail', date: new Date(year, 4, 1) },
      { name: 'Victoire 1945', date: new Date(year, 4, 8) },
      { name: 'Fête nationale', date: new Date(year, 6, 14) },
      { name: 'Assomption', date: new Date(year, 7, 15) },
      { name: 'Toussaint', date: new Date(year, 10, 1) },
      { name: 'Armistice 1918', date: new Date(year, 10, 11) },
      { name: 'Noël', date: new Date(year, 11, 25) },
    );

    // Mobile holidays based on Easter
    const easterDate = this.calculateEaster(year);

    // Lundi de Pâques (Easter Monday)
    const easterMonday = new Date(easterDate);
    easterMonday.setDate(easterDate.getDate() + 1);
    holidays.push({ name: 'Lundi de Pâques', date: easterMonday });

    // Ascension (39 days after Easter, always Thursday)
    const ascension = new Date(easterDate);
    ascension.setDate(easterDate.getDate() + 39);
    holidays.push({ name: 'Ascension', date: ascension });

    // Lundi de Pentecôte (50 days after Easter)
    const pentecostMonday = new Date(easterDate);
    pentecostMonday.setDate(easterDate.getDate() + 50);
    holidays.push({ name: 'Lundi de Pentecôte', date: pentecostMonday });

    return holidays.sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  private calculateEaster(year: number): Date {
    // Algorithm to calculate Easter Sunday using the algorithm of Oudin (1940)
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;

    return new Date(year, month - 1, day);
  }
}
