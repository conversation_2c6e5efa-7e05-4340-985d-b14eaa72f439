import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import { DateTime } from 'luxon';
import { Command, CommandRunner } from 'nest-commander';
import * as path from 'path';
import { ImportXmlUseCase } from '../../application/use-case/import-xml-use-case';
import { ImportSourceType } from '../../domain/enum/import-source-type.enum';

@Injectable()
@Command({
  name: 'import:xml-tours-from-local-files',
  description: 'Import tour XML files from the data directory',
})
export class ImportXmlToursCommand extends CommandRunner {
  private readonly logger = new Logger(ImportXmlToursCommand.name);
  private readonly dataDir = path.join(__dirname, '../../../data');
  private hasErrors = false;

  constructor(private readonly importXmlUseCase: ImportXmlUseCase) {
    super();
  }

  async run(): Promise<void> {
    const dataDirectory = path.join(__dirname, '../../../data');

    const folders = fs.readdirSync(dataDirectory);

    for (const folder of folders) {
      this.logger.log(`Importing folder: ${folder}`);
      const folderPath = path.join(dataDirectory, folder);
      const folderPathDate = DateTime.fromFormat(folder, 'yyyyMMdd').toISODate();

      const files = fs.readdirSync(folderPath);
      const xmlFiles = files.filter((file) => file.endsWith('.xml'));

      const toImportFiles = xmlFiles.map((fileName) => {
        const xmlContent = fs.readFileSync(path.join(folderPath, fileName), 'utf-8');

        return { xmlContent, fileName };
      });

      this.logger.log(`Importing ${toImportFiles.length} files`);

      const importEntity = await this.importXmlUseCase.importXmlFiles(
        toImportFiles,
        folderPathDate,
        ImportSourceType.FILESYSTEM,
      );

      this.logger.log(`Import completed: ${importEntity.id}`);
    }
  }
}
