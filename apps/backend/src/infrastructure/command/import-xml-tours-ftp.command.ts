import { Injectable, Logger } from '@nestjs/common';
import { Command, CommandRunner, Option } from 'nest-commander';
import { ImportXmlFilesFromFtpUseCase } from '../../application/use-case/import-xml-files-from-ftp.use-case';

interface ImportFtpOptions {
  date: string;
}

@Injectable()
@Command({
  name: 'import:xml-tours-from-ftp',
  description: 'Import tour XML files from the FTP server for the given date',
})
export class ImportXmlToursFtpCommand extends CommandRunner {
  private readonly logger = new Logger(ImportXmlToursFtpCommand.name);

  constructor(private readonly importXmlFilesFromFtpUseCase: ImportXmlFilesFromFtpUseCase) {
    super();
  }

  @Option({
    flags: '-d, --date <date>',
    description: 'Date to import (YYYY-MM-DD)',
    required: true,
  })
  parseDate(val: string): string {
    return val;
  }

  async run(_passed: string[], options: ImportFtpOptions): Promise<void> {
    try {
      const importEntity = await this.importXmlFilesFromFtpUseCase.execute(options.date);
      this.logger.log(`Import completed: ${importEntity.id}`);
    } catch (error) {
      this.logger.error(`Import failed: ${error.message}`);
      process.exit(1);
    }
  }
}
