import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Call<PERSON>andler, ExecutionContext, Inject, Injectable, NestInterceptor } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { Observable } from 'rxjs';
import { UserQueryService } from '../../application/service/user-query.service';
import { UserEntity } from '../../domain/entity/user.entity';
import { UserRepository } from '../repository/user.repository';

interface KeycloakTokenUserInfoType {
  preferred_username: string;
  username: string;
}

@Injectable()
export class CurrentUserInterceptor implements NestInterceptor {
  constructor(
    private userRepository: UserRepository,
    private userQueryService: UserQueryService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<unknown>> {
    const request = context.switchToHttp().getRequest();

    const keycloakUser = request.user as KeycloakTokenUserInfoType;

    const user = await this.getUserFromKeycloakAccessToken(keycloakUser);
    request.domainUser = user;

    return next.handle();
  }

  private async getUserFromKeycloakAccessToken(keycloakUser: KeycloakTokenUserInfoType): Promise<UserEntity> {
    const username = keycloakUser.preferred_username || keycloakUser.username;

    const cachedUser = await this.cacheManager.get<UserEntity>(`UserService:getUserByUsername:${username}`);

    if (cachedUser) {
      return cachedUser;
    }

    const userEntity = await this.userRepository.findOne({
      where: { username },
      loadEagerRelations: false,
    });

    let hydratedUser;

    if (!userEntity) {
      const newUser = await this.userRepository.create({
        username,
        isSSO: true,
      });

      await this.userRepository.save(newUser);

      hydratedUser = await this.userQueryService.getOneByUsername(username);
    } else {
      hydratedUser = await this.userQueryService.getOneByUsername(username);
    }

    await this.cacheManager.set(`UserService:getUserByUsername:${username}`, hydratedUser, 1000 * 60 * 5);

    return hydratedUser;
  }
}
