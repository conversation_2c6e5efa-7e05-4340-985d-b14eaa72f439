import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { QueryParamsDto } from '../../application/dto/pagination.dto';

export const QueryParamsQuery = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();

  // Les query params sont déjà des strings, on hydrate le DTO
  return plainToInstance(QueryParamsDto, request.query, {
    excludeExtraneousValues: false,
  });
});

// Alias pour maintenir la compatibilité
export const PaginationQuery = QueryParamsQuery;
