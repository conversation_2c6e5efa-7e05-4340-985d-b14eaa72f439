import 'reflect-metadata';

export const FILTERABLE_COLUMNS_METADATA_KEY = Symbol('filterableColumns');

export interface FilterableColumnOptions {
  type?: 'ilike' | 'array-contains' | 'exact';
  path?: string; // Support for nested paths like 'tourIdentifier.originalNumber'
}

export interface FilterableColumnMetadata {
  enabled: boolean;
  options?: FilterableColumnOptions;
}

export function FilterableColumn(options?: FilterableColumnOptions): PropertyDecorator {
  return function (target: any, propertyKey: string | symbol) {
    const existingMetadata = Reflect.getMetadata(FILTERABLE_COLUMNS_METADATA_KEY, target.constructor) || {};

    existingMetadata[propertyKey as string] = {
      enabled: true,
      options: options || { type: 'ilike' },
    };

    Reflect.defineMetadata(FILTERABLE_COLUMNS_METADATA_KEY, existingMetadata, target.constructor);
  };
}

export function getFilterableColumns(entity: new () => any): Record<string, FilterableColumnMetadata> {
  const metadata = Reflect.getMetadata(FILTERABLE_COLUMNS_METADATA_KEY, entity) || {};

  return metadata;
}

export function getFilterableColumnNames(entity: new () => any): string[] {
  const metadata = getFilterableColumns(entity);

  return Object.keys(metadata).filter((key) => metadata[key].enabled);
}
