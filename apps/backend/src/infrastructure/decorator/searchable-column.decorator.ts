import 'reflect-metadata';

export const SEARCHABLE_COLUMNS_METADATA_KEY = Symbol('searchableColumns');

export interface SearchableColumnOptions {
  weight?: number; // Poids pour le ranking (1-10)
  exact?: boolean; // Recherche exacte vs partielle
  alias?: string; // Alias pour les relations
}

export function SearchableColumn(options: SearchableColumnOptions = {}): PropertyDecorator {
  return function (target: any, propertyKey: string | symbol) {
    const existingSearchableColumns = Reflect.getMetadata(SEARCHABLE_COLUMNS_METADATA_KEY, target.constructor) || {};

    existingSearchableColumns[propertyKey as string] = {
      weight: options.weight || 1,
      exact: options.exact || false,
      alias: options.alias,
    };

    Reflect.defineMetadata(SEARCHABLE_COLUMNS_METADATA_KEY, existingSearchableColumns, target.constructor);
  };
}

export function getSearchableColumns<T>(entityClass: new () => T): Record<string, SearchableColumnOptions> {
  return Reflect.getMetadata(SEARCHABLE_COLUMNS_METADATA_KEY, entityClass) || {};
}
