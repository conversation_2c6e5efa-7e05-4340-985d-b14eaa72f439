import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { ImportEntity } from '../../domain/entity/import.entity';

@Injectable()
export class ImportRepository extends Repository<ImportEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(ImportEntity, entityManager || dataSource.createEntityManager());
  }

  // Ajoutez ici des méthodes spécifiques au repository si nécessaire
}
