import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { LogisticsEquipmentDetailsEntity } from '../../domain/entity/logistic-equipment-details.entity';

@Injectable()
export class LogisticsEquipmentDetailsRepository extends Repository<LogisticsEquipmentDetailsEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(LogisticsEquipmentDetailsEntity, entityManager || dataSource.createEntityManager());
  }
}
