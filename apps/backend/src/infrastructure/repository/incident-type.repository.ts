import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { IncidentType } from '../../domain/entity/incident-type.entity';

@Injectable()
export class IncidentTypeRepository extends Repository<IncidentType> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(IncidentType, entityManager || dataSource.createEntityManager());
  }
}
