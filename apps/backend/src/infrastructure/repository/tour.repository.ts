import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { TourIdentifier } from '../../domain/entity/tour-identifier';
import { TourEntity } from '../../domain/entity/tour.entity';
import { TourAssignmentRepository } from './tour-assignment.repository'; // Added import

@Injectable()
export class TourRepository extends Repository<TourEntity> {
  constructor(
    private readonly tourAssignmentRepository: TourAssignmentRepository, // Injected TourAssignmentRepository
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(TourEntity, entityManager || dataSource.createEntityManager());
  }

  public async findByTourIdentifierForDate(tourIdentifier: TourIdentifier, date: string): Promise<TourEntity> {
    const tour = await this.findOne({
      where: {
        tourIdentifier: tourIdentifier,
        deliveryDate: date,
      },
    });

    return tour;
  }

  public async findAssignedToursByUserAndDate(userId: string, date: string): Promise<TourEntity[]> {
    const assignments = await this.tourAssignmentRepository.findAssignmentsByUserAndDate(userId, date);

    if (!assignments || assignments.length === 0) {
      return [];
    }

    const tours: TourEntity[] = [];

    for (const assignment of assignments) {
      // The 'date' parameter for findAssignedToursByUserAndDate corresponds to 'deliveryDate' in TourEntity
      // and is the same 'date' used to fetch assignments.
      const tour = await this.findByTourIdentifierForDate(assignment.tourIdentifier, date);

      if (tour) {
        tours.push(tour);
      }
    }

    return tours;
  }

  public async findDistinctOriginalTourNumbers(): Promise<string[]> {
    const result = await this.createQueryBuilder('tour')
      .select('DISTINCT tour.tourIdentifier.originalNumber', 'originalNumber')
      .orderBy('tour.tourIdentifier.originalNumber', 'ASC')
      .getRawMany();

    return result.map((row) => row.originalNumber);
  }

  public async findDistinctTourIdentifiers(startDate?: string, endDate?: string): Promise<TourIdentifier[]> {
    const queryBuilder = this.createQueryBuilder('tour').select([
      'DISTINCT tour.tourIdentifier.originalNumber as original_number',
      'tour.tourIdentifier.number as number',
      'tour.tourIdentifier.type as type',
    ]);

    if (startDate && endDate) {
      queryBuilder.where('tour.deliveryDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    } else if (startDate) {
      queryBuilder.where('tour.deliveryDate >= :startDate', { startDate });
    } else if (endDate) {
      queryBuilder.where('tour.deliveryDate <= :endDate', { endDate });
    }

    queryBuilder.orderBy('tour.tourIdentifier.originalNumber', 'ASC');

    const rawResults = await queryBuilder.getRawMany();

    return rawResults.map((row) => {
      const tourIdentifier = new TourIdentifier();

      tourIdentifier.originalNumber = row.original_number;
      tourIdentifier.number = row.number;
      tourIdentifier.type = row.type;

      return tourIdentifier;
    });
  }
}
