import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { DeliveryNoteEntity } from '../../domain/entity/delivery-note.entity';

@Injectable()
export class DeliveryNoteRepository extends Repository<DeliveryNoteEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(DeliveryNoteEntity, entityManager || dataSource.createEntityManager());
  }
}
