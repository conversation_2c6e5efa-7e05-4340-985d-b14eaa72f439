import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { TourAssignmentEntity } from '../../domain/entity/tour-assignment.entity';

@Injectable()
export class TourAssignmentRepository extends Repository<TourAssignmentEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(TourAssignmentEntity, entityManager || dataSource.createEntityManager());
  }

  public async findAssignmentsByUserAndDate(userId: string, date: string): Promise<TourAssignmentEntity[]> {
    return this.createQueryBuilder('tour_assignment')
      .where('tour_assignment.user_id = :userId', { userId })
      .andWhere('tour_assignment.from_date <= :date', { date })
      .andWhere('(tour_assignment.to_date IS NULL OR tour_assignment.to_date >= :date)', { date })
      .getMany();
  }

  public async findAssignmentsByTourAndDate(
    number: string,
    type: string,
    originalNumber: string,
    date: string,
  ): Promise<TourAssignmentEntity[]> {
    return this.createQueryBuilder('tour_assignment')
      .leftJoinAndSelect('tour_assignment.user', 'user')
      .where('tour_assignment.tour_number = :number', { number })
      .andWhere('tour_assignment.tour_type = :type', { type })
      .andWhere('tour_assignment.tour_original_number = :originalNumber', {
        originalNumber,
      })
      .andWhere('tour_assignment.from_date <= :date', { date })
      .andWhere('(tour_assignment.to_date IS NULL OR tour_assignment.to_date >= :date)', { date })
      .getMany();
  }

  public async findExistingAssignment(
    number: string,
    type: string,
    originalNumber: string,
    userId: string,
    date: string,
  ): Promise<TourAssignmentEntity | null> {
    return this.createQueryBuilder('tour_assignment')
      .where('tour_assignment.tour_number = :number', { number })
      .andWhere('tour_assignment.tour_type = :type', { type })
      .andWhere('tour_assignment.tour_original_number = :originalNumber', {
        originalNumber,
      })
      .andWhere('tour_assignment.user_id = :userId', { userId })
      .andWhere('tour_assignment.from_date <= :date', { date })
      .andWhere('(tour_assignment.to_date IS NULL OR tour_assignment.to_date >= :date)', { date })
      .getOne();
  }

  public async findMergeableAssignments(
    number: string,
    type: string,
    originalNumber: string,
    userId: string,
    fromDate: string,
    toDate?: string,
  ): Promise<TourAssignmentEntity[]> {
    // Récupérer toutes les assignations pour ce tour/utilisateur d'abord
    const allAssignments = await this.createQueryBuilder('tour_assignment')
      .where('tour_assignment.tour_number = :number', { number })
      .andWhere('tour_assignment.tour_type = :type', { type })
      .andWhere('tour_assignment.tour_original_number = :originalNumber', {
        originalNumber,
      })
      .andWhere('tour_assignment.user_id = :userId', { userId })
      .getMany();

    if (allAssignments.length === 0) {
      return [];
    }

    // Trier par date de début
    allAssignments.sort((a, b) => a.fromDate.localeCompare(b.fromDate));

    const endDate = toDate || fromDate;
    const newRange = { from: fromDate, to: endDate };

    // Trouver toutes les assignations qui peuvent être connectées
    const mergeableAssignments: TourAssignmentEntity[] = [];

    for (const assignment of allAssignments) {
      const assignmentTo = assignment.toDate || assignment.fromDate;

      if (this.canMergeRanges({ from: assignment.fromDate, to: assignmentTo }, newRange, mergeableAssignments)) {
        mergeableAssignments.push(assignment);
      }
    }

    return mergeableAssignments;
  }

  private canMergeRanges(
    assignmentRange: { from: string; to: string },
    newRange: { from: string; to: string },
    existingMergeables: TourAssignmentEntity[],
  ): boolean {
    // Vérifier si l'assignation est directement adjacente/chevauchante avec la nouvelle plage
    if (this.rangesAreAdjacent(assignmentRange, newRange)) {
      return true;
    }

    // Vérifier si l'assignation peut être connectée via d'autres assignations déjà fusionnables
    for (const existing of existingMergeables) {
      const existingTo = existing.toDate || existing.fromDate;
      const existingRange = { from: existing.fromDate, to: existingTo };

      if (this.rangesAreAdjacent(assignmentRange, existingRange)) {
        return true;
      }
    }

    return false;
  }

  private rangesAreAdjacent(range1: { from: string; to: string }, range2: { from: string; to: string }): boolean {
    // Calculer les dates avec +/- 1 jour pour détecter l'adjacence
    const range1Start = new Date(range1.from);
    const range1End = new Date(range1.to);
    const range2Start = new Date(range2.from);
    const range2End = new Date(range2.to);

    // Ajouter/soustraire 1 jour pour vérifier l'adjacence
    const range1EndPlus1 = new Date(range1End);
    range1EndPlus1.setDate(range1EndPlus1.getDate() + 1);

    const range1StartMinus1 = new Date(range1Start);
    range1StartMinus1.setDate(range1StartMinus1.getDate() - 1);

    const range2EndPlus1 = new Date(range2End);
    range2EndPlus1.setDate(range2EndPlus1.getDate() + 1);

    const range2StartMinus1 = new Date(range2Start);
    range2StartMinus1.setDate(range2StartMinus1.getDate() - 1);

    // Vérifier chevauchement ou adjacence
    return !(range1EndPlus1 < range2Start || range2EndPlus1 < range1Start);
  }
}
