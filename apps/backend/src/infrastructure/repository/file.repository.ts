import { Injectable, NotFoundException } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { FileEntity } from '../../domain/entity/file.entity';

@Injectable()
export class FileRepository extends Repository<FileEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(FileEntity, entityManager || dataSource.createEntityManager());
  }

  async findByS3Key(s3fileKey: string): Promise<FileEntity> {
    const file = await this.findOne({ where: { s3fileKey } });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    return file;
  }

  async getById(id: string): Promise<FileEntity> {
    const file = await this.findOne({
      where: { id },
      relations: ['uploadedBy'],
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    return file;
  }
}
