import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { VehicleEntity } from '../../domain/entity/vehicle.entity';

@Injectable()
export class VehicleRepository extends Repository<VehicleEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(VehicleEntity, entityManager || dataSource.createEntityManager());
  }
}
