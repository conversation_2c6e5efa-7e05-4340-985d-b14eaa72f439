import { Injectable, NotFoundException } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { Address } from '../../domain/entity/address';
import { ClientEntity } from '../../domain/entity/client.entity';

@Injectable()
export class ClientRepository extends Repository<ClientEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(ClientEntity, entityManager || dataSource.createEntityManager());
  }

  async getById(id: string): Promise<ClientEntity> {
    const client = await this.findOne({ where: { id } });

    if (!client) {
      throw new NotFoundException('Client not found');
    }

    return client;
  }

  async findByCode(code: string): Promise<ClientEntity> {
    const client = await this.findOne({ where: { code } });

    if (!client) {
      throw new NotFoundException('Client not found');
    }

    return client;
  }

  async findByCodeOrNull(code: string): Promise<ClientEntity | null> {
    return this.findOne({ where: { code } });
  }

  async findByCodeOrCreate(
    code: string,
    clientData?: {
      name?: string;
      address?: Address;
      email?: string;
    },
  ): Promise<ClientEntity> {
    let client = await this.findByCodeOrNull(code);

    if (!client) {
      client = this.create({
        code,
        name: clientData?.name,
        address: clientData?.address || new Address(),
        email: clientData?.email,
      });

      await this.save(client);
    }

    return client;
  }
}
