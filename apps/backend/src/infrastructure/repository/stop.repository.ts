import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { StopEntity } from '../../domain/entity/stop.entity';

@Injectable()
export class StopRepository extends Repository<StopEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(StopEntity, entityManager || dataSource.createEntityManager());
  }
}
