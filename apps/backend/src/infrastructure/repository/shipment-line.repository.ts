import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { ShipmentLineEntity } from '../../domain/entity/shipment-line.entity';

@Injectable()
export class ShipmentLineRepository extends Repository<ShipmentLineEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(ShipmentLineEntity, entityManager || dataSource.createEntityManager());
  }
}
