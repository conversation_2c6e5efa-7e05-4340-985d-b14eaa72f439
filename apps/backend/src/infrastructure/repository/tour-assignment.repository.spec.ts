import { TourAssignmentEntity } from '../../domain/entity/tour-assignment.entity';
import { TourType } from '../../domain/enum/tour.enums';
import { TourAssignmentRepository } from './tour-assignment.repository';

describe('TourAssignmentRepository - Duplicate Detection', () => {
  let repository: TourAssignmentRepository;
  let mockQueryBuilder: any;

  beforeEach(() => {
    mockQueryBuilder = {
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
    };

    const mockEntityManager = {
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    };

    const mockDataSource = {
      createEntityManager: jest.fn().mockReturnValue(mockEntityManager),
    } as any;

    repository = new TourAssignmentRepository(undefined, mockDataSource);
    repository.createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder);
  });

  describe('findExistingAssignment', () => {
    it('should find existing assignment with matching criteria', async () => {
      const expectedAssignment = new TourAssignmentEntity();
      expectedAssignment.id = 'assignment-123';

      mockQueryBuilder.getOne.mockResolvedValue(expectedAssignment);

      const result = await repository.findExistingAssignment('4012', TourType.Normal, '4012', 'user-123', '2024-01-15');

      expect(result).toBe(expectedAssignment);
    });

    it('should return null when no matching assignment found', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(null);

      const result = await repository.findExistingAssignment('4012', TourType.Normal, '4012', 'user-123', '2024-01-15');

      expect(result).toBeNull();
    });

    it('should handle open-ended assignments (toDate is null)', async () => {
      const openEndedAssignment = new TourAssignmentEntity();
      openEndedAssignment.toDate = undefined;

      mockQueryBuilder.getOne.mockResolvedValue(openEndedAssignment);

      const result = await repository.findExistingAssignment('4012', TourType.Normal, '4012', 'user-123', '2024-01-20');

      expect(result).toBe(openEndedAssignment);
    });
  });

  describe('findMergeableAssignments', () => {
    beforeEach(() => {
      mockQueryBuilder.getMany = jest.fn();
    });

    it('should find assignments that can be merged (filling gap)', async () => {
      const gapFillingAssignments = [
        { id: 'assign-1', fromDate: '2024-01-20', toDate: '2024-01-20' },
        { id: 'assign-2', fromDate: '2024-01-22', toDate: '2024-01-22' },
      ];

      mockQueryBuilder.getMany.mockResolvedValue(gapFillingAssignments);

      // Adding assignment on 2024-01-21 should find both existing assignments to merge
      const result = await repository.findMergeableAssignments(
        '4012',
        TourType.Normal,
        '4012',
        'user-123',
        '2024-01-21',
        '2024-01-21',
      );

      // Should find both assignments as they can be connected through the new one
      expect(result).toHaveLength(2);
      expect(result).toEqual(gapFillingAssignments);
    });

    it('should return empty array when no mergeable assignments found', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([]);

      const result = await repository.findMergeableAssignments(
        '4012',
        TourType.Normal,
        '4012',
        'user-123',
        '2024-01-22',
        '2024-01-23',
      );

      expect(result).toEqual([]);
    });
  });
});
