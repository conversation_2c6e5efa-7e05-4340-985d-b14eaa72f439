import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { HolidayEntity } from '../../domain/entity/holiday.entity';

@Injectable()
export class HolidayRepository extends Repository<HolidayEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(HolidayEntity, entityManager || dataSource.createEntityManager());
  }
}
