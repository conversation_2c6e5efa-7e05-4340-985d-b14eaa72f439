import { Injectable, NotFoundException } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { UserEntity } from '../../domain/entity/user.entity';

@Injectable()
export class UserRepository extends Repository<UserEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(UserEntity, entityManager || dataSource.createEntityManager());
  }
  async getUserByUsername(username: string): Promise<UserEntity> {
    const user = await this.findOne({
      where: { username },
      relations: [],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }
}
