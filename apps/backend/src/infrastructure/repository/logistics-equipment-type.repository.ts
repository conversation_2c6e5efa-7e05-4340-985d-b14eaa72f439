import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { LogisticsEquipmentTypeEntity } from '../../domain/entity/logistics-equipment-type.entity';

@Injectable()
export class LogisticsEquipmentTypeRepository extends Repository<LogisticsEquipmentTypeEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(LogisticsEquipmentTypeEntity, entityManager || dataSource.createEntityManager());
  }
}
