import { Logger } from '@nestjs/common';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

/**
 * Injects VITE_ environment variables into the frontend index.html file
 * This replaces the shell-based docker-entrypoint.sh for distroless compatibility
 */
export function injectEnvironmentVariables(): void {
  const logger = new Logger('EnvInjectionService');

  try {
    const indexHtmlPath = join(__dirname, '..', '..', '..', 'client', 'index.html');

    logger.log(`Injecting environment variables into ${indexHtmlPath}`);

    // Read the index.html file
    const indexHtml = readFileSync(indexHtmlPath, 'utf-8');

    // Collect all VITE_ environment variables
    const viteVars = collectViteEnvironmentVariables();

    // Create JSON string
    const envJson = JSON.stringify(viteVars);

    // Escape quotes for HTML attribute
    const escapedJson = envJson.replace(/"/g, '&quot;');

    // Replace the meta tag content
    const updatedHtml = indexHtml.replace(
      /<meta name="env" content="[^"]*"/,
      `<meta name="env" content="${escapedJson}"`,
    );

    // Write the updated file
    writeFileSync(indexHtmlPath, updatedHtml, 'utf-8');

    logger.log('Environment variables injected successfully');
    logger.log(`Injected variables: ${JSON.stringify(viteVars, null, 2)}`);
  } catch (error) {
    logger.error('Failed to inject environment variables:', error);
    // Don't throw - this shouldn't prevent the application from starting
  }
}

/**
 * Collects all environment variables that start with VITE_
 */
function collectViteEnvironmentVariables(): Record<string, string> {
  const viteVars: Record<string, string> = {};

  Object.entries(process.env).forEach(([key, value]) => {
    if (key.startsWith('VITE_') && value !== undefined) {
      viteVars[key] = value;
    }
  });

  return viteVars;
}
