import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import SftpClient from 'ssh2-sftp-client';

@Injectable()
export class SftpService {
  private readonly logger = new Logger(SftpService.name);
  private readonly host = process.env.SFTP_HOST;
  private readonly port = Number(process.env.SFTP_PORT || '22');
  private readonly user = process.env.SFTP_USER;
  private readonly password = process.env.SFTP_PASSWORD;
  private readonly privateKeyPath = process.env.SFTP_PRIVATE_KEY_PATH;
  private readonly passphrase = process.env.SFTP_PASSPHRASE;

  private client: SftpClient;
  private clientTimeout: NodeJS.Timeout;

  private async connect(): Promise<SftpClient> {
    if (this.client) {
      clearTimeout(this.clientTimeout);
      this.clientTimeout = setTimeout(() => {
        this.client.end();
        this.client = undefined;
      }, 10000);

      return this.client;
    }

    const client = new SftpClient();

    const connectConfig: any = {
      host: this.host,
      port: this.port,
      username: this.user,
    };

    if (this.privateKeyPath && this.privateKeyPath !== '') {
      connectConfig.privateKey = fs.readFileSync(this.privateKeyPath);

      if (this.passphrase) {
        connectConfig.passphrase = this.passphrase;
      }
    } else if (this.password && this.password !== '') {
      connectConfig.password = this.password;
    }

    await client.connect(connectConfig);

    this.client = client;
    this.clientTimeout = setTimeout(() => {
      this.client.end();
      this.client = undefined;
    }, 10000);

    return client;
  }

  /**
   * Lists files in the specified directory
   * @param directory Remote directory path
   * @returns Array of file names
   */
  async listFiles(directory: string): Promise<string[]> {
    const client = await this.connect();

    try {
      const files = await client.list(directory);

      return files.filter((f) => f.type === '-').map((f) => f.name);
    } catch (error) {
      this.logger.error(`SFTP list error for directory ${directory}: ${error}`);
      throw error;
    }
  }

  /**
   * Lists directories in the specified directory
   * @param directory Remote directory path
   * @returns Array of directory names
   */
  async listDirectories(directory: string): Promise<string[]> {
    const client = await this.connect();

    try {
      const files = await client.list(directory);

      return files.filter((f) => f.type === 'd').map((f) => f.name);
    } catch (error) {
      this.logger.error(`SFTP list directories error for directory ${directory}: ${error}`);
      throw error;
    } finally {
    }
  }

  /**
   * Downloads a file from the SFTP server
   * @param remotePath Remote file path
   * @param localPath Local file path to save to
   */
  async downloadFile(remotePath: string, localPath: string): Promise<void> {
    const client = await this.connect();

    try {
      await client.get(remotePath, localPath);
      this.logger.log(`Successfully downloaded ${remotePath} to ${localPath}`);
    } catch (error) {
      this.logger.error(`SFTP download error for ${remotePath}: ${error}`);
      throw error;
    } finally {
    }
  }

  /**
   * Uploads a file to the SFTP server
   * @param localPath Local file path to upload
   * @param remotePath Remote file path to save to
   */
  async uploadFile(localPath: string, remotePath: string): Promise<void> {
    const client = await this.connect();

    try {
      await client.put(localPath, remotePath);
      this.logger.log(`Successfully uploaded ${localPath} to ${remotePath}`);
    } catch (error) {
      this.logger.error(`SFTP upload error for ${localPath}: ${error}`);
      throw error;
    } finally {
    }
  }

  /**
   * Reads a file from the SFTP server into a Buffer
   * @param remotePath Remote file path
   * @returns File content as Buffer
   */
  async readFile(remotePath: string): Promise<Buffer | null> {
    const client = await this.connect();

    try {
      const buffer = await client.get(remotePath);

      return buffer as Buffer;
    } catch (error) {
      if (error.message.includes('No such file')) {
        return null;
      }

      this.logger.error(`SFTP read error for ${remotePath}: ${error}`);
      throw error;
    } finally {
    }
  }

  /**
   * Writes content to a file on the SFTP server
   * @param remotePath Remote file path
   * @param content Content to write (Buffer or string)
   */
  async writeFile(remotePath: string, content: Buffer | string): Promise<void> {
    const client = await this.connect();

    try {
      await client.put(content, remotePath);
      this.logger.log(`Successfully wrote content to ${remotePath}`);
    } catch (error) {
      this.logger.error(`SFTP write error for ${remotePath}: ${error}`);
      throw error;
    } finally {
    }
  }

  /**
   * Deletes a file from the SFTP server
   * @param remotePath Remote file path to delete
   */
  async deleteFile(remotePath: string): Promise<void> {
    const client = await this.connect();

    try {
      await client.delete(remotePath);
      this.logger.log(`Successfully deleted ${remotePath}`);
    } catch (error) {
      this.logger.error(`SFTP delete error for ${remotePath}: ${error}`);
      throw error;
    } finally {
    }
  }

  /**
   * Creates a directory on the SFTP server
   * @param remotePath Remote directory path to create
   * @param recursive Whether to create parent directories if they don't exist
   */
  async createDirectory(remotePath: string, recursive = false): Promise<void> {
    const client = await this.connect();

    try {
      await client.mkdir(remotePath, recursive);
      this.logger.log(`Successfully created directory ${remotePath}`);
    } catch (error) {
      this.logger.error(`SFTP create directory error for ${remotePath}: ${error}`);
      throw error;
    } finally {
    }
  }

  /**
   * Removes a directory from the SFTP server
   * @param remotePath Remote directory path to remove
   * @param recursive Whether to remove directory and all its contents
   */
  async removeDirectory(remotePath: string, recursive = false): Promise<void> {
    const client = await this.connect();

    try {
      await client.rmdir(remotePath, recursive);
      this.logger.log(`Successfully removed directory ${remotePath}`);
    } catch (error) {
      this.logger.error(`SFTP remove directory error for ${remotePath}: ${error}`);
      throw error;
    } finally {
      await client.end();
    }
  }

  /**
   * Checks if a file or directory exists on the SFTP server
   * @param remotePath Remote path to check
   * @returns Boolean indicating if the path exists
   */
  async exists(remotePath: string): Promise<boolean> {
    const client = await this.connect();

    try {
      const stat = await client.stat(remotePath);

      return !!stat;
    } catch {
      return false;
    } finally {
      await client.end();
    }
  }

  /**
   * Checks if a directory exists on the SFTP server
   * @param remotePath Remote directory path to check
   * @returns Boolean indicating if the directory exists
   */
  async directoryExists(remotePath: string): Promise<boolean> {
    const client = await this.connect();

    try {
      const stat = await client.stat(remotePath);

      return stat.isDirectory;
    } catch {
      return false;
    } finally {
    }
  }

  /**
   * Gets information about a file or directory
   * @param remotePath Remote path to get info for
   * @returns File/directory information
   */
  async getInfo(remotePath: string): Promise<any> {
    const client = await this.connect();

    try {
      const stat = await client.stat(remotePath);

      return stat;
    } catch (error) {
      this.logger.error(`SFTP stat error for ${remotePath}: ${error}`);
      throw error;
    } finally {
      await client.end();
    }
  }

  onModuleDestroy(): void {
    if (this.client) {
      this.client.end();
    }
  }
}
