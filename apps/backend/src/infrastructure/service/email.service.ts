import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { Email } from '../../domain/model/email';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly mailerService: MailerService) {}

  async sendEmail(email: Email): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: email.to,
        subject: email.subject,
        html: email.html,
        text: email.text,
      });

      this.logger.log(`Email sent successfully to ${email.to}: ${email.subject}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${email.to}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
