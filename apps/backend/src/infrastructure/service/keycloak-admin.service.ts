import { Injectable, NotFoundException } from '@nestjs/common';
import { RoleRepresentation, UserRepresentation } from '@s3pweb/keycloak-admin-client-cjs';
import 'dotenv/config';
import { KeycloakAdminClient } from './keycloak-admin.client';

@Injectable()
export class KeycloakAdminService {
  constructor(private readonly keycloakAdminClient: KeycloakAdminClient) {}

  public async findUserByEmail(email: string, getRoles: boolean = true): Promise<UserRepresentation | null> {
    const users = await this.keycloakAdminClient.users.find({ email, exact: true }, { catchNotFound: true });

    if (users.length > 1) {
      throw new Error(`Found ${users.length} users while searching for a user with this exact email.`);
    }

    if (users.length === 0) {
      return null;
    }

    const user = users[0];

    if (getRoles && user.id) {
      const realmRoles = await this.getUserRealmRoles(user.id);
      user.realmRoles = realmRoles;
    }

    return user;
  }

  public async getUserByUsername(username: string): Promise<UserRepresentation> {
    const users = await this.keycloakAdminClient.users.find({ username, exact: true }, { catchNotFound: true });

    if (users.length > 1) {
      throw new Error(`Found ${users.length} users while searching for a user with this exact username.`);
    }

    if (users.length === 0) {
      throw new NotFoundException(`Could not find user with username: ${username}`);
    }

    const user = users[0];

    if (user.id) {
      const realmRoles = await this.getUserRealmRoles(user.id);
      user.realmRoles = realmRoles;
    }

    return user;
  }

  public async changeUserLocale(userId: string, locale: string): Promise<UserRepresentation> {
    const user = await this.keycloakAdminClient.users.findOne({
      id: userId,
      userProfileMetadata: true,
    });

    if (!user) {
      throw new Error(`Could not find user with userId ${userId}`);
    }

    if (!user.attributes) {
      user.attributes = {};
    }

    user.attributes.locale = locale;

    await this.keycloakAdminClient.users.update({ id: user.id! }, user);

    return this.keycloakAdminClient.users.findOne({
      id: userId,
      userProfileMetadata: true,
    }) as UserRepresentation;
  }

  /**
   * Will create listed roles into the authentication system,
   * but only if necessary, this function is idempotent
   * @param roleNames array with the names of the roles
   */
  public async createMissingRoles(roleNames: string[]): Promise<void> {
    const promises = [];

    const existingRoles = await this.keycloakAdminClient.roles.find();
    const existingRoleNames = existingRoles.map((roleRepresentation) => roleRepresentation.name);

    roleNames.forEach((role) => {
      if (!existingRoleNames.includes(role)) {
        promises.push(
          this.keycloakAdminClient.roles
            .create({ name: role })
            .then((newRole) => console.log(`Role ${newRole.roleName} created.`))
            .catch((error) => {
              console.error(`Could not create role ${role}`, error);
            }),
        );
      } else {
        console.log(`Role ${role} already exists`);
      }
    });

    await Promise.all(promises);
  }

  public async createUser(
    email: string,
    firstName: string,
    lastName: string,
    locale: string = 'fr',
  ): Promise<UserRepresentation | null> {
    await this.keycloakAdminClient.users.create({
      email,
      firstName,
      lastName,
      username: email,
      attributes: { locale },
      enabled: true,
    });

    return this.findUserByEmail(email, false);
  }

  /**
   * Définit le mot de passe d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param password Nouveau mot de passe
   * @param temporary Si true, l'utilisateur devra changer le mot de passe à la prochaine connexion
   */
  public async setUserPassword(userId: string, password: string, temporary: boolean = false): Promise<void> {
    await this.keycloakAdminClient.users.resetPassword({
      id: userId,
      credential: {
        temporary,
        type: 'password',
        value: password,
      },
    });

    console.log(`Password set for user ${userId} (temporary: ${temporary})`);
  }

  public async addRealmRoleToUser(userId: string, role: string): Promise<void> {
    const roleWithId = await this.keycloakAdminClient.roles.findOneByName({
      name: role,
    });

    if (!roleWithId) {
      throw new Error(`role: "${role}" does not seem to exist`);
    }

    await this.keycloakAdminClient.users.addRealmRoleMappings({
      id: userId,
      roles: [roleWithId as RoleRepresentation & { id: string; name: string }],
    });
  }

  public async removeRealmRoleFromUser(userId: string, role: string): Promise<void> {
    const roleWithId = await this.keycloakAdminClient.roles.findOneByName({
      name: role,
    });

    if (!roleWithId) {
      throw new Error(`role: "${role}" does not seem to exist`);
    }

    await this.keycloakAdminClient.users.delRealmRoleMappings({
      id: userId,
      roles: [roleWithId as RoleRepresentation & { id: string; name: string }],
    });
  }

  public async updateUser(user: UserRepresentation): Promise<void> {
    await this.keycloakAdminClient.users.update({ id: user.id }, user);
  }

  public async getUserById(id: string): Promise<UserRepresentation> {
    const user = await this.keycloakAdminClient.users.findOne({ id });

    if (!user) {
      throw new NotFoundException(`Could not find user with id: ${id}`);
    }

    return user;
  }

  public async findUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ items: UserRepresentation[]; total: number }> {
    const { page = 1, limit = 10, search, sortBy: _sortBy, sortOrder: _sortOrder } = params;
    const first = (page - 1) * limit;
    const max = limit;
    const query: Record<string, string | number | boolean> = { first, max };

    if (search) {
      query.search = search;
    }
    // Keycloak does not support sortBy/sortOrder nativement, mais on peut filtrer côté client si besoin
    const users = await this.keycloakAdminClient.users.find(query);

    // Keycloak ne retourne pas le total, donc on doit le calculer ou le simuler
    // Pour l'instant, on retourne juste la page demandée
    return { items: users, total: users.length };
  }

  /**
   * Supprime un utilisateur de Keycloak
   * @param userId ID de l'utilisateur à supprimer
   */
  public async deleteUser(userId: string): Promise<void> {
    await this.keycloakAdminClient.users.del({ id: userId });
    console.log(`User ${userId} deleted from Keycloak`);
  }

  /**
   * Récupère les rôles de realm d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Liste des noms des rôles de realm
   */
  public async getUserRealmRoles(userId: string): Promise<string[]> {
    try {
      const roleMappings = await this.keycloakAdminClient.users.listRealmRoleMappings({
        id: userId,
      });

      return roleMappings.map((role) => role.name).filter((name) => name !== undefined) as string[];
    } catch (error) {
      console.error(`Error fetching realm roles for user ${userId}:`, error);

      return [];
    }
  }

  /**
   * Synchronise les rôles d'un utilisateur avec la liste fournie
   * Ajoute les nouveaux rôles et supprime ceux qui ne sont plus présents
   * @param userId ID de l'utilisateur
   * @param targetRoles Rôles cibles à synchroniser
   */
  public async syncUserRoles(userId: string, targetRoles: string[]): Promise<void> {
    try {
      const currentRoles = await this.getUserRealmRoles(userId);

      // Rôles à ajouter (présents dans target mais pas dans current)
      const rolesToAdd = targetRoles.filter((role) => !currentRoles.includes(role));

      // Rôles à supprimer (présents dans current mais pas dans target)
      const rolesToRemove = currentRoles.filter((role) => !targetRoles.includes(role));

      // Ajouter les nouveaux rôles
      for (const role of rolesToAdd) {
        await this.addRealmRoleToUser(userId, role);
      }

      // Supprimer les anciens rôles
      for (const role of rolesToRemove) {
        await this.removeRealmRoleFromUser(userId, role);
      }

      console.log(
        `User ${userId} roles synced. Added: [${rolesToAdd.join(', ')}], Removed: [${rolesToRemove.join(', ')}]`,
      );
    } catch (error) {
      console.error(`Error syncing user roles for ${userId}:`, error);
      throw error;
    }
  }
}
