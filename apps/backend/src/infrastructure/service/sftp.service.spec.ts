import { Test, TestingModule } from '@nestjs/testing';
import { SftpService } from './sftp.service';
import SftpClient from 'ssh2-sftp-client';
import * as fs from 'fs';

jest.mock('ssh2-sftp-client');
jest.mock('fs');

describe('SftpService', () => {
  let service: SftpService;
  let mockSftpClient: jest.Mocked<SftpClient>;

  beforeEach(async () => {
    // Mock environment variables before creating the service
    process.env.SFTP_HOST = 'test-host';
    process.env.SFTP_PORT = '22';
    process.env.SFTP_USER = 'test-user';
    process.env.SFTP_PASSWORD = 'test-password';

    const module: TestingModule = await Test.createTestingModule({
      providers: [SftpService],
    }).compile();

    service = module.get<SftpService>(SftpService);
    mockSftpClient = new SftpClient() as jest.Mocked<SftpClient>;
    (SftpClient as jest.MockedClass<typeof SftpClient>).mockReturnValue(mockSftpClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.SFTP_HOST;
    delete process.env.SFTP_PORT;
    delete process.env.SFTP_USER;
    delete process.env.SFTP_PASSWORD;
    delete process.env.SFTP_PRIVATE_KEY_PATH;
    delete process.env.SFTP_PASSPHRASE;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('listFiles', () => {
    it('should list only files from directory', async () => {
      const mockFiles = [
        {
          name: 'file1.txt',
          type: '-',
          size: 1024,
          modifyTime: new Date(),
          accessTime: new Date(),
          rights: { user: 'rwx', group: 'r-x', other: 'r--' },
          owner: 1000,
          group: 1000,
        },
        {
          name: 'dir1',
          type: 'd',
          size: 512,
          modifyTime: new Date(),
          accessTime: new Date(),
          rights: { user: 'rwx', group: 'r-x', other: 'r--' },
          owner: 1000,
          group: 1000,
        },
        {
          name: 'file2.txt',
          type: '-',
          size: 2048,
          modifyTime: new Date(),
          accessTime: new Date(),
          rights: { user: 'rwx', group: 'r-x', other: 'r--' },
          owner: 1000,
          group: 1000,
        },
      ];
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.list.mockResolvedValue(mockFiles as any);
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.listFiles('/test-dir');

      expect(result).toEqual(['file1.txt', 'file2.txt']);
      expect(mockSftpClient.connect).toHaveBeenCalledWith({
        host: 'test-host',
        port: 22,
        username: 'test-user',
        password: 'test-password',
      });
      expect(mockSftpClient.list).toHaveBeenCalledWith('/test-dir');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should handle errors and still close connection', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.list.mockRejectedValue(new Error('List failed'));
      mockSftpClient.end.mockResolvedValue(undefined);

      await expect(service.listFiles('/test-dir')).rejects.toThrow('List failed');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('listDirectories', () => {
    it('should list only directories', async () => {
      const mockFiles = [
        {
          name: 'file1.txt',
          type: '-',
          size: 1024,
          modifyTime: new Date(),
          accessTime: new Date(),
          rights: { user: 'rwx', group: 'r-x', other: 'r--' },
          owner: 1000,
          group: 1000,
        },
        {
          name: 'dir1',
          type: 'd',
          size: 512,
          modifyTime: new Date(),
          accessTime: new Date(),
          rights: { user: 'rwx', group: 'r-x', other: 'r--' },
          owner: 1000,
          group: 1000,
        },
        {
          name: 'dir2',
          type: 'd',
          size: 512,
          modifyTime: new Date(),
          accessTime: new Date(),
          rights: { user: 'rwx', group: 'r-x', other: 'r--' },
          owner: 1000,
          group: 1000,
        },
      ];
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.list.mockResolvedValue(mockFiles as any);
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.listDirectories('/test-dir');

      expect(result).toEqual(['dir1', 'dir2']);
      expect(mockSftpClient.list).toHaveBeenCalledWith('/test-dir');
    });
  });

  describe('downloadFile', () => {
    it('should download file successfully', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.get.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.downloadFile('/remote/path/file.txt', '/local/path/file.txt');

      expect(mockSftpClient.get).toHaveBeenCalledWith('/remote/path/file.txt', '/local/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should handle download errors', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.get.mockRejectedValue(new Error('Download failed'));
      mockSftpClient.end.mockResolvedValue(undefined);

      await expect(service.downloadFile('/remote/path/file.txt', '/local/path/file.txt')).rejects.toThrow(
        'Download failed',
      );
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('uploadFile', () => {
    it('should upload file successfully', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.put.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.uploadFile('/local/path/file.txt', '/remote/path/file.txt');

      expect(mockSftpClient.put).toHaveBeenCalledWith('/local/path/file.txt', '/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('readFile', () => {
    it('should read file content as buffer', async () => {
      const mockStream = {
        [Symbol.asyncIterator]: async function* (): AsyncGenerator<Buffer> {
          yield Buffer.from('chunk1');
          yield Buffer.from('chunk2');
        },
      };

      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.get.mockResolvedValue(mockStream as any);
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.readFile('/remote/path/file.txt');

      expect(result).toEqual(Buffer.from('chunk1chunk2'));
      expect(mockSftpClient.get).toHaveBeenCalledWith('/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('writeFile', () => {
    it('should write content to file', async () => {
      const content = Buffer.from('test content');
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.put.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.writeFile('/remote/path/file.txt', content);

      expect(mockSftpClient.put).toHaveBeenCalledWith(content, '/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should handle string content', async () => {
      const content = 'test content';
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.put.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.writeFile('/remote/path/file.txt', content);

      expect(mockSftpClient.put).toHaveBeenCalledWith(content, '/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.delete.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.deleteFile('/remote/path/file.txt');

      expect(mockSftpClient.delete).toHaveBeenCalledWith('/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('createDirectory', () => {
    it('should create directory', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.mkdir.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.createDirectory('/remote/path/newdir');

      expect(mockSftpClient.mkdir).toHaveBeenCalledWith('/remote/path/newdir', false);
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should create directory recursively', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.mkdir.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.createDirectory('/remote/path/newdir', true);

      expect(mockSftpClient.mkdir).toHaveBeenCalledWith('/remote/path/newdir', true);
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('removeDirectory', () => {
    it('should remove directory', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.rmdir.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.removeDirectory('/remote/path/dir');

      expect(mockSftpClient.rmdir).toHaveBeenCalledWith('/remote/path/dir', false);
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should remove directory recursively', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.rmdir.mockResolvedValue(undefined);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.removeDirectory('/remote/path/dir', true);

      expect(mockSftpClient.rmdir).toHaveBeenCalledWith('/remote/path/dir', true);
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('exists', () => {
    it('should return true if path exists', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.stat.mockResolvedValue({
        isFile: true,
        mode: 33188,
        uid: 1000,
        gid: 1000,
        size: 1024,
        accessTime: new Date(),
        modifyTime: new Date(),
        isDirectory: false,
        isBlockDevice: false,
        isCharacterDevice: false,
        isSymbolicLink: false,
        isFIFO: false,
        isSocket: false,
      } as any);
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.exists('/remote/path/file.txt');

      expect(result).toBe(true);
      expect(mockSftpClient.stat).toHaveBeenCalledWith('/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should return false if path does not exist', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.stat.mockRejectedValue(new Error('File not found'));
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.exists('/remote/path/nonexistent.txt');

      expect(result).toBe(false);
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('directoryExists', () => {
    it('should return true if directory exists', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.stat.mockResolvedValue({
        isFile: false,
        mode: 16877,
        uid: 1000,
        gid: 1000,
        size: 512,
        accessTime: new Date(),
        modifyTime: new Date(),
        isDirectory: true,
        isBlockDevice: false,
        isCharacterDevice: false,
        isSymbolicLink: false,
        isFIFO: false,
        isSocket: false,
      } as any);
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.directoryExists('/remote/path/dir');

      expect(result).toBe(true);
      expect(mockSftpClient.stat).toHaveBeenCalledWith('/remote/path/dir');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should return false if path is a file', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.stat.mockResolvedValue({
        isFile: true,
        mode: 33188,
        uid: 1000,
        gid: 1000,
        size: 1024,
        accessTime: new Date(),
        modifyTime: new Date(),
        isDirectory: false,
        isBlockDevice: false,
        isCharacterDevice: false,
        isSymbolicLink: false,
        isFIFO: false,
        isSocket: false,
      } as any);
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.directoryExists('/remote/path/file.txt');

      expect(result).toBe(false);
      expect(mockSftpClient.stat).toHaveBeenCalledWith('/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should return false if directory does not exist', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.stat.mockRejectedValue(new Error('Directory not found'));
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.directoryExists('/remote/path/nonexistent');

      expect(result).toBe(false);
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('getInfo', () => {
    it('should return file info', async () => {
      const mockStat = {
        isFile: true,
        size: 1024,
        mode: 33188,
        uid: 1000,
        gid: 1000,
        accessTime: new Date(),
        modifyTime: new Date(),
        isDirectory: false,
        isBlockDevice: false,
        isCharacterDevice: false,
        isSymbolicLink: false,
        isFIFO: false,
        isSocket: false,
      };
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.stat.mockResolvedValue(mockStat as any);
      mockSftpClient.end.mockResolvedValue(undefined);

      const result = await service.getInfo('/remote/path/file.txt');

      expect(result).toEqual(mockStat);
      expect(mockSftpClient.stat).toHaveBeenCalledWith('/remote/path/file.txt');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });

    it('should handle stat errors', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.stat.mockRejectedValue(new Error('Stat failed'));
      mockSftpClient.end.mockResolvedValue(undefined);

      await expect(service.getInfo('/remote/path/file.txt')).rejects.toThrow('Stat failed');
      expect(mockSftpClient.end).toHaveBeenCalled();
    });
  });

  describe('connection configuration', () => {
    it('should use private key authentication when provided', async () => {
      process.env.SFTP_PRIVATE_KEY_PATH = '/path/to/key';
      process.env.SFTP_PASSPHRASE = 'key-passphrase';
      delete process.env.SFTP_PASSWORD;

      (fs.readFileSync as jest.Mock).mockReturnValue(Buffer.from('mock-key'));

      // Create a new service instance with the updated environment
      const module: TestingModule = await Test.createTestingModule({
        providers: [SftpService],
      }).compile();
      const newService = module.get<SftpService>(SftpService);

      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.list.mockResolvedValue([]);
      mockSftpClient.end.mockResolvedValue(undefined);

      await newService.listFiles('/test-dir');

      expect(mockSftpClient.connect).toHaveBeenCalledWith({
        host: 'test-host',
        port: 22,
        username: 'test-user',
        privateKey: Buffer.from('mock-key'),
        passphrase: 'key-passphrase',
      });
    });

    it('should use password authentication when no private key is provided', async () => {
      mockSftpClient.connect.mockResolvedValue(undefined);
      mockSftpClient.list.mockResolvedValue([]);
      mockSftpClient.end.mockResolvedValue(undefined);

      await service.listFiles('/test-dir');

      expect(mockSftpClient.connect).toHaveBeenCalledWith({
        host: 'test-host',
        port: 22,
        username: 'test-user',
        password: 'test-password',
      });
    });
  });
});
