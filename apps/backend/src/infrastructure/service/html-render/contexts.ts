export class HtmlRenderContext {}

export class StopCompletionTableOnlyContext extends HtmlRenderContext {
  merchandiseItems: MerchandiseItem[];
  currentPage: number;
  totalPages: number;
}

export interface MerchandiseItem {
  quantity: number;
  unit: string;
  designation: string;
  weight: number;
}

export class StopCompletionDocumentContext extends HtmlRenderContext {
  title: string;
  shipmentNumber: string;
  clientRef: string;
  clientRef3: string;

  transportCompanyName: string;
  transportCompanyAddressLines: string[];
  transportCompanyPhone: string;
  transportCompanyEmail: string;

  senderName: string;
  senderCompany: string;
  senderAddressLines: string[];

  truckNumber: string;

  recipientName: string;
  recipientLocation: string;
  recipientPhone?: string;
  recipientContact?: string;
  recipientAddressLines: string[];

  deliveryStatus: string;
  deliveryDate: string;
  totalPackages: number;
  totalWeight: string;
  temperatureInfo?: string;
  showTemperatureInfo: boolean;

  deliveryHours?: string;
  showDeliveryHours: boolean;

  signatureBase64?: string;
  signatureName?: string;
  signatureDateTime?: string;

  qrCodeBase64?: string;
  qrCodeText?: string;
  showQrCode: boolean;

  arrivalTime?: string;
  waitingTime?: string;
  departureTime?: string;
  showTimingInfo: boolean;

  clientRemarks?: string;
  showClientRemarks: boolean;

  merchandiseItems: MerchandiseItem[];

  currentPage: number;
  totalPages: number;

  constructor() {
    super();
    this.title = 'Confirmation de réception';
  }

  setSignatureFromBuffer(buffer: Buffer): void {
    this.signatureBase64 = buffer.toString('base64');
  }

  setQrCodeFromBuffer(buffer: Buffer): void {
    this.qrCodeBase64 = buffer.toString('base64');
    this.qrCodeText = 'Google Maps';
    this.showQrCode = true;
  }
}
