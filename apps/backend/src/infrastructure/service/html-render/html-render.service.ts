import { Injectable } from '@nestjs/common';
import fs from 'fs/promises';
import Handlebars from 'handlebars';
import path from 'path';
import { getAssetPath } from '../../util/path.utils';
import { HtmlRenderContext } from './contexts';

type HtmlTemplateRenderer<T extends HtmlRenderContext> = Handlebars.TemplateDelegate<T>;

@Injectable()
export class HtmlRenderService {
  private readonly assetPath = getAssetPath();
  constructor() {}

  async render<T>(template: HtmlTemplateRenderer<T>, context: T): Promise<string> {
    return template(context);
  }

  public async readTemplate(templateName: string): Promise<string> {
    const templatePath = path.join(this.assetPath, templateName);
    const template = await fs.readFile(templatePath, 'utf8');

    return template;
  }

  public async getCompiledTemplate<T>(templateHtml: string): Promise<HtmlTemplateRenderer<T>> {
    const template = Handlebars.compile(templateHtml);

    return template;
  }

  public async readTemplateAndRender<T extends HtmlRenderContext>(templateName: string, context: T): Promise<string> {
    const templateHtml = await this.readTemplate(templateName);
    const template = await this.getCompiledTemplate<T>(templateHtml);

    return this.render(template, context);
  }
}
