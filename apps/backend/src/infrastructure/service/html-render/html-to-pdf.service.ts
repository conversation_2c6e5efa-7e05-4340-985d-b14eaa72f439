import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class HtmlToPdfService {
  private readonly apiUrl: string;
  private readonly credentials: string;

  constructor() {
    this.apiUrl = process.env.HTML_TO_PDF_API_URL;
    this.credentials = process.env.HTML_TO_PDF_CREDENTIALS;
  }

  get htmlFileToPdfApiUrl(): string {
    return `${this.apiUrl}/forms/chromium/convert/html`;
  }

  get mergePdfsApiUrl(): string {
    return `${this.apiUrl}/forms/pdfengines/merge`;
  }

  private setFormDataSettings(formData: FormData): void {
    formData.append('marginTop', '0');
    formData.append('marginBottom', '0');
    formData.append('marginLeft', '0');
    formData.append('marginRight', '0');
    formData.append('singlePage', 'true');
    formData.append('skipNetworkIdleEvent', 'true');
  }

  private addFileToFormData(formData: FormData, file: Blob, name: string): void {
    formData.append('files', file, name);
  }

  private getFormData(html: string): FormData {
    const formData = new FormData();

    const indexHtmlBlob = new Blob([html], { type: 'text/html' });

    this.addFileToFormData(formData, indexHtmlBlob, 'index.html');

    this.setFormDataSettings(formData);

    return formData;
  }

  private getHeaders(): { [key: string]: string } {
    const b64Credentials = Buffer.from(this.credentials).toString('base64');

    return {
      'Content-Type': 'multipart/form-data',
      Authorization: `Basic ${b64Credentials}`,
    };
  }

  public async convertHtmlToPdf(html: string): Promise<Buffer> {
    const response = await axios.post(this.htmlFileToPdfApiUrl, this.getFormData(html), {
      headers: this.getHeaders(),
      responseType: 'arraybuffer',
    });

    return response.data;
  }

  public async mergeMultiplePdfs(pdfs: Buffer[]): Promise<Buffer> {
    const formData = new FormData();

    for (const pdfIndex in pdfs) {
      const pdf = pdfs[pdfIndex];
      const pdfBlob = new Blob([pdf], { type: 'application/pdf' });

      this.addFileToFormData(formData, pdfBlob, `file${pdfIndex}.pdf`);
    }

    const response = await axios.post(this.mergePdfsApiUrl, formData, {
      headers: { ...this.getHeaders() },
      responseType: 'arraybuffer',
    });

    return response.data;
  }

  public async convertMultipleHtmlToPdf(htmls: string[]): Promise<Buffer> {
    const pdfs = [] as Buffer[];

    for (const html of htmls) {
      const pdf = await this.convertHtmlToPdf(html);

      pdfs.push(pdf);
    }

    if (pdfs.length === 0) {
      throw new Error('No pdfs generated');
    }

    if (pdfs.length === 1) {
      return pdfs[0];
    }

    return await this.mergeMultiplePdfs(pdfs);
  }
}
