import { Logger } from '@nestjs/common';
import { KeycloakAdminClient as KeycloakAdminClientBase } from '@s3pweb/keycloak-admin-client-cjs';

const MAX_TOKEN_REFRESH_INTERVAL = 1000 * 60 * 5; // 5 minutes
const MIN_TOKEN_LIFESPAN = 1000 * 30; // 30 seconds

export class KeycloakAdminClient extends KeycloakAdminClientBase {
  private logger = new Logger(KeycloakAdminClient.name);

  constructor() {
    super({
      baseUrl: process.env.KEYCLOAK_SERVER_URL,
    });
    this.setConfig({
      realmName: process.env.KEYCLOAK_REALM,
    });
  }

  private async authenticate(): Promise<string> {
    const masterClient = new KeycloakAdminClientBase({
      baseUrl: process.env.KEYCLOAK_SERVER_URL,
    });

    masterClient.setConfig({
      realmName: process.env.KEYCLOAK_REALM,
    });

    await masterClient.auth({
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET,
      grantType: 'client_credentials',
      clientId: process.env.KEYCLOAK_CLIENT_ID,
    });

    if (!masterClient.accessToken) {
      throw new Error('Failed to authenticate admin user keycloak service');
    }

    this.setAccessToken(masterClient.accessToken);

    return masterClient.accessToken;
  }

  public async initializeClient(): Promise<void> {
    const authToken = await this.authenticate();

    const parsedToken = JSON.parse(atob(authToken.split('.')[1]));

    const tokenLifespan = (parsedToken.exp - parsedToken.iat) * 1000;

    const tokenRefreshInterval =
      Math.min(MAX_TOKEN_REFRESH_INTERVAL, Math.max(MIN_TOKEN_LIFESPAN, tokenLifespan)) - 5000;

    this.logger.log(`Will refresh token every ${tokenRefreshInterval / 1000} seconds`);

    setInterval(() => {
      this.authenticate();
    }, tokenRefreshInterval);
  }
}
