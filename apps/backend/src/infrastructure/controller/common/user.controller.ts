import { Body, ClassSerializerInterceptor, Controller, Get, Patch, Post, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PatchCurrentUserRequest } from '../../../application/dto/user/patch-current-user-request';
import { ResetPasswordRequest } from '../../../application/dto/user/reset-password-request.dto';
import { UserQueryService } from '../../../application/service/user-query.service';
import { UserService } from '../../../application/service/user.service';
import { UserEntity } from '../../../domain/entity/user.entity';
import { CurrentUser } from '../../decorator/current-user.decorator';

@ApiTags('Common - Users')
@Controller('user')
@UseInterceptors(ClassSerializerInterceptor)
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly userQueryService: UserQueryService,
  ) {}

  @Get('me')
  @ApiOperation({
    summary: 'Mon profil',
    description:
      'Récupère les informations de l’utilisateur connecté (source Keycloak + app) pour affichage et contexte d’affectation.',
  })
  @ApiResponse({ status: 200, type: UserEntity })
  async getMe(@CurrentUser() user: UserEntity): Promise<UserEntity> {
    return this.userQueryService.getOneByUsername(user.username);
  }

  @Patch('me')
  @ApiOperation({
    summary: 'Mettre à jour mon profil',
    description: 'Permet de modifier certaines informations du profil opérateur (ex: téléphone).',
  })
  @ApiResponse({ status: 200, type: UserEntity })
  async updateMe(@CurrentUser() user: UserEntity, @Body() body: PatchCurrentUserRequest): Promise<UserEntity> {
    return this.userService.updateUser(user.id, body);
  }

  @Post('me/reset-password')
  @ApiOperation({
    summary: 'Réinitialiser mon mot de passe',
    description: 'Initie une réinitialisation du mot de passe via Keycloak pour l’utilisateur courant.',
  })
  @ApiResponse({
    status: 200,
    description: 'Mot de passe réinitialisé avec succès',
  })
  async resetMyPassword(
    @CurrentUser() user: UserEntity,
    @Body() resetPasswordDto: ResetPasswordRequest,
  ): Promise<void> {
    return this.userService.resetUserPassword(user.id, resetPasswordDto.password, resetPasswordDto.temporary);
  }
}
