import { ClassSerializerInterceptor, Controller, Get, Param, ParseIntPipe, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HolidayService } from '../../../application/service/holiday.service';
import { HolidayEntity } from '../../../domain/entity/holiday.entity';

@ApiTags('Common - Holidays')
@Controller('holidays')
@UseInterceptors(ClassSerializerInterceptor)
export class HolidayController {
  constructor(private readonly holidayService: HolidayService) {}

  @Get()
  @ApiOperation({
    summary: 'Lister les jours fériés',
    description: 'Référentiel des jours fériés utilisé pour le planning et les importations (calendrier opérationnel).',
  })
  @ApiResponse({ status: 200, type: [HolidayEntity] })
  async listHolidays(): Promise<HolidayEntity[]> {
    return this.holidayService.findAll();
  }

  @Get('year/:year')
  @ApiOperation({
    summary: 'Jours fériés par année',
    description:
      'Filtre par année civile pour alimenter les écrans de paramétrage et éviter les tournées sur jours fériés.',
  })
  @ApiResponse({ status: 200, type: [HolidayEntity] })
  async getHolidaysByYear(@Param('year', ParseIntPipe) year: number): Promise<HolidayEntity[]> {
    return this.holidayService.findByYear(year);
  }
}
