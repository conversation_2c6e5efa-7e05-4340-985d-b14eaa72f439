import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseUUIDPipe,
  Post,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { Response } from 'express';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import { FileService } from '../../../application/service/file.service';
import { FileEntity } from '../../../domain/entity/file.entity';
import { UserEntity } from '../../../domain/entity/user.entity';
import { CurrentUser } from '../../decorator/current-user.decorator';
import { PaginationQuery } from '../../decorator/pagination.decorator';
import { Public } from '../../decorator/public.decorator';
import { FileRepository } from '../../repository/file.repository';
import { S3Service } from '../../service/s3.service';

@ApiTags('Common - Files')
@Controller('file')
export class FileController {
  constructor(
    private readonly fileService: FileService,
    private readonly fileRepository: FileRepository,
    private readonly s3Service: S3Service,
  ) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Uploader un fichier',
    description:
      'Stocke un fichier applicatif (ex: photo, signature) sur S3 avec métadonnées. Permet les fichiers publics pour usage front.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 201,
    type: FileEntity,
    description: 'Fichier uploadé avec succès',
  })
  @ApiResponse({ status: 400, description: 'Fichier ou métadonnées invalides' })
  async create(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser() user: UserEntity,
    @Body('folder') folder: string,
    @Body('metadata') metadata?: string,
    @Body('isPublic') isPublic?: string,
  ): Promise<FileEntity> {
    const meta = metadata ? JSON.parse(metadata) : undefined;
    const isPublicBool = isPublic === 'true';
    const entity = await this.fileService.createFile({
      file,
      folder,
      createdByUser: user,
      metadata: meta,
      isPublic: isPublicBool,
    });

    return plainToInstance(FileEntity, entity, {
      excludeExtraneousValues: true,
    });
  }

  @Get(':id')
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({
    summary: 'Détail d’un fichier',
    description: 'Métadonnées et droits (public/privé) pour affichage et contrôle d’accès.',
  })
  @ApiResponse({
    status: 200,
    type: FileEntity,
    description: 'Détails du fichier',
  })
  @ApiResponse({ status: 404, description: 'Fichier introuvable' })
  async getById(@Param('id', ParseUUIDPipe) id: string): Promise<FileEntity> {
    const entity = await this.fileRepository.getById(id);

    return plainToInstance(FileEntity, entity, {
      excludeExtraneousValues: true,
    });
  }

  @Get(':id/download')
  @ApiOperation({
    summary: 'Télécharger un fichier',
    description: 'Téléchargement authentifié du contenu depuis S3.',
  })
  @ApiResponse({
    status: 200,
    description: 'Téléchargement réussi',
  })
  @ApiResponse({ status: 404, description: 'Fichier introuvable' })
  async download(@Param('id', ParseUUIDPipe) id: string, @Res() res: Response): Promise<void> {
    const entity = await this.fileRepository.getById(id);

    if (!entity) {
      throw new NotFoundException('File not found');
    }

    const buffer = await this.s3Service.getObject(entity.s3fileKey);

    res.setHeader('Content-Type', entity.contentType || 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(entity.originalFilename)}"`);
    res.setHeader('Content-Length', entity.fileSize.toString());

    res.send(buffer);
  }

  @Get(':id/download/public')
  @Public()
  @ApiOperation({
    summary: 'Télécharger un fichier public',
    description: 'Accès sans authentification pour les fichiers marqués publics (ex: assets).',
  })
  @ApiResponse({
    status: 200,
    description: 'Téléchargement public réussi',
  })
  @ApiResponse({
    status: 404,
    description: 'Fichier introuvable ou non public',
  })
  async downloadPublic(@Param('id', ParseUUIDPipe) id: string, @Res() res: Response): Promise<void> {
    const entity = await this.fileRepository.getById(id);

    if (!entity || !entity.isPublic) {
      throw new NotFoundException('File not found or not public');
    }

    const buffer = await this.s3Service.getObject(entity.s3fileKey);

    res.setHeader('Content-Type', entity.contentType || 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(entity.originalFilename)}"`);
    res.setHeader('Content-Length', entity.fileSize.toString());

    res.send(buffer);
  }

  @Get()
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({
    summary: 'Lister les fichiers (paginé)',
    description: 'Recherche paginée pour back-office et diagnostics.',
  })
  @ApiResponse({
    status: 200,
    type: PaginatedResponseDto,
    description: 'Fichiers récupérés',
  })
  async listFiles(@PaginationQuery() pagination: PaginationParamsDto): Promise<PaginatedResponseDto<FileEntity>> {
    const { items, meta } = await this.fileService.getPaginatedFiles(pagination);

    return new ListBuilder(FileEntity).items(items).meta(meta).build();
  }
}
