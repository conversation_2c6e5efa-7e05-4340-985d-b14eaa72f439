import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard, ResourceGuard, RoleMatchingMode, Roles } from 'nest-keycloak-connect';
import { IncidentTypeService } from '../../../application/service/incident-type.service';
import { IncidentType } from '../../../domain/entity/incident-type.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';

@ApiTags('Deliver - Incident Types')
@Controller('deliver/incident-types')
@UseGuards(AuthGuard, ResourceGuard)
@Roles({ roles: [`realm:${UserRole.Deliverer}`], mode: RoleMatchingMode.ANY })
@ApiBearerAuth()
export class IncidentTypeDeliverController {
  constructor(private readonly incidentTypeService: IncidentTypeService) {}

  @Get()
  @ApiOperation({
    summary: 'Lister les types d’incidents',
    description:
      'Catalogue des motifs d’incident utilisables lors de la clôture (ex: absent, adresse introuvable). Obligatoire si incident déclaré avec type de complétion FULL/PARTIAL/NONE.',
  })
  async findAll(): Promise<IncidentType[]> {
    return await this.incidentTypeService.findAllWithoutPagination();
  }
}
