import {
  ClassSerializerInterceptor,
  Controller,
  ForbiddenException,
  Get,
  NotFoundException,
  Param,
  ParseU<PERSON><PERSON>ipe,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { DateTime } from 'luxon';
import { Roles } from 'nest-keycloak-connect';
import { TourProgressDto } from '../../../application/dto/tour/tour-progress.dto';
import { TourService } from '../../../application/service/tour.service';
import { TourEntity } from '../../../domain/entity/tour.entity';
import { UserEntity } from '../../../domain/entity/user.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { CurrentUser } from '../../decorator/current-user.decorator';
import { DeliveryNoteRepository } from '../../repository/delivery-note.repository';
import { TourAssignmentRepository } from '../../repository/tour-assignment.repository';
import { TourRepository } from '../../repository/tour.repository';
import { S3Service } from '../../service/s3.service';

@ApiTags('Deliver - Tours')
@Controller('deliver/tours')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Deliverer}`] })
export class TourDeliverController {
  constructor(
    private readonly tourService: TourService,
    private readonly tourRepository: TourRepository,
    private readonly tourAssignmentRepository: TourAssignmentRepository,
    private readonly deliveryNoteRepository: DeliveryNoteRepository,
    private readonly s3Service: S3Service,
  ) {}

  @Get('today')
  @ApiOperation({
    summary: 'Mes tournées du jour',
    description:
      'Retourne les tournées affectées au livreur pour la date du jour (fusion par originalNumber + date). Sert d’écran d’accueil opérateur.',
  })
  @ApiResponse({
    status: 200,
    type: [TourEntity],
    description: 'Liste des tournées affectées au livreur pour aujourd’hui',
  })
  async getAssignedToursForCurrentUserToday(@CurrentUser() user: UserEntity): Promise<TourEntity[]> {
    const today = DateTime.now().toISODate();

    return this.tourService.getAllToursForUserAndDate(user, today);
  }

  @Get('progress/:date')
  @ApiOperation({
    summary: 'Avancement des tournées par date',
    description:
      'Agrège le pourcentage de complétion (arrêts livrés/incidents) pour les tournées affectées au livreur à une date donnée (clé: originalNumber + deliveryDate).',
  })
  @ApiParam({
    name: 'date',
    type: String,
    description: 'Date de livraison (YYYY-MM-DD)',
    example: '2024-12-01',
  })
  @ApiResponse({ status: 200, type: [TourProgressDto] })
  async getToursProgressForDate(
    @Param('date') date: string,
    @CurrentUser() user: UserEntity,
  ): Promise<TourProgressDto[]> {
    return this.tourService.getToursProgressForUserAndDate(user, date);
  }

  @Get(':tourId/delivery-notes/:deliveryNoteId/download')
  @ApiOperation({
    summary: 'Télécharger un BL (PDF) d’une tournée',
    description:
      'Permet au livreur de récupérer le PDF du bon de livraison rattaché à un arrêt de la tournée si elle lui est bien assignée (vérification affectation par originalNumber + date).',
  })
  @ApiParam({ name: 'tourId', type: String, description: 'ID de la tournée' })
  @ApiParam({
    name: 'deliveryNoteId',
    type: String,
    description: 'ID du bon de livraison',
  })
  @ApiResponse({
    status: 200,
    description: 'BL téléchargé avec succès',
  })
  @ApiResponse({
    status: 404,
    description: 'Tournée, BL ou fichier introuvable',
  })
  async downloadDeliveryNote(
    @Param('tourId', ParseUUIDPipe) tourId: string,
    @Param('deliveryNoteId', ParseUUIDPipe) deliveryNoteId: string,
    @CurrentUser() user: UserEntity,
    @Res() res: Response,
  ): Promise<void> {
    const tour = await this.tourRepository.findOne({
      where: { id: tourId },
      relations: [],
    });

    const tourDate = DateTime.fromISO(tour.deliveryDate);

    // Check if user has access to this tour
    const userAssignments = await this.tourAssignmentRepository.findAssignmentsByUserAndDate(
      user.id,
      tourDate.toISODate(),
    );

    if (!tour) {
      throw new NotFoundException('Tour not found');
    }

    const hasAccess = userAssignments.some(
      (assignment) => assignment.tourIdentifier.originalNumber === tour.tourIdentifier.originalNumber,
    );

    if (!hasAccess) {
      throw new ForbiddenException('You do not have access to this tour');
    }

    // Get the delivery note with its file
    const deliveryNote = await this.deliveryNoteRepository.findOne({
      where: { id: deliveryNoteId, stop: { tourId } },
      relations: ['file'],
    });

    if (!deliveryNote) {
      throw new NotFoundException('Delivery note not found in this tour');
    }

    if (!deliveryNote.file || !deliveryNote.fileId) {
      throw new NotFoundException('No file associated with this delivery note');
    }

    // Download file from S3
    const buffer = await this.s3Service.getObject(deliveryNote.file.s3fileKey);

    // Set response headers
    res.setHeader('Content-Type', deliveryNote.file.contentType || 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${encodeURIComponent(deliveryNote.file.originalFilename)}"`,
    );
    res.setHeader('Content-Length', deliveryNote.file.fileSize.toString());

    res.send(buffer);
  }
}
