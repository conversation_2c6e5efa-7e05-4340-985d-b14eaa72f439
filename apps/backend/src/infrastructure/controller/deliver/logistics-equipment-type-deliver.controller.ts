import { ClassSerializerInterceptor, Controller, Get, Param, ParseUUIDPipe, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PaginationParamsDto, PaginatedResponseDto } from '../../../application/dto/pagination.dto';
import { LogisticsEquipmentTypeService } from '../../../application/service/logistics-equipment-type.service';
import { LogisticsEquipmentTypeEntity } from '../../../domain/entity/logistics-equipment-type.entity';
import { PaginationQuery } from '../../decorator/pagination.decorator';
import { ListBuilder } from '../../../application/dto/list-builder';

@ApiTags('Deliver - Logistics Equipment Types')
@Controller('deliver/logistics-equipment-types')
@UseInterceptors(ClassSerializerInterceptor)
export class LogisticsEquipmentTypeDeliverController {
  constructor(private readonly logisticsEquipmentTypeService: LogisticsEquipmentTypeService) {}

  @Get()
  @ApiOperation({
    summary: 'Lister les types d’agrès logistiques',
    description:
      'Référentiel des agrès (palettes, rolls, colis) utilisé pour la saisie des reprises à la clôture. Paginé.',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(
    @PaginationQuery() pagination: PaginationParamsDto,
  ): Promise<PaginatedResponseDto<LogisticsEquipmentTypeEntity>> {
    const { items, meta } = await this.logisticsEquipmentTypeService.findAllPaginated(pagination);

    return new ListBuilder(LogisticsEquipmentTypeEntity).items(items).meta(meta).build();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Détail d’un type d’agrès',
    description: 'Récupère un type d’agrès par identifiant pour affichage ou contrôle de saisie.',
  })
  @ApiResponse({ status: 200, type: LogisticsEquipmentTypeEntity })
  @ApiResponse({
    status: 404,
    description: 'Type d’agrès introuvable',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<LogisticsEquipmentTypeEntity> {
    return this.logisticsEquipmentTypeService.findById(id);
  }
}
