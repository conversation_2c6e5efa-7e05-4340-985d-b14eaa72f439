import { Body, Controller, Logger, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard, ResourceGuard, RoleMatchingMode, Roles } from 'nest-keycloak-connect';
import { CompleteStopDeliveryDto } from '../../../application/dto/stop/complete-stop-delivery.dto';
import { LoadStopDto, PreloadStopDto } from '../../../application/dto/stop/load-stop.dto';
import { CompleteStopDeliveryUseCase } from '../../../application/use-case/complete-stop-delivery.use-case';
import { LoadStopUseCase } from '../../../application/use-case/load-stop.use-case';
import { PreloadStopUseCase } from '../../../application/use-case/preload-stop.use-case';
import { StopEntity } from '../../../domain/entity/stop.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';

@ApiTags('Deliver - Stops')
@Controller('deliver/stops')
@UseGuards(AuthGuard, ResourceGuard)
@Roles({ roles: [`realm:${UserRole.Deliverer}`], mode: RoleMatchingMode.ANY })
@ApiBearerAuth()
export class StopDeliverController {
  private readonly logger = new Logger(StopDeliverController.name);

  constructor(
    private readonly completeStopDeliveryUseCase: CompleteStopDeliveryUseCase,
    private readonly loadStopUseCase: LoadStopUseCase,
    private readonly preloadStopUseCase: PreloadStopUseCase,
  ) {}

  @Post(':id/complete')
  @ApiOperation({
    summary: 'Clôturer une livraison à l’arrêt',
    description:
      'Règles métier: 1) Preuve requise: signature OU photo (obligatoire photo si lieu sécurisé). 2) Gestion incident: FULL/PARTIAL/NONE doit être précisé si incident. 3) Reprise d’agrès optionnelle: palettes/rolls/colis. Les fichiers sont stockés S3 avec métadonnées.',
  })
  async completeDelivery(@Param('id') stopId: string, @Body() dto: CompleteStopDeliveryDto): Promise<StopEntity> {
    try {
      return await this.completeStopDeliveryUseCase.execute(stopId, dto);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Post(':id/load')
  @ApiOperation({
    summary: 'Charger un arrêt',
    description:
      'Pointage de chargement au dépôt: enregistre les cartons/colis chargés par BL. Sert de base pour comparer au déchargement et détecter les écarts.',
  })
  async loadStop(@Param('id') stopId: string, @Body() dto: LoadStopDto): Promise<StopEntity> {
    return await this.loadStopUseCase.execute(stopId, dto);
  }

  @Post(':id/preload')
  @ApiOperation({
    summary: 'Pré-charger un arrêt',
    description:
      'Pré-saisie du chargement avant passage au quai. Utile pour préparer la tournée; peut être corrigé lors du chargement réel.',
  })
  async preloadStop(@Param('id') stopId: string, @Body() dto: PreloadStopDto): Promise<StopEntity> {
    return await this.preloadStopUseCase.execute(stopId, dto);
  }
}
