import { ClassSerializerInterceptor, Controller, Get, Param, ParseUUIDPipe, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import { VehicleService } from '../../../application/service/vehicle.service';
import { VehicleEntity } from '../../../domain/entity/vehicle.entity';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('Deliver - Vehicles')
@Controller('deliver/vehicles')
@UseInterceptors(ClassSerializerInterceptor)
export class VehicleDeliverController {
  constructor(private readonly vehicleService: VehicleService) {}

  @Get()
  @ApiOperation({
    summary: 'Lister les véhicules disponibles',
    description: 'Permet à l’opérateur de sélectionner son véhicule pour la tournée. Résultats paginés.',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(@PaginationQuery() pagination: PaginationParamsDto): Promise<PaginatedResponseDto<VehicleEntity>> {
    const { items, meta } = await this.vehicleService.findAllPaginated(pagination);

    return new ListBuilder(VehicleEntity).items(items).meta(meta).build();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Détail d’un véhicule',
    description: 'Informations d’identification du véhicule sélectionné pour la tournée.',
  })
  @ApiResponse({ status: 200, type: VehicleEntity })
  @ApiResponse({
    status: 404,
    description: 'Véhicule introuvable',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<VehicleEntity> {
    return this.vehicleService.findById(id);
  }
}
