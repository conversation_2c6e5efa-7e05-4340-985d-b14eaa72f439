import { ClassSerializerInterceptor, Controller, Get, Param, Query, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import { DateRangeQueryDto } from '../../../application/dto/tour/date-range-query.dto';
import { StopService } from '../../../application/service/stop.service';
import { StopEntity } from '../../../domain/entity/stop.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('manager/stops')
@Controller('manager/stops')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class StopManagerController {
  constructor(private readonly stopService: StopService) {}

  @Get('by-date/:date')
  @ApiOperation({
    summary: 'Récupérer les arrêts pour une date de livraison spécifique',
  })
  @ApiParam({
    name: 'date',
    type: String,
    description: 'Date de livraison (YYYY-MM-DD)',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async getStopsByDeliveryDate(
    @Param('date') date: string,
    @PaginationQuery() pagination: PaginationParamsDto,
    @Query() filters: Record<string, string>,
  ): Promise<PaginatedResponseDto<StopEntity>> {
    const { items, meta } = await this.stopService.getPaginatedStopsByDeliveryDate(date, pagination, filters);

    return new ListBuilder(StopEntity).items(items).meta(meta).build();
  }

  @Get('today')
  @ApiOperation({
    summary: 'Arrêts du jour',
    description: 'Suivi en temps réel des arrêts planifiés.',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async getTodayStops(
    @PaginationQuery() pagination: PaginationParamsDto,
    @Query() filters: Record<string, string>,
  ): Promise<PaginatedResponseDto<StopEntity>> {
    const { items, meta } = await this.stopService.getTodayStops(pagination, filters);

    return new ListBuilder(StopEntity).items(items).meta(meta).build();
  }

  @Get('by-date-range')
  @ApiOperation({
    summary: 'Arrêts par plage de dates',
    description: 'Analyse multi-jours pour supervision et reporting.',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async getStopsByDateRange(
    @Query() dateRange: DateRangeQueryDto,
    @PaginationQuery() pagination: PaginationParamsDto,
    @Query() filters: Record<string, string>,
  ): Promise<PaginatedResponseDto<StopEntity>> {
    const { items, meta } = await this.stopService.getPaginatedStopsByDateRange(
      dateRange.startDate,
      dateRange.endDate,
      pagination,
      filters,
    );

    return new ListBuilder(StopEntity).items(items).meta(meta).build();
  }
}
