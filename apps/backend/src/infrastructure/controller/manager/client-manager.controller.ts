import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Patch,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { UpdateClientDto } from '../../../application/dto/client/update-client.dto';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import { ClientService } from '../../../application/service/client.service';
import { ClientEntity } from '../../../domain/entity/client.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('manager/clients')
@Controller('manager/clients')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class ClientManagerController {
  constructor(private readonly clientService: ClientService) {}

  @Get()
  @ApiOperation({ summary: 'Get all clients (paginated)' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(@PaginationQuery() pagination: PaginationParamsDto): Promise<PaginatedResponseDto<ClientEntity>> {
    const { items, meta } = await this.clientService.findAll(pagination);

    return new ListBuilder(ClientEntity).items(items).meta(meta).build();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a client by ID' })
  @ApiResponse({ status: 200, type: ClientEntity })
  @ApiResponse({ status: 404, description: 'Client not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ClientEntity> {
    return this.clientService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Mettre à jour un client (email/adresse)' })
  @ApiResponse({ status: 200, type: ClientEntity })
  @ApiResponse({ status: 404, description: 'Client not found' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateDto: UpdateClientDto): Promise<ClientEntity> {
    return this.clientService.update(id, updateDto);
  }
}
