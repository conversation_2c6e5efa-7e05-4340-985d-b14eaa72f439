import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import { CreateUserRequest } from '../../../application/dto/user/create-user-request';
import { ResetPasswordRequest } from '../../../application/dto/user/reset-password-request.dto';
import { UpdateUserRequest } from '../../../application/dto/user/update-user-request.dto';
import { ColorService } from '../../../application/service/color.service';
import { UserQueryService } from '../../../application/service/user-query.service';
import { UserService } from '../../../application/service/user.service';
import { UserEntity } from '../../../domain/entity/user.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('Manager - Users')
@Controller('manager/users')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class UserManagerController {
  constructor(
    private readonly userService: UserService,
    private readonly userQueryService: UserQueryService,
    private readonly colorService: ColorService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Créer un utilisateur',
    description:
      'Création Keycloak + profil applicatif (rôles Manager/Operator). En cas de doublon email/username, rejet.',
  })
  @ApiResponse({
    status: 201,
    type: UserEntity,
    description: 'Utilisateur créé',
  })
  @ApiResponse({
    status: 409,
    description: 'User with this username or email already exists',
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createUser(@Body() createUserDto: CreateUserRequest): Promise<UserEntity> {
    return this.userService.createUser(createUserDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all users (paginated)' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(
    @PaginationQuery() pagination: PaginationParamsDto,
    @Query() queryParams: Record<string, any>,
  ): Promise<PaginatedResponseDto<UserEntity>> {
    const { items, meta } = await this.userQueryService.findAllPaginated(pagination, queryParams);

    return new ListBuilder(UserEntity).items(items).meta(meta).build();
  }

  @Get('default-colors')
  @ApiOperation({
    summary: 'Palette de couleurs par défaut',
    description: 'Codes hex proposés pour différencier les opérateurs sur le planning.',
  })
  @ApiResponse({
    status: 200,
    type: [String],
    description: 'Palette de codes hex',
  })
  async getDefaultColors(): Promise<string[]> {
    return this.colorService.getColorPalette();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Détail utilisateur' })
  @ApiResponse({ status: 200, type: UserEntity })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<UserEntity> {
    return this.userQueryService.getOneById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Mettre à jour un utilisateur' })
  @ApiResponse({ status: 200, type: UserEntity })
  @ApiResponse({ status: 404, description: 'User not found' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateUserDto: UpdateUserRequest): Promise<UserEntity> {
    return this.userService.updateUser(id, updateUserDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user' })
  @ApiResponse({
    status: 204,
    description: 'User deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async remove(@Param('id', ParseUUIDPipe) _id: string): Promise<void> {
    // TODO: Implement user deletion logic
    throw new Error('User deletion not yet implemented');
  }

  @Post(':id/reset-password')
  @ApiOperation({
    summary: 'Réinitialiser le mot de passe',
    description: 'Déclenche une réinitialisation via Keycloak.',
  })
  @ApiResponse({
    status: 200,
    description: 'Password reset successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async resetUserPassword(
    @Param('id', ParseUUIDPipe) userId: string,
    @Body() resetPasswordDto: ResetPasswordRequest,
  ): Promise<void> {
    return this.userService.resetUserPassword(userId, resetPasswordDto.password, resetPasswordDto.temporary);
  }
}
