import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DateTime } from 'luxon';
import { Roles } from 'nest-keycloak-connect';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import { DateRangeQueryDto } from '../../../application/dto/tour/date-range-query.dto';
import { OptionalDateRangeQueryDto } from '../../../application/dto/tour/optional-date-range-query.dto';
import { TourProgressDto } from '../../../application/dto/tour/tour-progress.dto';
import { TourIncidentDto } from '../../../application/dto/tour/tour-incident.dto';
import { TourService } from '../../../application/service/tour.service';
import { StopService } from '../../../application/service/stop.service';
import { StopEntity } from '../../../domain/entity/stop.entity';
import { TourIdentifier } from '../../../domain/entity/tour-identifier';
import { TourEntity } from '../../../domain/entity/tour.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('Manager - Tours')
@Controller('manager/tours')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class TourManagerController {
  constructor(
    private readonly tourService: TourService,
    private readonly stopService: StopService,
  ) {}

  @Get('by-date/:date')
  @ApiOperation({
    summary: 'Lister les tournées d’une date',
    description: 'Vue planning pour gestionnaire, filtrable. Identité tournée = originalNumber + deliveryDate.',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async getToursByDate(
    @Param('date') date: string,
    @PaginationQuery() pagination: PaginationParamsDto,
    @Query() filters: Record<string, string>,
  ): Promise<PaginatedResponseDto<TourEntity>> {
    const { items, meta } = await this.tourService.getPaginatedToursByDate(date, pagination, filters);

    return new ListBuilder(TourEntity).items(items).meta(meta).build();
  }

  @Get('today')
  @ApiOperation({
    summary: 'Tournées du jour (manager)',
    description: 'Surveillance en temps réel des tournées affectées ce jour avec filtres.',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async getTodayTours(
    @PaginationQuery() pagination: PaginationParamsDto,
    @Query() filters: Record<string, string>,
  ): Promise<PaginatedResponseDto<TourEntity>> {
    const today = DateTime.now().toISODate();

    const { items, meta } = await this.tourService.getPaginatedToursByDate(today, pagination, filters);

    return new ListBuilder(TourEntity).items(items).meta(meta).build();
  }

  @Get('by-date-range')
  @ApiOperation({
    summary: 'Tournées par plage de dates',
    description: 'Analyse multi-jours pour supervision, exports et KPI.',
  })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async getToursByDateRange(
    @Query() dateRange: DateRangeQueryDto,
    @PaginationQuery() pagination: PaginationParamsDto,
  ): Promise<PaginatedResponseDto<TourEntity>> {
    const { items, meta } = await this.tourService.getPaginatedToursByDateRange(
      dateRange.startDate,
      dateRange.endDate,
      pagination,
    );

    return new ListBuilder(TourEntity).items(items).meta(meta).build();
  }

  @Get('progress/:date')
  @ApiOperation({
    summary: 'Avancement global des tournées (par date)',
    description: 'Consolide le taux de complétion et incidents toutes tournées pour la date donnée.',
  })
  @ApiParam({
    name: 'date',
    type: String,
    description: 'Date de livraison (YYYY-MM-DD)',
    example: '2024-12-01',
  })
  @ApiResponse({ status: 200, type: [TourProgressDto] })
  async getToursProgressForDate(@Param('date') date: string): Promise<TourProgressDto[]> {
    return this.tourService.getToursProgressForDate(date);
  }

  @Get('identifiers/distinct')
  @ApiOperation({
    summary: 'Identifiants distincts de tournées',
    description:
      'Liste des originalNumber distincts, optionnellement filtrés par période, pour affectations et filtres UI.',
  })
  @ApiResponse({ status: 200, type: [TourIdentifier] })
  async getDistinctTourIdentifiers(@Query() dateRangeQuery?: OptionalDateRangeQueryDto): Promise<TourIdentifier[]> {
    return this.tourService.getDistinctTourIdentifiers(dateRangeQuery?.startDate, dateRangeQuery?.endDate);
  }

  @Get(':id/stops')
  @ApiOperation({
    summary: 'Arrêts d’une tournée (paginé)',
    description: 'Liste des arrêts pour consultation et contrôle qualité.',
  })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  @ApiResponse({ status: 404, description: 'Tour not found' })
  async getTourStops(
    @Param('id', ParseUUIDPipe) id: string,
    @PaginationQuery() pagination: PaginationParamsDto,
    @Query() filters: Record<string, string>,
  ): Promise<PaginatedResponseDto<StopEntity>> {
    const { items, meta } = await this.stopService.getStopsByTourId(id, pagination, filters);

    return new ListBuilder(StopEntity).items(items).meta(meta).build();
  }

  @Get(':id/incidents')
  @ApiOperation({
    summary: 'Incidents d’une tournée',
    description: 'Détail des incidents saisis à la clôture.',
  })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({ status: 200, type: [TourIncidentDto] })
  @ApiResponse({ status: 404, description: 'Tour not found' })
  async getTourIncidents(@Param('id', ParseUUIDPipe) id: string): Promise<TourIncidentDto[]> {
    return this.tourService.getTourIncidents(id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Récupérer une tournée par son ID' })
  @ApiParam({ name: 'id', type: String, format: 'uuid' })
  @ApiResponse({ status: 200, type: TourEntity })
  @ApiResponse({ status: 404, description: 'Tour not found' })
  async getTourById(@Param('id', ParseUUIDPipe) id: string): Promise<TourEntity> {
    return this.tourService.findTourById(id);
  }

  @Get(':deliveryDate/equipment-discrepancies')
  @ApiOperation({
    summary: 'Get tours with equipment discrepancies for a specific date',
  })
  @ApiParam({
    name: 'deliveryDate',
    description: 'Delivery date (YYYY-MM-DD)',
    example: '2024-01-15',
  })
  @ApiResponse({
    status: 200,
    type: [TourEntity],
    description: 'Tours with equipment discrepancies',
  })
  @ApiResponse({ status: 400, description: 'Invalid date format' })
  async getToursWithEquipmentDiscrepancies(@Param('deliveryDate') deliveryDate: string): Promise<TourEntity[]> {
    return this.tourService.getToursWithEquipmentDiscrepancies(deliveryDate);
  }
}
