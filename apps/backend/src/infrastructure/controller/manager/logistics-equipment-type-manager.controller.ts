import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Patch,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { CreateLogisticsEquipmentTypeDto } from '../../../application/dto/logistics-equipment-type/create-logistics-equipment-type.dto';
import { UpdateLogisticsEquipmentTypeDto } from '../../../application/dto/logistics-equipment-type/update-logistics-equipment-type.dto';
import { PaginationParamsDto, PaginatedResponseDto } from '../../../application/dto/pagination.dto';
import { LogisticsEquipmentTypeService } from '../../../application/service/logistics-equipment-type.service';
import { LogisticsEquipmentTypeEntity } from '../../../domain/entity/logistics-equipment-type.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';
import { ListBuilder } from '../../../application/dto/list-builder';

@ApiTags('manager/logistics-equipment-types')
@Controller('manager/logistics-equipment-types')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class LogisticsEquipmentTypeManagerController {
  constructor(private readonly logisticsEquipmentTypeService: LogisticsEquipmentTypeService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new logistics equipment type' })
  @ApiResponse({ status: 201, type: LogisticsEquipmentTypeEntity })
  async create(
    @Body() createLogisticsEquipmentTypeDto: CreateLogisticsEquipmentTypeDto,
  ): Promise<LogisticsEquipmentTypeEntity> {
    return this.logisticsEquipmentTypeService.create(createLogisticsEquipmentTypeDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all logistics equipment types' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(
    @PaginationQuery() pagination: PaginationParamsDto,
  ): Promise<PaginatedResponseDto<LogisticsEquipmentTypeEntity>> {
    const { items, meta } = await this.logisticsEquipmentTypeService.findAllPaginated(pagination);

    return new ListBuilder(LogisticsEquipmentTypeEntity).items(items).meta(meta).build();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a logistics equipment type by ID' })
  @ApiResponse({ status: 200, type: LogisticsEquipmentTypeEntity })
  @ApiResponse({
    status: 404,
    description: 'Logistics equipment type not found',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<LogisticsEquipmentTypeEntity> {
    return this.logisticsEquipmentTypeService.findById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Mettre à jour un type d’agrès' })
  @ApiResponse({ status: 200, type: LogisticsEquipmentTypeEntity })
  @ApiResponse({
    status: 404,
    description: 'Logistics equipment type not found',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLogisticsEquipmentTypeDto: UpdateLogisticsEquipmentTypeDto,
  ): Promise<LogisticsEquipmentTypeEntity> {
    return this.logisticsEquipmentTypeService.update(id, updateLogisticsEquipmentTypeDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a logistics equipment type' })
  @ApiResponse({
    status: 204,
    description: 'Logistics equipment type deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Logistics equipment type not found',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.logisticsEquipmentTypeService.delete(id);
  }
}
