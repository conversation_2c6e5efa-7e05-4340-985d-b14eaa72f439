import { Controller, <PERSON>, Param, Patch, UseGuards, UseInterceptors, ClassSerializerInterceptor } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard, ResourceGuard, Roles } from 'nest-keycloak-connect';
import { NotificationService } from '../../../application/service/notification.service';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { NotificationEntity } from '../../../domain/entity/notification.entity';

@ApiTags('Manager - Notifications')
@Controller('manager/notifications')
@UseInterceptors(ClassSerializerInterceptor)
@UseGuards(AuthGuard, ResourceGuard)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
@ApiBearerAuth()
export class NotificationManagerController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get notifications for a user' })
  async findByUserId(@Param('userId') userId: string): Promise<NotificationEntity[]> {
    return await this.notificationService.findByUserId(userId);
  }

  @Patch(':id/mark-read')
  @ApiOperation({ summary: 'Marquer une notification comme lue' })
  async markAsRead(@Param('id') id: string): Promise<NotificationEntity> {
    return await this.notificationService.markAsRead(id);
  }
}
