import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { ImportListParamsDto } from '../../../application/dto/import/import-list-params.dto';
import { TriggerSftpImportDto } from '../../../application/dto/import/trigger-sftp-import.dto';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto } from '../../../application/dto/pagination.dto';
import { ImportService } from '../../../application/service/import.service';
import { ImportXmlFilesFromFtpUseCase } from '../../../application/use-case/import-xml-files-from-ftp.use-case';
import { ImportEntity } from '../../../domain/entity/import.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('manager/imports')
@Controller('manager/imports')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class ImportManagerController {
  constructor(
    private readonly importService: ImportService,
    private readonly importXmlFilesFromFtpUseCase: ImportXmlFilesFromFtpUseCase,
  ) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get import status by ID' })
  @ApiResponse({ status: 200, type: ImportEntity })
  @ApiResponse({ status: 404, description: 'Import not found' })
  async getImportStatus(@Param('id', new ParseUUIDPipe()) id: string): Promise<ImportEntity> {
    return this.importService.getImportStatus(id);
  }

  @Get(':id/details')
  @ApiOperation({
    summary: 'Get detailed import information including imported files',
    description: 'Returns complete import details including file contents. Response may be large.',
  })
  @ApiResponse({ status: 200, type: ImportEntity })
  @ApiResponse({ status: 404, description: 'Import not found' })
  async getImportDetails(@Param('id', new ParseUUIDPipe()) id: string): Promise<ImportEntity> {
    return this.importService.getImportDetails(id);
  }

  @Get()
  @ApiOperation({ summary: 'Historique des imports (paginé)' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async getImportList(@PaginationQuery() params: ImportListParamsDto): Promise<PaginatedResponseDto<ImportEntity>> {
    const { items, meta } = await this.importService.getImportList(params);

    return new ListBuilder(ImportEntity).items(items).meta(meta).build();
  }

  @Get('sftp/status')
  @ApiOperation({ summary: 'Check if SFTP import is currently in progress' })
  @ApiResponse({
    status: 200,
    description: 'Import status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        isImportInProgress: {
          type: 'boolean',
          description: 'Whether an SFTP import is currently running',
        },
      },
    },
  })
  async getSftpImportStatus(): Promise<{ isImportInProgress: boolean }> {
    return {
      isImportInProgress: this.importXmlFilesFromFtpUseCase.isImportInProgress(),
    };
  }

  @Post('sftp')
  @ApiOperation({
    summary: 'Déclencher un import SFTP',
    description: 'Lance l’import XML pour une date donnée. Concurrency contrôlée.',
  })
  @ApiResponse({
    status: 201,
    type: ImportEntity,
    description: 'Import successfully triggered',
  })
  @ApiResponse({ status: 400, description: 'Invalid date format' })
  @ApiResponse({
    status: 409,
    description: 'Another import is already in progress',
  })
  @ApiResponse({ status: 500, description: 'Import failed' })
  async triggerSftpImport(@Body() triggerSftpImportDto: TriggerSftpImportDto): Promise<ImportEntity> {
    return this.importXmlFilesFromFtpUseCase.execute(triggerSftpImportDto.date);
  }
}
