import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Patch,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { CreateIncidentTypeDto, UpdateIncidentTypeDto } from '../../../application/dto/incident-type/incident-type.dto';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import { IncidentTypeService } from '../../../application/service/incident-type.service';
import { IncidentType } from '../../../domain/entity/incident-type.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('Manager - Incident Types')
@Controller('manager/incident-types')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class IncidentTypeManagerController {
  constructor(private readonly incidentTypeService: IncidentTypeService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new incident type' })
  @ApiResponse({ status: 201, type: IncidentType })
  async create(@Body() createIncidentTypeDto: CreateIncidentTypeDto): Promise<IncidentType> {
    return this.incidentTypeService.create(createIncidentTypeDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all incident types (paginated)' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(@PaginationQuery() pagination: PaginationParamsDto): Promise<PaginatedResponseDto<IncidentType>> {
    const { items, meta } = await this.incidentTypeService.findAll(pagination);

    return new ListBuilder(IncidentType).items(items).meta(meta).build();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an incident type by ID' })
  @ApiResponse({ status: 200, type: IncidentType })
  @ApiResponse({ status: 404, description: 'Incident type not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<IncidentType> {
    return this.incidentTypeService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an incident type' })
  @ApiResponse({ status: 200, type: IncidentType })
  @ApiResponse({ status: 404, description: 'Incident type not found' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateIncidentTypeDto: UpdateIncidentTypeDto,
  ): Promise<IncidentType> {
    return this.incidentTypeService.update(id, updateIncidentTypeDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Supprimer un type d’incident' })
  @ApiResponse({
    status: 204,
    description: 'Incident type deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Incident type not found' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.incidentTypeService.remove(id);
  }
}
