import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  Param,
  ParseU<PERSON><PERSON>ipe,
  Patch,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { ListBuilder } from '../../../application/dto/list-builder';
import { PaginatedResponseDto, PaginationParamsDto } from '../../../application/dto/pagination.dto';
import {
  CreateTourAssignmentDto,
  UpdateTourAssignmentDto,
} from '../../../application/dto/tour-assignment/tour-assignment.dto';
import { TourAssignmentService } from '../../../application/service/tour-assignment.service';
import { TourAssignmentEntity } from '../../../domain/entity/tour-assignment.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';

@ApiTags('Manager - Tour Assignments')
@Controller('manager/tour-assignments')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class TourAssignmentManagerController {
  constructor(private readonly service: TourAssignmentService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tour assignment' })
  @ApiResponse({ status: 201, type: TourAssignmentEntity })
  create(@Body() createDto: CreateTourAssignmentDto): Promise<TourAssignmentEntity> {
    return this.service.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tour assignments' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(
    @PaginationQuery() pagination: PaginationParamsDto,
  ): Promise<PaginatedResponseDto<TourAssignmentEntity>> {
    const { items, meta } = await this.service.findAllPaginated(pagination);

    return new ListBuilder(TourAssignmentEntity).items(items).meta(meta).build();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a tour assignment by ID' })
  @ApiResponse({ status: 200, type: TourAssignmentEntity })
  @ApiResponse({ status: 404, description: 'Tour assignment not found' })
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<TourAssignmentEntity> {
    return this.service.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Mettre à jour une affectation',
    description: 'Permet de réassigner ou corriger une affectation.',
  })
  @ApiResponse({ status: 200, type: TourAssignmentEntity })
  @ApiResponse({ status: 404, description: 'Tour assignment not found' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateTourAssignmentDto,
  ): Promise<TourAssignmentEntity> {
    return this.service.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tour assignment' })
  @ApiResponse({ status: 200 })
  @ApiResponse({ status: 404, description: 'Tour assignment not found' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.service.delete(id);
  }
}
