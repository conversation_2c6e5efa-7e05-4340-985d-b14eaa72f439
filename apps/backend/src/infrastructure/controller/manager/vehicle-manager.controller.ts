import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Patch,
  Post,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from 'nest-keycloak-connect';
import { CreateVehicleDto } from '../../../application/dto/vehicle/create-vehicle.dto';
import { UpdateVehicleDto } from '../../../application/dto/vehicle/update-vehicle.dto';
import { PaginationParamsDto, PaginatedResponseDto } from '../../../application/dto/pagination.dto';
import { VehicleService } from '../../../application/service/vehicle.service';
import { VehicleEntity } from '../../../domain/entity/vehicle.entity';
import { UserRole } from '../../../domain/enum/user-role.enum';
import { PaginationQuery } from '../../decorator/pagination.decorator';
import { ListBuilder } from '../../../application/dto/list-builder';

@ApiTags('manager/vehicles')
@Controller('manager/vehicles')
@UseInterceptors(ClassSerializerInterceptor)
@Roles({ roles: [`realm:${UserRole.Manager}`, `realm:${UserRole.Admin}`] })
export class VehicleManagerController {
  constructor(private readonly vehicleService: VehicleService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new vehicle' })
  @ApiResponse({ status: 201, type: VehicleEntity })
  async create(@Body() createVehicleDto: CreateVehicleDto): Promise<VehicleEntity> {
    return this.vehicleService.create(createVehicleDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all vehicles' })
  @ApiResponse({ status: 200, type: PaginatedResponseDto })
  async findAll(@PaginationQuery() pagination: PaginationParamsDto): Promise<PaginatedResponseDto<VehicleEntity>> {
    const { items, meta } = await this.vehicleService.findAllPaginated(pagination);

    return new ListBuilder(VehicleEntity).items(items).meta(meta).build();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Détail véhicule',
    description: 'Informations d’identification et capacités.',
  })
  @ApiResponse({ status: 200, type: VehicleEntity })
  @ApiResponse({
    status: 404,
    description: 'Vehicle not found',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<VehicleEntity> {
    return this.vehicleService.findById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a vehicle' })
  @ApiResponse({ status: 200, type: VehicleEntity })
  @ApiResponse({
    status: 404,
    description: 'Vehicle not found',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateVehicleDto: UpdateVehicleDto,
  ): Promise<VehicleEntity> {
    return this.vehicleService.update(id, updateVehicleDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a vehicle' })
  @ApiResponse({
    status: 204,
    description: 'Vehicle deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Vehicle not found',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.vehicleService.delete(id);
  }
}
