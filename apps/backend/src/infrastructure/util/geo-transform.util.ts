import { Point } from 'typeorm';
import { Geometry as WKTPoint } from 'wkx';

export interface TransformContext {
  value: string | Point | null;
}

/**
 * Transforms a hex WKB string to a TypeORM Point object
 */
export const transformPoint = ({ value }: TransformContext): Point | null => {
  // Handle wkb
  if (typeof value === 'string') {
    const wkb = Buffer.from(value, 'hex');

    const parsed = WKTPoint.parse(wkb);
    const geoJSON = parsed.toGeoJSON() as Point;

    return {
      type: 'Point',
      coordinates: [geoJSON.coordinates[0], geoJSON.coordinates[1]],
    };
  }

  return value;
};
