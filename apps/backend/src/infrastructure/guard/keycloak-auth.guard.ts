import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { AuthGuard as KeycloakConnectAuthGuard } from 'nest-keycloak-connect';

@Injectable()
export class KeycloakAuthGuard implements CanActivate {
  constructor(private readonly keycloakAuthGuard: KeycloakConnectAuthGuard) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    return this.keycloakAuthGuard.canActivate(context);
  }
}
