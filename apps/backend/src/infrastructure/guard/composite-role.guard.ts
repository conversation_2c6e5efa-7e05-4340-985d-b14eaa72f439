import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RoleGuard as KeycloakRoleGuard } from 'nest-keycloak-connect';

@Injectable()
export class CompositeRoleGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private keycloakRoleGuard: KeycloakRoleGuard,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // Si l'utilisateur a été authentifié par clé API
    if (request.user && request.user.roles) {
      const roles = this.reflector.get<string[]>('roles', context.getHandler());

      if (!roles) {
        return true;
      }

      return roles.some((role) => request.user.roles.includes(role));
    }

    return this.keycloakRoleGuard.canActivate(context);
  }
}
