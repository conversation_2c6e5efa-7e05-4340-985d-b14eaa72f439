### Test de création d'utilisateur avec rôle
POST http://localhost:3000/api/user
Content-Type: application/json

{
  "username": "test.manager",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "Manager",
  "locale": "fr",
  "role": "lrg-bl-manager",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Test de mise à jour d'utilisateur - modifier le rôle
PATCH http://localhost:3000/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "role": "lrg-bl-deliverer",
  "firstName": "Test Updated",
  "locale": "en"
}

### Test de mise à jour d'utilisateur - modifier seulement le rôle
PATCH http://localhost:3000/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "role": "lrg-bl-receptionist"
}

### Test de mise à jour d'utilisateur - modifier seulement les infos personnelles
PATCH http://localhost:3000/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "color": "#FF5733"
}

### Test d'erreur - rôle invalide
PATCH http://localhost:3000/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "role": "invalid-role"
}

### Test d'erreur - utilisateur inexistant
PATCH http://localhost:3000/api/user/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "role": "lrg-bl-manager"
}

### Test de création d'utilisateur avec rôle réceptionniste
POST http://localhost:3000/api/user
Content-Type: application/json

{
  "username": "test.receptionist",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "Receptionist",
  "role": "lrg-bl-receptionist",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Test de création d'utilisateur avec rôle livreur
POST http://localhost:3000/api/user
Content-Type: application/json

{
  "username": "test.deliverer",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "Deliverer",
  "role": "lrg-bl-deliverer",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
} 