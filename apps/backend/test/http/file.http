### Upload file
POST {{BACKEND_BASE_URL}}/api/file
Authorization: Bearer {{bearer_token}}
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; name="file"; filename="test.txt"
Content-Type: text/plain

Hello, ceci est un fichier de test.
--boundary
Content-Disposition: form-data; name="metadata"

{"description": "Fichier de test httpyac", "type": "demo"}

> {% client.global.set("file_id", response.body.id) %}
--boundary--

### Get file metadata by id
# Remplacez {{file_id}} par l'id retourné lors de l'upload
GET {{BACKEND_BASE_URL}}/api/file/{{file_id}}
Authorization: Bearer {{bearer_token}}

### Download file by id
# Remplacez {{file_id}} par l'id retourné lors de l'upload
GET {{BACKEND_BASE_URL}}/api/file/{{file_id}}/download
Authorization: Bearer {{bearer_token}}

### List files (paginated)
GET {{BACKEND_BASE_URL}}/api/file?page=1&limit=2&sortBy=createdAt&sortOrder=desc
Authorization: Bearer {{bearer_token}} 