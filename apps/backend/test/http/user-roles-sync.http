### Tests de synchronisation des rôles utilisateurs

### Récupérer un utilisateur - vérifier que les rôles sont synchronisés
GET http://localhost:3000/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}

### Récupérer l'utilisateur actuel - vérifier les rôles
GET http://localhost:3000/api/user/me
Authorization: Bearer {{bearer_token}}

### Lister tous les utilisateurs - vérifier les rôles dans la liste paginée
GET http://localhost:3000/api/user?page=1&limit=10
Authorization: Bearer {{bearer_token}}

### Test de synchronisation lors de la création avec rôle Manager
POST http://localhost:3000/api/user
Content-Type: application/json

{
  "username": "sync.test.manager",
  "email": "<EMAIL>",
  "firstName": "Sync Test",
  "lastName": "Manager",
  "locale": "fr",
  "role": "lrg-bl-manager",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Test de synchronisation lors de la création avec rôle Réceptionniste
POST http://localhost:3000/api/user
Content-Type: application/json

{
  "username": "sync.test.receptionist",
  "email": "<EMAIL>",
  "firstName": "Sync Test",
  "lastName": "Receptionist",
  "locale": "fr",
  "role": "lrg-bl-receptionist",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Test de synchronisation lors de la mise à jour de rôle
PATCH http://localhost:3000/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "role": "lrg-bl-deliverer"
}

### Test de récupération après mise à jour - vérifier la synchronisation
GET http://localhost:3000/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}

### Test de récupération par username - vérifier la synchronisation automatique
# Remplacer {{username}} par le nom d'utilisateur à tester
# Cette méthode devrait synchroniser automatiquement les données depuis Keycloak
POST http://localhost:3000/api/internal/user/findByUsername
Content-Type: application/json

{
  "username": "{{username}}"
} 