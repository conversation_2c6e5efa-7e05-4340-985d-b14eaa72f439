### Tests pour UserManagerController
### Nécessite un token d'authentification avec rôle Manager ou Admin

### Variables
@baseUrl = http://localhost:3000
@bearer_token = your_bearer_token_here
@user_id = user_id_here

### Créer un nouvel utilisateur
POST {{baseUrl}}/api/manager/users
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "username": "test.manager.user",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "Manager User",
  "locale": "fr",
  "role": "lrg-bl-manager",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Lister tous les utilisateurs (paginé)
GET {{baseUrl}}/api/manager/users?page=1&limit=10
Authorization: Bearer {{bearer_token}}

### Obtenir les couleurs par défaut
GET {{baseUrl}}/api/manager/users/default-colors
Authorization: Bearer {{bearer_token}}

### Obtenir un utilisateur par ID
GET {{baseUrl}}/api/manager/users/{{user_id}}
Authorization: Bearer {{bearer_token}}

### Mettre à jour un utilisateur
PATCH {{baseUrl}}/api/manager/users/{{user_id}}
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "firstName": "Updated First Name",
  "lastName": "Updated Last Name",
  "role": "lrg-bl-receptionist",
  "color": "#FF5733"
}

### Supprimer un utilisateur (non implémenté)
DELETE {{baseUrl}}/api/manager/users/{{user_id}}
Authorization: Bearer {{bearer_token}}

### Test avec recherche
GET {{baseUrl}}/api/manager/users?page=1&limit=5&search=test
Authorization: Bearer {{bearer_token}}

### Test de création avec rôle Réceptionniste
POST {{baseUrl}}/api/manager/users
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "username": "test.receptionist",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "Receptionist",
  "locale": "fr",
  "role": "lrg-bl-receptionist",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Test de création avec rôle Livreur
POST {{baseUrl}}/api/manager/users
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "username": "test.deliverer",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "Deliverer",
  "locale": "fr",
  "role": "lrg-bl-deliverer",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Test filtrage par rôle - Manager
GET {{baseUrl}}/api/manager/users?role=Manager
Authorization: Bearer {{bearer_token}}

### Test filtrage par rôle - Réceptionniste
GET {{baseUrl}}/api/manager/users?role=Receptionist
Authorization: Bearer {{bearer_token}}

### Test filtrage par rôle - Livreur
GET {{baseUrl}}/api/manager/users?role=Deliverer
Authorization: Bearer {{bearer_token}}

### Test filtrage par rôle - Aucun rôle
GET {{baseUrl}}/api/manager/users?role=null
Authorization: Bearer {{bearer_token}}

### Test filtrage avec recherche ET rôle
GET {{baseUrl}}/api/manager/users?search=john&role=Manager
Authorization: Bearer {{bearer_token}}

### Test pagination avec filtrage par rôle
GET {{baseUrl}}/api/manager/users?role=Manager&page=1&limit=5
Authorization: Bearer {{bearer_token}} 