### Test de création d'utilisateur avec Keycloak
POST http://localhost:3000/user
Content-Type: application/json

{
  "username": "john.doe",
  "email": "<EMAIL>",
  "firstName": "<PERSON>",
  "lastName": "Do<PERSON>",
  "locale": "fr",
  "color": "#FF5733",
  "createInKeycloak": true,
  "password": "SecurePassword123!"
}

### Test de création d'utilisateur sans Keycloak (SSO externe)
POST http://localhost:3000/user
Content-Type: application/json

{
  "username": "jane.smith",
  "email": "<EMAIL>",
  "firstName": "<PERSON>",
  "lastName": "Smith",
  "locale": "en",
  "createInKeycloak": false
}

### Test de création d'utilisateur avec données minimales
POST http://localhost:3000/user
Content-Type: application/json

{
  "username": "minimal.user",
  "email": "<EMAIL>",
  "createInKeycloak": true,
  "password": "MinimalPass123!"
}

### Test d'erreur - utilisateur déjà existant
POST http://localhost:3000/user
Content-Type: application/json

{
  "username": "john.doe",
  "email": "<EMAIL>",
  "createInKeycloak": true,
  "password": "AnotherPassword123!"
}

### Test d'erreur - email déjà existant
POST http://localhost:3000/user
Content-Type: application/json

{
  "username": "different.user",
  "email": "<EMAIL>",
  "createInKeycloak": true,
  "password": "AnotherPassword123!"
}

### Test d'erreur - mot de passe manquant pour Keycloak
POST http://localhost:3000/user
Content-Type: application/json

{
  "username": "no.password",
  "email": "<EMAIL>",
  "createInKeycloak": true
} 