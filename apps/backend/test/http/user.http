### Get user
GET {{BACKEND_BASE_URL}}/api/user/me
Authorization: Bear<PERSON> {{bearer_token}}

### Update user service status
PATCH {{BACKEND_BASE_URL}}/api/user/me/service-status
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "isInService": true,
  "latitude": 48.8566,
  "longitude": 2.3522
}

### Update user service status out of service
PATCH {{BACKEND_BASE_URL}}/api/user/me/service-status
Authorization: Bearer {{bearer_token}}
Content-Type: application/json

{
  "isInService": false
}

### Get user by id
GET {{BACKEND_BASE_URL}}/api/user/{{user_id}}
Authorization: Bearer {{bearer_token}}

### Get user by username
GET {{BACKEND_BASE_URL}}/api/user/username/{{user_username}}
Authorization: Bearer {{bearer_token}}

### List users (paginated)
GET {{BACKEND_BASE_URL}}/api/user?page=1&limit=10
Authorization: Bearer {{bearer_token}}
