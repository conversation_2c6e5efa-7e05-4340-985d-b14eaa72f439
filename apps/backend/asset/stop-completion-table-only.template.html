<!DOCTYPE html>
<html>

<head>
  <!-- <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script> -->
  <style>
    /*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
    @layer properties;
    @layer theme, base, components, utilities;

    @layer theme {

      :root,
      :host {
        --font-sans: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
          'Noto Color Emoji';
        --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
          monospace;
        --color-cyan-500: oklch(71.5% 0.143 215.221);
        --color-gray-800: oklch(27.8% 0.033 256.848);
        --spacing: 0.25rem;
        --text-xs: 0.75rem;
        --text-xs--line-height: calc(1 / 0.75);
        --text-sm: 0.875rem;
        --text-sm--line-height: calc(1.25 / 0.875);
        --font-weight-normal: 400;
        --font-weight-bold: 700;
        --radius-md: 0.375rem;
        --default-font-family: var(--font-sans);
        --default-mono-font-family: var(--font-mono);
      }
    }

    @layer base {

      *,
      ::after,
      ::before,
      ::backdrop,
      ::file-selector-button {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border: 0 solid;
      }

      html,
      :host {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        tab-size: 4;
        font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');
        font-feature-settings: var(--default-font-feature-settings, normal);
        font-variation-settings: var(--default-font-variation-settings, normal);
        -webkit-tap-highlight-color: transparent;
      }

      hr {
        height: 0;
        color: inherit;
        border-top-width: 1px;
      }

      abbr:where([title]) {
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-size: inherit;
        font-weight: inherit;
      }

      a {
        color: inherit;
        -webkit-text-decoration: inherit;
        text-decoration: inherit;
      }

      b,
      strong {
        font-weight: bolder;
      }

      code,
      kbd,
      samp,
      pre {
        font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);
        font-feature-settings: var(--default-mono-font-feature-settings, normal);
        font-variation-settings: var(--default-mono-font-variation-settings, normal);
        font-size: 1em;
      }

      small {
        font-size: 80%;
      }

      sub,
      sup {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }

      sub {
        bottom: -0.25em;
      }

      sup {
        top: -0.5em;
      }

      table {
        text-indent: 0;
        border-color: inherit;
        border-collapse: collapse;
      }

      :-moz-focusring {
        outline: auto;
      }

      progress {
        vertical-align: baseline;
      }

      summary {
        display: list-item;
      }

      ol,
      ul,
      menu {
        list-style: none;
      }

      img,
      svg,
      video,
      canvas,
      audio,
      iframe,
      embed,
      object {
        display: block;
        vertical-align: middle;
      }

      img,
      video {
        max-width: 100%;
        height: auto;
      }

      button,
      input,
      select,
      optgroup,
      textarea,
      ::file-selector-button {
        font: inherit;
        font-feature-settings: inherit;
        font-variation-settings: inherit;
        letter-spacing: inherit;
        color: inherit;
        border-radius: 0;
        background-color: transparent;
        opacity: 1;
      }

      :where(select:is([multiple], [size])) optgroup {
        font-weight: bolder;
      }

      :where(select:is([multiple], [size])) optgroup option {
        padding-inline-start: 20px;
      }

      ::file-selector-button {
        margin-inline-end: 4px;
      }

      ::placeholder {
        opacity: 1;
      }

      @supports (not (-webkit-appearance: -apple-pay-button)) or (contain-intrinsic-size: 1px) {
        ::placeholder {
          color: currentcolor;

          @supports (color: color-mix(in lab, red, red)) {
            color: color-mix(in oklab, currentcolor 50%, transparent);
          }
        }
      }

      textarea {
        resize: vertical;
      }

      ::-webkit-search-decoration {
        -webkit-appearance: none;
      }

      ::-webkit-date-and-time-value {
        min-height: 1lh;
        text-align: inherit;
      }

      ::-webkit-datetime-edit {
        display: inline-flex;
      }

      ::-webkit-datetime-edit-fields-wrapper {
        padding: 0;
      }

      ::-webkit-datetime-edit,
      ::-webkit-datetime-edit-year-field,
      ::-webkit-datetime-edit-month-field,
      ::-webkit-datetime-edit-day-field,
      ::-webkit-datetime-edit-hour-field,
      ::-webkit-datetime-edit-minute-field,
      ::-webkit-datetime-edit-second-field,
      ::-webkit-datetime-edit-millisecond-field,
      ::-webkit-datetime-edit-meridiem-field {
        padding-block: 0;
      }

      :-moz-ui-invalid {
        box-shadow: none;
      }

      button,
      input:where([type='button'], [type='reset'], [type='submit']),
      ::file-selector-button {
        appearance: button;
      }

      ::-webkit-inner-spin-button,
      ::-webkit-outer-spin-button {
        height: auto;
      }

      [hidden]:where(:not([hidden='until-found'])) {
        display: none !important;
      }
    }

    @layer utilities {
      .relative {
        position: relative;
      }

      .left-\[-1px\] {
        left: -1px;
      }

      .mt-2 {
        margin-top: calc(var(--spacing) * 2);
      }

      .mt-3 {
        margin-top: calc(var(--spacing) * 3);
      }

      .mt-4 {
        margin-top: calc(var(--spacing) * 4);
      }

      .flex {
        display: flex;
      }

      .grid {
        display: grid;
      }

      .inline-block {
        display: inline-block;
      }

      .h-\[25mm\] {
        height: 25mm;
      }

      .h-\[30mm\] {
        height: 30mm;
      }

      .h-full {
        height: 100%;
      }

      .w-3\/4 {
        width: calc(3/4 * 100%);
      }

      .w-\[25mm\] {
        width: 25mm;
      }

      .w-full {
        width: 100%;
      }

      .flex-shrink-0 {
        flex-shrink: 0;
      }

      .flex-grow {
        flex-grow: 1;
      }

      .flex-grow-0 {
        flex-grow: 0;
      }

      .grid-cols-\[60mm_40mm_1fr\] {
        grid-template-columns: 60mm 40mm 1fr;
      }

      .grid-cols-\[75mm_1fr\] {
        grid-template-columns: 75mm 1fr;
      }

      .grid-cols-\[100mm_1fr\] {
        grid-template-columns: 100mm 1fr;
      }

      .flex-col {
        flex-direction: column;
      }

      .flex-row {
        flex-direction: row;
      }

      .items-center {
        align-items: center;
      }

      .justify-between {
        justify-content: space-between;
      }

      .justify-center {
        justify-content: center;
      }

      .gap-1 {
        gap: calc(var(--spacing) * 1);
      }

      .gap-2 {
        gap: calc(var(--spacing) * 2);
      }

      .rounded-md {
        border-radius: var(--radius-md);
      }

      .border-2 {
        border-style: var(--tw-border-style);
        border-width: 2px;
      }

      .border-b-2 {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 2px;
      }

      .border-dashed {
        --tw-border-style: dashed;
        border-style: dashed;
      }

      .border-gray-800 {
        border-color: var(--color-gray-800);
      }

      .bg-cyan-500 {
        background-color: var(--color-cyan-500);
      }

      .object-contain {
        object-fit: contain;
      }

      .px-1 {
        padding-inline: calc(var(--spacing) * 1);
      }

      .pt-6 {
        padding-top: calc(var(--spacing) * 6);
      }

      .pb-2 {
        padding-bottom: calc(var(--spacing) * 2);
      }

      .text-center {
        text-align: center;
      }

      .text-left {
        text-align: left;
      }

      .align-middle {
        vertical-align: middle;
      }

      .text-sm {
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
      }

      .text-xs {
        font-size: var(--text-xs);
        line-height: var(--tw-leading, var(--text-xs--line-height));
      }

      .font-bold {
        --tw-font-weight: var(--font-weight-bold);
        font-weight: var(--font-weight-bold);
      }

      .font-normal {
        --tw-font-weight: var(--font-weight-normal);
        font-weight: var(--font-weight-normal);
      }
    }

    @property --tw-border-style {
      syntax: "*";
      inherits: false;
      initial-value: solid;
    }

    @property --tw-font-weight {
      syntax: "*";
      inherits: false;
    }

    @layer properties {
      @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {

        *,
        ::before,
        ::after,
        ::backdrop {
          --tw-border-style: solid;
          --tw-font-weight: initial;
        }
      }
    }
  </style>


  <style>
    /* Dimensions A4 exactes */
    @page {
      size: A4;
      /* 210mm x 297mm */
      margin: 0;
      padding: 0;
      /* Important pour contrôler totalement la mise en page */
    }

    html {
      padding: 0;
      margin: 0;
      font-family: "Arial", sans-serif;
      line-height: 1.2;
      font-size: 10pt;
    }

    h1 {
      font-size: 14pt;
      font-weight: bold;
      margin: 0;
      padding: 0;
      color: #363636
    }

    h2 {
      font-size: 12pt;
      font-weight: bold;
      margin: 0;
      padding: 0;
      color: #1c1c1c
    }

    h3 {
      font-size: 11pt;
      font-weight: bold;
      margin: 0;
      padding: 0;
    }

    body {
      width: 210mm;
      height: 297mm;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }



    hr {
      height: 2px;
      border: none;
      background-color: #363636;
    }

    .page {
      width: 210mm;
      height: 297mm;
      position: relative;
      page-break-after: always;
      overflow: hidden;
    }

    /* Styles pour multi-pages */
    @media print {
      .page {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        border: initial;
        border-radius: initial;
        width: initial;
        min-height: initial;
        box-shadow: initial;
        background: initial;
        page-break-after: always;
      }
    }

    .content {
      padding: 10mm 20mm;
    }



    .footer {
      position: absolute;
      width: 100%;
      bottom: 0;

      .footer-text-container {
        padding-left: 20mm;
        padding-right: 20mm;
      }
    }

    .footer-image {
      background-color: rgb(6, 74, 128);
      height: 10mm;
      width: 100%;
    }

    section {
      margin-bottom: 2mm;
      margin-top: 5mm;
    }
  </style>
</head>

<body>
  <div class="page">

    <div class="header"></div>

    <div class="content">


      <div class="w-full">
        <table class="w-full mt-2">
          <thead class="border-b-2 border-gray-800 border-dashed">
            <tr>
              <th class="font-normal text-left">Nombre</th>
              <th class="font-normal text-left">Unité</th>
              <th class="font-normal text-left">Désignation</th>
              <th class="font-normal text-left">Poids</th>
            </tr>
          </thead>
          <tbody>
            {{#each merchandiseItems}}
            <tr class="mt-2">
              <td>{{quantity}}</td>
              <td>{{unit}}</td>
              <td>{{designation}}</td>
              <td>{{weight}}</td>
            </tr>
            {{/each}}
          </tbody>
        </table>

        <hr class="mt-4" />

      </div>


    </div>



    <div class="footer flex flex-col">
      <div class="flex justify-between flex-grow footer-text-container pb-2">
        <p class="text-xs">
          Les conditions générales de vente s'appliquent conformément à www.galliker.com/agb.
        </p>
        <p class="text-xs">
          Page {{currentPage}} de {{totalPages}}
        </p>


      </div>
      <div class="footer-image" />
    </div>
  </div>
</body>