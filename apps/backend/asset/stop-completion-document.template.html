<!DOCTYPE html>
<html>

<head>
  <!-- <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>  -->
  <style>
    /*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
    @layer properties;
    @layer theme, base, components, utilities;

    @layer theme {

      :root,
      :host {
        --font-sans: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
          'Noto Color Emoji';
        --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
          monospace;
        --color-cyan-500: oklch(71.5% 0.143 215.221);
        --color-gray-800: oklch(27.8% 0.033 256.848);
        --spacing: 0.25rem;
        --text-xs: 0.75rem;
        --text-xs--line-height: calc(1 / 0.75);
        --text-sm: 0.875rem;
        --text-sm--line-height: calc(1.25 / 0.875);
        --font-weight-normal: 400;
        --font-weight-bold: 700;
        --radius-md: 0.375rem;
        --default-font-family: var(--font-sans);
        --default-mono-font-family: var(--font-mono);
      }
    }

    @layer base {

      *,
      ::after,
      ::before,
      ::backdrop,
      ::file-selector-button {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border: 0 solid;
      }

      html,
      :host {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        tab-size: 4;
        font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');
        font-feature-settings: var(--default-font-feature-settings, normal);
        font-variation-settings: var(--default-font-variation-settings, normal);
        -webkit-tap-highlight-color: transparent;
      }

      hr {
        height: 0;
        color: inherit;
        border-top-width: 1px;
      }

      abbr:where([title]) {
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-size: inherit;
        font-weight: inherit;
      }

      a {
        color: inherit;
        -webkit-text-decoration: inherit;
        text-decoration: inherit;
      }

      b,
      strong {
        font-weight: bolder;
      }

      code,
      kbd,
      samp,
      pre {
        font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);
        font-feature-settings: var(--default-mono-font-feature-settings, normal);
        font-variation-settings: var(--default-mono-font-variation-settings, normal);
        font-size: 1em;
      }

      small {
        font-size: 80%;
      }

      sub,
      sup {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }

      sub {
        bottom: -0.25em;
      }

      sup {
        top: -0.5em;
      }

      table {
        text-indent: 0;
        border-color: inherit;
        border-collapse: collapse;
      }

      :-moz-focusring {
        outline: auto;
      }

      progress {
        vertical-align: baseline;
      }

      summary {
        display: list-item;
      }

      ol,
      ul,
      menu {
        list-style: none;
      }

      img,
      svg,
      video,
      canvas,
      audio,
      iframe,
      embed,
      object {
        display: block;
        vertical-align: middle;
      }

      img,
      video {
        max-width: 100%;
        height: auto;
      }

      button,
      input,
      select,
      optgroup,
      textarea,
      ::file-selector-button {
        font: inherit;
        font-feature-settings: inherit;
        font-variation-settings: inherit;
        letter-spacing: inherit;
        color: inherit;
        border-radius: 0;
        background-color: transparent;
        opacity: 1;
      }

      :where(select:is([multiple], [size])) optgroup {
        font-weight: bolder;
      }

      :where(select:is([multiple], [size])) optgroup option {
        padding-inline-start: 20px;
      }

      ::file-selector-button {
        margin-inline-end: 4px;
      }

      ::placeholder {
        opacity: 1;
      }

      @supports (not (-webkit-appearance: -apple-pay-button)) or (contain-intrinsic-size: 1px) {
        ::placeholder {
          color: currentcolor;

          @supports (color: color-mix(in lab, red, red)) {
            color: color-mix(in oklab, currentcolor 50%, transparent);
          }
        }
      }

      textarea {
        resize: vertical;
      }

      ::-webkit-search-decoration {
        -webkit-appearance: none;
      }

      ::-webkit-date-and-time-value {
        min-height: 1lh;
        text-align: inherit;
      }

      ::-webkit-datetime-edit {
        display: inline-flex;
      }

      ::-webkit-datetime-edit-fields-wrapper {
        padding: 0;
      }

      ::-webkit-datetime-edit,
      ::-webkit-datetime-edit-year-field,
      ::-webkit-datetime-edit-month-field,
      ::-webkit-datetime-edit-day-field,
      ::-webkit-datetime-edit-hour-field,
      ::-webkit-datetime-edit-minute-field,
      ::-webkit-datetime-edit-second-field,
      ::-webkit-datetime-edit-millisecond-field,
      ::-webkit-datetime-edit-meridiem-field {
        padding-block: 0;
      }

      :-moz-ui-invalid {
        box-shadow: none;
      }

      button,
      input:where([type='button'], [type='reset'], [type='submit']),
      ::file-selector-button {
        appearance: button;
      }

      ::-webkit-inner-spin-button,
      ::-webkit-outer-spin-button {
        height: auto;
      }

      [hidden]:where(:not([hidden='until-found'])) {
        display: none !important;
      }
    }

    @layer utilities {
      .relative {
        position: relative;
      }

      .left-\[-1px\] {
        left: -1px;
      }

      .mt-2 {
        margin-top: calc(var(--spacing) * 2);
      }

      .mt-3 {
        margin-top: calc(var(--spacing) * 3);
      }

      .mt-4 {
        margin-top: calc(var(--spacing) * 4);
      }

      .flex {
        display: flex;
      }

      .grid {
        display: grid;
      }

      .inline-block {
        display: inline-block;
      }

      .h-\[25mm\] {
        height: 25mm;
      }

      .h-\[30mm\] {
        height: 30mm;
      }

      .h-full {
        height: 100%;
      }

      .w-3\/4 {
        width: calc(3/4 * 100%);
      }

      .w-\[25mm\] {
        width: 25mm;
      }

      .w-full {
        width: 100%;
      }

      .flex-shrink-0 {
        flex-shrink: 0;
      }

      .flex-grow {
        flex-grow: 1;
      }

      .flex-grow-0 {
        flex-grow: 0;
      }

      .grid-cols-\[60mm_40mm_1fr\] {
        grid-template-columns: 60mm 40mm 1fr;
      }

      .grid-cols-\[75mm_1fr\] {
        grid-template-columns: 75mm 1fr;
      }

      .grid-cols-\[100mm_1fr\] {
        grid-template-columns: 100mm 1fr;
      }

      .flex-col {
        flex-direction: column;
      }

      .flex-row {
        flex-direction: row;
      }

      .items-center {
        align-items: center;
      }

      .justify-between {
        justify-content: space-between;
      }

      .justify-center {
        justify-content: center;
      }

      .gap-1 {
        gap: calc(var(--spacing) * 1);
      }

      .gap-2 {
        gap: calc(var(--spacing) * 2);
      }

      .rounded-md {
        border-radius: var(--radius-md);
      }

      .border-2 {
        border-style: var(--tw-border-style);
        border-width: 2px;
      }

      .border-b-2 {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 2px;
      }

      .border-dashed {
        --tw-border-style: dashed;
        border-style: dashed;
      }

      .border-gray-800 {
        border-color: var(--color-gray-800);
      }

      .bg-cyan-500 {
        background-color: var(--color-cyan-500);
      }

      .object-contain {
        object-fit: contain;
      }

      .px-1 {
        padding-inline: calc(var(--spacing) * 1);
      }

      .pt-6 {
        padding-top: calc(var(--spacing) * 6);
      }

      .pb-2 {
        padding-bottom: calc(var(--spacing) * 2);
      }

      .text-center {
        text-align: center;
      }

      .text-left {
        text-align: left;
      }

      .align-middle {
        vertical-align: middle;
      }

      .text-sm {
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
      }

      .text-xs {
        font-size: var(--text-xs);
        line-height: var(--tw-leading, var(--text-xs--line-height));
      }

      .font-bold {
        --tw-font-weight: var(--font-weight-bold);
        font-weight: var(--font-weight-bold);
      }

      .font-normal {
        --tw-font-weight: var(--font-weight-normal);
        font-weight: var(--font-weight-normal);
      }
    }

    @property --tw-border-style {
      syntax: "*";
      inherits: false;
      initial-value: solid;
    }

    @property --tw-font-weight {
      syntax: "*";
      inherits: false;
    }

    @layer properties {
      @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {

        *,
        ::before,
        ::after,
        ::backdrop {
          --tw-border-style: solid;
          --tw-font-weight: initial;
        }
      }
    }
  </style>


  <style>
    /* Dimensions A4 exactes */
    @page {
      size: A4;
      /* 210mm x 297mm */
      margin: 0;
      padding: 0;
      /* Important pour contrôler totalement la mise en page */
    }

    html {
      padding: 0;
      margin: 0;
      font-family: "Arial", sans-serif;
      line-height: 1.2;
      font-size: 10pt;
    }

    h1 {
      font-size: 14pt;
      font-weight: bold;
      margin: 0;
      padding: 0;
      color: #363636
    }

    h2 {
      font-size: 12pt;
      font-weight: bold;
      margin: 0;
      padding: 0;
      color: #1c1c1c
    }

    h3 {
      font-size: 11pt;
      font-weight: bold;
      margin: 0;
      padding: 0;
    }

    body {
      width: 210mm;
      height: 297mm;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }



    hr {
      height: 2px;
      border: none;
      background-color: #363636;
    }

    .page {
      width: 210mm;
      height: 297mm;
      position: relative;
      page-break-after: always;
      overflow: hidden;
    }

    /* Styles pour multi-pages */
    @media print {
      .page {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        border: initial;
        border-radius: initial;
        width: initial;
        min-height: initial;
        box-shadow: initial;
        background: initial;
        page-break-after: always;
      }
    }

    .content {
      padding: 10mm 20mm;
    }



    .footer {
      position: absolute;
      width: 100%;
      bottom: 0;

      .footer-text-container {
        padding-left: 20mm;
        padding-right: 20mm;
      }
    }

    .footer-image {
      background-color: rgb(6, 74, 128);
      height: 10mm;
      width: 100%;
    }

    section {
      margin-bottom: 2mm;
      margin-top: 5mm;
    }
  </style>
</head>

<body>
  <div class="page">

    <div class="header"></div>

    <div class="content">
      <div class="content-header">
        <h1>{{title}}</h1>
      </div>
      <section class="grid grid-cols-[100mm_1fr] w-full2">
        <div>
          <ul>
            <li>
              <span class="font-bold">Numéro d'expédition:</span>
              <span>{{shipmentNumber}}</span>
            </li>
            <li>
              <span class="font-bold">Réf. client:</span>
              <span>{{clientRef}}</span>
            </li>
            <li>
              <span class="font-bold">Réf. client 3:</span>
              <span>{{clientRef3}}</span>
            </li>
          </ul>
        </div>

        <div>
          <ul>
            <li>
              <h3>
                {{transportCompanyName}}
              </h3>
            </li>
            {{#each transportCompanyAddressLines}}
            <li>{{this}}</li>
            {{/each}}
            <li>{{transportCompanyPhone}}</li>
            <li>{{transportCompanyEmail}}</li>
          </ul>
        </div>
      </section>
      <hr />
      <section class="grid grid-cols-[60mm_40mm_1fr]  w-full">
        <div>
          <ul>
            <li>
              <h2>
                Expéditeur
              </h2>
            </li>
            <li>{{senderName}}</li>
            <li>{{senderCompany}}</li>
            {{#each senderAddressLines}}
            <li>{{this}}</li>
            {{/each}}
          </ul>
        </div>

        <div class="flex justify-left items-center flex-col gap-2">
          <svg version="1.0" xmlns="http://www.w3.org/2000/svg" width="18mm" height="auto"
            viewBox="0 0 1280.**********.000000" preserveAspectRatio="xMidYMid meet">
            <g transform="translate(0.000000,950.000000) scale(0.100000,-0.100000)" fill="#363636" stroke="none">
              <path
                d="M247 9489 c-90 -21 -151 -71 -189 -154 l-23 -50 -3 -2952 -2 -2953 4401 0 c4381 0 4402 0 4434 -20 18 -11 44 -37 59 -59 25 -35 26 -43 17 -78 -11 -40 -37 -71 -78 -92 -16 -8 -1188 -11 -4443 -11 l-4421 0 3 -632 3 -633 23 -47 c13 -26 43 -65 67 -87 79 -73 59 -71 868 -71 681 0 719 -1 712 -18 -33 -84 -30 -347 5 -514 120 -571 544 -983 1125 -1094 123 -24 376 -24 505 0 570 104 1010 536 1135 1116 24 111 31 369 12 448 l-14 62 2029 0 2030 0 -9 -33 c-4 -17 -8 -115 -8 -217 1 -198 13 -280 62 -430 139 -418 475 -754 896 -895 153 -51 267 -69 447 -69 379 0 705 132 975 395 266 259 409 575 431 957 6 97 -3 207 -22 269 l-6 23 644 0 c726 0 715 -1 793 71 24 22 54 61 67 87 l23 47 0 2515 0 2515 -23 51 c-15 33 -52 79 -105 131 -246 242 -1825 1707 -1861 1726 l-41 22 -925 5 -925 5 -5 240 -5 240 -31 55 c-37 66 -101 113 -174 129 -64 14 -8395 14 -8453 0z m11963 -3945 l0 -986 -22 6 c-13 3 -609 162 -1325 353 l-1303 347 0 633 0 633 1325 0 1325 0 0 -986z m-8944 -3364 c253 -65 470 -257 563 -499 42 -109 54 -181 53 -306 -2 -269 -127 -504 -346 -649 -142 -95 -276 -136 -444 -136 -288 0 -541 128 -694 353 -186 274 -175 659 26 941 108 151 298 274 471 305 33 6 71 13 85 15 50 9 210 -5 286 -24z m6812 0 c357 -90 602 -402 602 -768 0 -348 -193 -645 -500 -769 -241 -97 -542 -61 -753 91 -245 175 -399 514 -358 786 54 357 322 626 676 680 83 12 241 3 333 -20z" />
            </g>
          </svg>
          <div class="arrow text-center inline-block">
            - - - - - - - - - - - - - - - - -<span class="relative align-middle inline-block left-[-1px]">&#9654;</span>
          </div>
          <span class="text-center">
            Cam. {{truckNumber}}
          </span>
        </div>
        <div>
          <ul>
            <li>
              <h2>
                Destinataire
              </h2>
            </li>
            <li>{{recipientName}}</li>
            <li>{{recipientLocation}}</li>
            {{#if recipientPhone}}
            <li>{{recipientPhone}}</li>
            {{/if}}
            {{#if recipientContact}}
            <li>{{recipientContact}}</li>
            {{/if}}
            {{#each recipientAddressLines}}
            <li>{{this}}</li>
            {{/each}}
          </ul>
        </div>
      </section>

      <hr />
      <section>
        <h2>
          Evoi:
        </h2>
        <div class="grid grid-cols-[75mm_1fr] gap-1 mt-2">
          <!-- Section d'envoi -->
          <div>
            <ul>
              <li>
                <span class="font-bold">Statut:</span>
                <span>{{deliveryStatus}}</span>
              </li>
              <li>
                <span class="font-bold">Date de livraison:</span>
                <span>{{deliveryDate}}</span>
              </li>
              <li>
                <span class="font-bold">Total emb.:</span>
                <span>{{totalPackages}}</span>
              </li>
              <li>
                <span class="font-bold">Poids total:</span>
                <span>{{totalWeight}}</span>
              </li>
              {{#if showTemperatureInfo}}
              <li>
                <span class="font-bold">Température:</span>
                <span class="bg-cyan-500 px-1">{{temperatureInfo}}</span>
              </li>
              {{/if}}
            </ul>
            <ul class="pt-6">
              <li>
                Informations supplémentaires:
              </li>
              {{#if showDeliveryHours}}
              <li class="mt-2 flex flex-row gap-2 items-center">
                <svg version="1.0" xmlns="http://www.w3.org/2000/svg" width="auto" height="5mm"
                  viewBox="0 0 1280.000000 1280.000000" preserveAspectRatio="xMidYMid meet">

                  <g transform="translate(0.000000,1280.000000) scale(0.100000,-0.100000)" fill="#363636" stroke="none">
                    <path
                      d="M6120 12794 c-1455 -74 -2798 -606 -3897 -1547 -244 -209 -599 -575 -805 -832 -802 -999 -1275 -2176 -1395 -3475 -23 -256 -23 -824 0 -1080 125 -1349 619 -2544 1483 -3581 148 -178 545 -580 719 -728 1038 -885 2276 -1405 3640 -1527 233 -21 789 -24 1010 -6 1408 118 2651 635 3715 1546 151 129 472 449 610 606 931 1065 1464 2331 1582 3755 16 197 16 748 0 950 -107 1325 -587 2533 -1416 3560 -247 307 -624 684 -931 931 -1008 813 -2200 1295 -3485 1409 -169 15 -677 26 -830 19z m-1471 -1320 c97 -28 181 -140 181 -240 0 -29 -96 -507 -111 -553 -16 -49 -76 -111 -134 -137 -50 -24 -52 -24 -445 -24 -394 0 -395 0 -446 24 -55 26 -113 86 -131 138 -22 62 -104 499 -104 552 0 117 80 219 194 246 18 5 237 8 488 9 387 1 463 -1 508 -15z m1546 -1015 c27 -12 179 -158 457 -436 398 -400 417 -420 432 -472 20 -68 20 -84 0 -152 -12 -41 -28 -66 -67 -104 -57 -55 -105 -75 -183 -75 -99 0 -116 15 -565 466 -353 356 -417 425 -427 460 -63 222 148 409 353 313z m-1780 -234 c749 -79 1406 -440 1875 -1030 161 -202 331 -508 420 -755 42 -114 120 -402 120 -440 0 -20 11 -20 903 -20 l902 0 50 -24 c56 -26 98 -70 126 -131 14 -30 19 -65 19 -137 l0 -98 508 0 507 0 50 -24 c39 -18 180 -148 622 -575 348 -337 579 -567 590 -589 17 -35 18 -109 21 -1511 1 -994 -1 -1486 -8 -1512 -16 -58 -68 -119 -129 -151 l-53 -28 -348 0 -347 0 -6 -87 c-25 -372 -302 -703 -672 -804 -115 -32 -345 -32 -460 0 -370 101 -647 432 -672 804 l-6 87 -1100 -2 -1101 -3 -3 -55 c-16 -264 -138 -500 -340 -661 -354 -281 -857 -260 -1186 50 -163 154 -264 366 -283 592 l-7 79 -391 0 c-357 0 -394 2 -431 19 -50 22 -99 70 -128 125 -21 40 -22 52 -25 399 l-3 357 2636 0 c2337 0 2639 2 2665 15 82 42 80 179 -4 219 -31 15 -251 16 -2665 16 l-2631 0 0 258 0 257 -81 23 c-204 57 -499 195 -704 330 -592 387 -1017 997 -1169 1672 -53 238 -61 316 -61 610 0 331 19 482 95 752 298 1053 1210 1834 2300 1967 191 24 414 26 605 6z" />
                    <path
                      d="M3900 9723 c-303 -33 -632 -143 -897 -301 -426 -253 -756 -642 -938 -1106 -101 -258 -148 -518 -148 -811 1 -442 121 -845 361 -1210 617 -942 1837 -1274 2847 -775 1093 540 1544 1864 1009 2965 -239 494 -658 883 -1169 1088 -282 113 -522 158 -840 155 -104 -1 -206 -3 -225 -5z m525 -484 c560 -93 1035 -449 1285 -964 102 -209 152 -393 172 -633 34 -398 -82 -815 -320 -1152 -85 -121 -290 -326 -412 -413 -109 -77 -338 -194 -455 -233 -779 -256 -1617 43 -2050 731 -280 446 -345 990 -177 1493 274 818 1103 1314 1957 1171z" />
                    <path
                      d="M4669 8737 c-35 -24 -833 -1051 -870 -1120 -60 -113 -11 -299 103 -388 158 -126 404 -97 512 60 14 20 137 312 272 647 237 588 245 611 241 667 -4 64 -24 98 -77 135 -46 31 -134 31 -181 -1z" />
                    <path
                      d="M9222 5758 l3 -301 510 -133 c281 -74 586 -154 680 -178 127 -34 171 -42 177 -33 4 7 8 222 8 480 l0 467 -690 0 -690 0 2 -302z" />
                    <path
                      d="M5205 3506 c-142 -45 -234 -142 -271 -286 -20 -77 -14 -152 17 -236 31 -83 131 -182 214 -213 200 -76 409 6 498 196 78 168 30 363 -118 475 -90 69 -238 96 -340 64z" />
                    <path
                      d="M9199 3502 c-243 -81 -345 -368 -207 -585 90 -141 274 -208 435 -159 345 105 380 573 55 725 -85 40 -197 47 -283 19z" />
                  </g>
                </svg>
                <span class="font-bold">
                  Horaires de livraison:
                </span>
                <span>
                  {{deliveryHours}}
                </span>
              </li>
              {{/if}}
            </ul>
          </div>

          <!-- zone de signature -->
          <div class="w-full">
            <div class="flex flex-row justify-between w-full  flex-grow-0 flex-shrink-0 gap-2">
              <div class="w-3/4 border-2 border-gray-800 rounded-md h-[25mm] relative ">
                {{#if signatureBase64}}
                <img src="data:image/png;base64,{{signatureBase64}}" alt="Signature"
                  class="w-full h-full object-contain" />
                {{else}}
                Ici placer la signature
                {{/if}}
              </div>
              <div class="display flex flex-col items-center justify-center w-[25mm] h-[30mm] ">
                <div class="w-[25mm] h-[25mm] relative">
                  {{#if showQrCode}}
                  <img src="data:image/png;base64,{{qrCodeBase64}}" alt="QR Code"
                    class="w-full h-full object-contain" />
                  {{/if}}
                </div>
                {{#if showQrCode}}
                {{qrCodeText}}
                {{/if}}
              </div>

            </div>
            <div class="">
              <ul>
                <li>
                  <span class="font-bold">Signature:</span>
                  <span>{{signatureName}}</span>
                </li>
                <li>
                  <span class="font-bold">Date / Heure:</span>
                  <span>{{signatureDateTime}}</span>
                </li>
              </ul>

              {{#if showTimingInfo}}
              <ul class="mt-3 text-sm">
                {{#if arrivalTime}}
                <li>
                  <span class="font-bold">Arrivée:</span>
                  <span>{{arrivalTime}}</span>
                </li>
                {{/if}}
                {{#if waitingTime}}
                <li>
                  <span class="font-bold">Temps d'attente:</span>
                  <span>{{waitingTime}}</span>
                </li>
                {{/if}}
                {{#if departureTime}}
                <li>
                  <span class="font-bold">Départ:</span>
                  <span>{{departureTime}}</span>
                </li>
                {{/if}}
              </ul>
              {{/if}}

              {{#if showClientRemarks}}
              <ul class="text-sm mt-3">
                <li>
                  <span class="font-bold">Remarque client:</span>
                  <span>
                    {{clientRemarks}}
                  </span>
                </li>
              </ul>
              {{/if}}

            </div>
          </div>
        </div>

        <div class="w-full">
          <h3>
            Marchandises:
          </h3>
          <table class="w-full mt-2">
            <thead class="border-b-2 border-gray-800 border-dashed">
              <tr>
                <th class="font-normal text-left">Nombre</th>
                <th class="font-normal text-left">Unité</th>
                <th class="font-normal text-left">Désignation</th>
                <th class="font-normal text-left">Poids</th>
              </tr>
            </thead>
            <tbody>
              {{#each merchandiseItems}}
              <tr class="mt-2">
                <td>{{quantity}}</td>
                <td>{{unit}}</td>
                <td>{{designation}}</td>
                <td>{{weight}}</td>
              </tr>
              {{/each}}
            </tbody>
          </table>

          <hr class="mt-4" />

        </div>

      </section>

    </div>



    <div class="footer flex flex-col">
      <div class="flex justify-between flex-grow footer-text-container pb-2">
        <p class="text-xs">
          Les conditions générales de vente s'appliquent conformément à www.galliker.com/agb.
        </p>
        <p class="text-xs">
          Page {{currentPage}} de {{totalPages}}
        </p>


      </div>
      <div class="footer-image" />

    </div>
  </div>
</body>