#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status.

# Function to check if a database exists
# Returns 0 if exists, 1 if not
database_exists() {
    local db_name=$1
    psql -tAc "SELECT 1 FROM pg_database WHERE datname='$db_name'" | grep -q 1
}

# Create Keycloak database if it doesn't exist
if database_exists "$KEYCLOAK_DB_NAME"; then
    echo "Database $KEYCLOAK_DB_NAME already exists."
else
    echo "Creating database $KEYCLOAK_DB_NAME..."
    psql -v ON_ERROR_STOP=1 --username "$PGUSER" --dbname "$PGDATABASE" <<-EOSQL
        CREATE DATABASE $KEYCLOAK_DB_NAME;
        CREATE USER $KEYCLOAK_DB_USER WITH PASSWORD '$KEYCLOAK_DB_PASSWORD';
        GRANT ALL PRIVILEGES ON DATABASE $KEYCLOAK_DB_NAME TO $KEYCLOAK_DB_USER;
        \c $KEYCLOAK_DB_NAME
        GRANT CREATE, USAGE ON SCHEMA public TO $KEYCLOAK_DB_USER;
EOSQL
    echo "Database $KEYCLOAK_DB_NAME created and schema permissions granted."
fi

# Create Lrg database if it doesn't exist
if database_exists "$LRG_DB_NAME"; then
    echo "Database $LRG_DB_NAME already exists."
else
    echo "Creating database $LRG_DB_NAME..."
    psql -v ON_ERROR_STOP=1 --username "$PGUSER" --dbname "$PGDATABASE" <<-EOSQL
        CREATE DATABASE $LRG_DB_NAME;
        CREATE USER $LRG_DB_USER WITH PASSWORD '$LRG_DB_PASSWORD';
        GRANT ALL PRIVILEGES ON DATABASE $LRG_DB_NAME TO $LRG_DB_USER;
        \c $LRG_DB_NAME
        GRANT CREATE, USAGE ON SCHEMA public TO $LRG_DB_USER;
        CREATE EXTENSION IF NOT EXISTS postgis;
EOSQL
    echo "Database $LRG_DB_NAME created, schema permissions granted, and PostGIS extension enabled."
fi

echo "Database setup complete." 