version: "3.8"

services:
  mailhog:
    image: mailhog/mailhog:latest
    container_name: mailhog_dev
    ports:
      - "4025:1025" # SMTP Port +3000
      - "11025:8025" # Web UI Port +3000

  postgres:
    image: postgis/postgis:16-3.4 # Using PostGIS enabled image
    container_name: postgres_dev
    environment:
      POSTGRES_USER: postgres # Temp admin user for setup
      POSTGRES_PASSWORD: postgres # Temp admin password for setup
      POSTGRES_DB: postgres # Default DB, setup script will create specific ones
    ports:
      - "8432:5432" # PostgreSQL Port +3000
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  setup_db:
    image: postgis/postgis:16-3.4 # Use postgres image for psql client
    container_name: setup_db_dev
    depends_on:
      postgres:
        condition: service_healthy # Wait for postgres to be ready
    environment:
      POSTGRES_USER: postgres # Temp admin user for setup
      POSTGRES_PASSWORD: postgres # Temp admin password for setup
      POSTGRES_DB: postgres # Default DB, setup script will create specific ones
      PGHOST: postgres
      PGUSER: postgres
      PGPASSWORD: postgres
      PGDATABASE: postgres
      # Credentials for the new databases/users
      KEYCLOAK_DB_USER: keycloak_user
      KEYCLOAK_DB_PASSWORD: keycloak_password
      KEYCLOAK_DB_NAME: keycloak_db
      LRG_DB_USER: lrg_user
      LRG_DB_PASSWORD: lrg_password
      LRG_DB_NAME: lrg_db
    volumes:
      - ./scripts/setup-db.sh:/docker-entrypoint-initdb.d/setup-db.sh # Mount setup script
    command:
      [
        "/bin/bash",
        "-c",
        "chmod +x /docker-entrypoint-initdb.d/setup-db.sh && /docker-entrypoint-initdb.d/setup-db.sh",
      ]

    # This container runs the script and exits. We don't need to keep it running.
    # Keycloak depends on this implicitly via the database being ready with credentials.

  keycloak:
    image: quay.io/keycloak/keycloak:latest
    container_name: keycloak_dev
    depends_on:
      postgres:
        condition: service_healthy # Ensure postgres is healthy before starting
      # Setup needs to complete, but docker-compose doesn't have a direct wait for a one-off container like setup_db easily.
      # We rely on Keycloak retrying DB connection or starting after setup_db likely finished.
      # Alternatively, use a wait-for-it script or similar mechanism if startup issues arise.
    environment:
      KC_DB: postgres
      KC_DB_URL_HOST: postgres
      KC_DB_URL_DATABASE: ${KEYCLOAK_DB_NAME:-keycloak_db} # Use env var from setup_db or default
      KC_DB_SCHEMA: public
      KC_DB_USERNAME: ${KEYCLOAK_DB_USER:-keycloak_user} # Use env var from setup_db or default
      KC_DB_PASSWORD: ${KEYCLOAK_DB_PASSWORD:-keycloak_password} # Use env var from setup_db or default
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
      KC_HOSTNAME_STRICT: "false" # Allow access via localhost/different hostnames in dev
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_HTTP_ENABLED: "true" # Enable HTTP for dev
    ports:
      - "11080:8080" # Keycloak Port +3000
    command: start-dev # Use development mode for Keycloak

  minio:
    image: minio/minio:latest
    container_name: minio_dev
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "12000:9000" # API S3
      - "12001:9001" # Console UI
    volumes:
      - minio_data:/data
      - ./scripts/minio-init-bucket.sh:/docker-entrypoint-initdb.d/minio-init-bucket.sh:ro
    entrypoint: ["/bin/sh", "-c", "minio server /data --console-address ':9001' & sleep 5 && /docker-entrypoint-initdb.d/minio-init-bucket.sh && wait"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local
  minio_data:
    driver: local
