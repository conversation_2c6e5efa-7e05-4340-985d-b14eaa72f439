# Product Requirements Document - Tour Assignment System

## Executive Summary

Le système d'assignation de tours permet aux managers de planifier et gérer l'affectation des chauffeurs aux tournées de livraison via une interface de planning visuelle avec calendrier mensuel.

## Business Context

### Problématique Métier
- **Planification manuelle complexe** : Les managers doivent assigner quotidiennement des chauffeurs aux différentes tournées
- **Gestion multi-jours** : Certaines tournées s'étalent sur plusieurs jours consécutifs
- **Visibilité temps réel** : Besoin d'une vue d'ensemble claire de la planification
- **Contraintes temporelles** : Impossible de modifier le passé, règles de validation strictes

### Objectifs Business
1. **Efficacité de planification** : Réduire le temps de planification de 70%
2. **Réduction d'erreurs** : Éliminer les conflits d'assignation
3. **Flexibilité** : Permettre modifications rapides et réaffectations
4. **Traçabilité** : Historique complet des changements

## Core Business Rules

### Règle 1: Identification Unique des Tours
- Un tour est identifié par : `numeroTour + datelivraison`
- Le même numéro de tour peut exister pour différentes dates
- Tours "TK" = tours surgelés, tours normaux sinon

### Règle 2: Contraintes d'Assignation
- **Temporelles** : Aucune assignation sur une date passée
- **Unicité** : Un seul chauffeur par tour par jour
- **Durée** : Assignations peuvent être ponctuelles ou multi-jours
- **Ouvertes** : Assignations sans date de fin (indéfinies)

### Règle 3: Permissions par Rôle
- **Manager/Admin** : Création, modification, suppression complète
- **Chauffeur** : Consultation uniquement de ses propres assignations
- **Anonyme** : Aucun accès

### Règle 4: Gestion Multi-jours
- Une assignation peut couvrir plusieurs jours consécutifs
- Visualisation sous forme de "span" unifié sur le calendrier
- Modification/suppression affecte l'ensemble de la période

## User Stories

### US1: Créer une Assignation Simple
**En tant que** Manager  
**Je veux** assigner un chauffeur à une tournée pour une date donnée  
**Afin de** couvrir les besoins de livraison  

**Critères d'acceptation :**
- Sélection du tour depuis la liste disponible
- Choix du chauffeur depuis dropdown
- Sélection de la date depuis calendrier
- Assignation visible immédiatement
- Validation : pas de date passée, chauffeur existe

### US2: Créer une Assignation Multi-jours
**En tant que** Manager  
**Je veux** assigner un chauffeur à une tournée pour plusieurs jours consécutifs  
**Afin de** maintenir la continuité sur les longs parcours  

**Critères d'acceptation :**
- Sélection date début et fin
- Visualisation span unifié sur calendrier
- Durée affichée (ex: "5 jours")
- Modification/suppression du span complet

### US3: Planification par Glisser-Déposer
**En tant que** Manager  
**Je veux** glisser les chauffeurs depuis la liste vers les créneaux de tournée  
**Afin de** créer rapidement des assignations  

**Critères d'acceptation :**
- Drag depuis section "Chauffeurs disponibles"
- Drop sur cellule tour-date spécifique
- Feedback visuel pendant l'opération
- Blocage sur cibles invalides (dates passées)

### US4: Déplacer une Assignation
**En tant que** Manager  
**Je veux** déplacer une assignation existante vers un autre créneau  
**Afin de** réajuster la planification selon les changements  

**Critères d'acceptation :**
- Drag de la cellule assignation existante
- Drop vers nouvelle position (tour/date différents)
- Suppression automatique ancienne position
- Conservation des notes d'assignation

### US5: Modifier la Durée d'Assignation
**En tant que** Manager  
**Je veux** étendre ou raccourcir les assignations multi-jours  
**Afin de** m'adapter aux exigences changeantes  

**Critères d'acceptation :**
- Modal d'édition avec dates début/fin modifiables
- Mise à jour visuelle immédiate du span
- Validation nouvelle plage de dates
- Impossible de modifier les portions passées

### US6: Ajouter des Notes d'Assignation
**En tant que** Manager  
**Je veux** ajouter des notes aux assignations  
**Afin de** communiquer instructions spéciales ou contexte  

**Critères d'acceptation :**
- Champ texte dans modal d'assignation
- Persistence avec l'enregistrement
- Visibilité dans tooltips ou vue étendue
- Limite de caractères (500 max)

### US7: Supprimer une Assignation
**En tant que** Manager  
**Je veux** supprimer les assignations devenues inutiles  
**Afin de** maintenir un planning précis  

**Critères d'acceptation :**
- Action supprimer via clic-droit ou bouton
- Dialog de confirmation
- Suppression immédiate de la grille
- Blocage sur assignations passées

### US8: Vue Planning Calendaire
**En tant que** Manager  
**Je veux** voir une vue calendaire mensuelle de toutes les assignations  
**Afin de** comprendre l'allocation des ressources en un coup d'œil  

**Critères d'acceptation :**
- Navigation mois précédent/suivant
- Tours listés verticalement, dates horizontalement
- Assignations affichées en cellules colorées
- Couleurs distinctes par chauffeur (auto-assignées)
- Mise en évidence date du jour
- Distinction visuelle week-ends/jours fériés

### US9: Filtrage et Recherche
**En tant que** Manager  
**Je veux** filtrer les assignations par chauffeur, tour ou plage de dates  
**Afin de** me concentrer sur des besoins de planification spécifiques  

**Critères d'acceptation :**
- Filtre dropdown chauffeurs
- Recherche numéro de tour
- Sélecteur plage de dates
- Combinaison de filtres
- Option effacer tous filtres

## Business Logic

### Validation d'Assignation
```
SI date < aujourd'hui ALORS
  REJETER "Impossible d'assigner sur une date passée"

SI chauffeur déjà assigné sur ce tour à cette date ALORS
  REJETER "Chauffeur déjà assigné sur cette tournée"

SI chauffeur n'existe pas ALORS
  REJETER "Chauffeur inexistant"

SI tour n'existe pas ALORS
  REJETER "Tournée inexistante"
```

### Calcul de Spans Multi-jours
```
POUR chaque ressource:
  assignations = OBTENIR_ASSIGNATIONS(ressource, mois)
  spans = []
  
  POUR chaque jour du mois:
    SI assignation existe ET jour non traité ALORS
      span = CALCULER_SPAN_CONSECUTIF(jour, assignation.chauffeur)
      spans.AJOUTER(span)
      MARQUER_JOURS_TRAITÉS(span.jours)
      
  RETOURNER spans
```

### Gestion des Conflits
- **Assignation simultanée** : Last-write-wins
- **Tour supprimé** : Assignations orphelines affichées avec warning
- **Chauffeur désactivé** : Assignations existantes maintenues, nouvelles bloquées

## Contraintes Business

### Contraintes Temporelles
- **Horizon planning** : 12 mois dans le futur maximum
- **Historique** : Conservation 5 ans minimum
- **Modifications passé** : Strictement interdites
- **Assignations ouvertes** : Maximum 1 an

### Contraintes Utilisateurs
- **Charge chauffeur** : Pas de limite multiple tours/jour
- **Disponibilité** : Pas de gestion congés intégrée (externe)
- **Qualification** : Pas de validation compétences tours

### Contraintes Opérationnelles
- **Import quotidien** : Tours importés depuis XML chaque jour
- **Cohérence données** : Assignations peuvent référencer tours futurs
- **Performance** : Chargement planning < 2 secondes
- **Concurrence** : 50 managers simultanés supportés

## Success Metrics

### KPIs Opérationnels
- **Temps de planification** : < 5 minutes pour planning journalier complet
- **Taux d'erreur** : < 1% conflits d'assignation
- **Adoption** : 95% managers utilisent l'outil vs méthodes manuelles
- **Modifications** : < 10% assignations modifiées après création

### KPIs Techniques
- **Disponibilité** : 99.5% uptime
- **Performance** : 95% requêtes < 500ms
- **Concurrence** : Support 50 utilisateurs simultanés
- **Données** : 0 perte de données assignation

## Out of Scope

### Fonctionnalités Exclues V1
- ❌ Gestion automatique des congés chauffeurs
- ❌ Optimisation automatique des tournées
- ❌ Intégration GPS temps réel
- ❌ Notifications push mobile
- ❌ Workflow validation assignations
- ❌ Reporting avancé et KPIs
- ❌ Gestion des remplacements automatiques
- ❌ Interface mobile native

### Intégrations Non Incluses
- ❌ Système RH pour congés/disponibilités
- ❌ Système WMS pour optimisation routes
- ❌ Portail client pour visibilité livraisons
- ❌ Système comptable pour facturation

## Dependencies

### Systèmes Externes Requis
- **Keycloak** : Authentification et gestion rôles
- **Import XML** : Processus quotidien import tours
- **Base utilisateurs** : Annuaire chauffeurs actifs

### Prérequis Fonctionnels
- Tours créés quotidiennement via import XML
- Utilisateurs avec rôles appropriés configurés
- Calendrier jours fériés maintenu à jour